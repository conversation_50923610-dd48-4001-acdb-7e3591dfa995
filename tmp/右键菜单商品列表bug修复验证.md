# 右键菜单商品列表Bug修复验证

## Bug描述

**问题**: 当用户手动选择商品后，原始识别的商品会从右键菜单的可选列表中消失。

**具体场景**:
1. 整个视频中只有一个片段识别出商品A
2. 用户右键该片段，手动选择了商品B  
3. 商品A从右键菜单的可选列表中消失，无法再次选择

## 修复方案

### 原始实现问题
```javascript
// 原始代码只收集当前显示的商品
segments.forEach(seg => {
  if (seg.status === 'completed' && seg.itemId) {
    // 只添加当前的 itemId，忽略了原始识别结果
    allProducts.set(seg.itemId, { ... });
  }
});
```

### 修复后的实现
```javascript
segments.forEach(seg => {
  if (seg.status === 'completed') {
    // 1. 添加当前显示的商品（可能是手动选择的）
    if (seg.itemId) {
      allProducts.set(seg.itemId, { ... });
    }
    
    // 2. 添加原始识别结果（如果被手动替换了）
    if (seg.originalItemId && seg.originalItemId !== seg.itemId) {
      allProducts.set(seg.originalItemId, { ... });
    }
    
    // 3. 添加所有识别结果
    if (seg.allRecognitionResults) {
      seg.allRecognitionResults.forEach(result => {
        allProducts.set(result.itemId, { ... });
      });
    }
  }
});
```

## 修复内容详解

### 1. 数据收集逻辑增强
- **当前商品**: 收集`seg.itemId`（当前时间轴显示的商品）
- **原始商品**: 收集`seg.originalItemId`（被手动替换前的原始识别结果）
- **历史结果**: 收集`seg.allRecognitionResults`中的所有商品

### 2. 去重处理
- 使用`Map`数据结构自动去重
- 相同`itemId`的商品只保留一份记录
- 确保商品信息的一致性

### 3. 条件判断优化
- 检查`seg.originalItemId !== seg.itemId`避免重复添加
- 验证数据存在性避免空值错误
- 保持向后兼容性

## 测试验证步骤

### 测试场景1: 基础功能验证
1. **准备**: 启动视频识别，等待识别完成
2. **操作**: 在任意已识别片段右键点击
3. **验证**: 确认菜单显示所有已识别的商品
4. **预期**: 所有商品都应该出现在列表中

### 测试场景2: 手动选择后的保留验证
1. **准备**: 完成场景1的操作
2. **操作**: 
   - 选择一个与当前不同的商品（比如从商品A改为商品B）
   - 再次右键点击该片段
3. **验证**: 确认菜单中同时包含商品A和商品B
4. **预期**: 原始识别的商品A应该仍然在可选列表中

### 测试场景3: 多次手动选择验证
1. **准备**: 完成场景2的操作
2. **操作**:
   - 再次手动选择另一个商品（比如选择商品C）
   - 右键点击查看菜单
3. **验证**: 确认菜单包含所有历史出现过的商品
4. **预期**: 商品A、B、C都应该在可选列表中

### 测试场景4: 置信度显示验证
1. **准备**: 完成上述任一场景
2. **操作**: 查看右键菜单中各商品的置信度显示
3. **验证**: 
   - 当前片段识别结果中的商品应显示置信度
   - 不在当前片段识别结果中的商品不应显示置信度
4. **预期**: 置信度显示逻辑应保持正确

### 测试场景5: 跨片段验证
1. **准备**: 多个片段完成识别，包含不同商品
2. **操作**: 在不同片段进行手动选择操作
3. **验证**: 在任意片段右键查看可选商品列表
4. **预期**: 应包含所有片段中出现过的所有商品

## 边界情况测试

### 情况1: 空识别结果
- **场景**: 某个片段识别失败，没有识别结果
- **验证**: 右键菜单应正常显示其他片段的识别结果
- **预期**: 不应影响整体功能

### 情况2: 单一商品视频
- **场景**: 整个视频只识别出一个商品
- **验证**: 手动选择后，原商品应仍在列表中
- **预期**: 至少包含原识别商品和手动选择商品

### 情况3: 重复商品名称
- **场景**: 不同itemId但相同itemName的商品
- **验证**: 应正确区分和显示
- **预期**: 每个唯一的itemId都应该出现

## 回归测试检查点

### 功能完整性
- [ ] 右键菜单正常显示
- [ ] 商品选择功能正常
- [ ] 颜色指示器正确显示
- [ ] 置信度显示逻辑正确

### 视觉效果
- [ ] 菜单布局美观
- [ ] hover效果正常
- [ ] 颜色对比度良好
- [ ] 文本显示完整

### 性能表现
- [ ] 菜单打开速度正常
- [ ] 大量商品时性能良好
- [ ] 内存使用合理
- [ ] 无明显卡顿

## 预期修复效果

修复后，用户将获得以下改进体验：

1. **完整的商品历史**: 所有曾经识别过的商品都保留在可选列表中
2. **灵活的选择**: 可以在原始识别结果和手动选择之间自由切换
3. **一致的体验**: 不会因为手动操作而丢失历史信息
4. **智能去重**: 相同商品不会重复显示，保持列表简洁

这个修复确保了右键菜单功能的完整性和用户体验的连续性。

## 修复前后对比示例

### 示例场景
假设视频中有一个片段，原始识别结果为：
- 商品A (置信度: 80%)
- 商品B (置信度: 60%)
- 商品C (置信度: 40%)

用户手动选择了商品D。

### 修复前的行为
```
片段状态:
- itemId: "D" (手动选择)
- originalItemId: "A"
- allRecognitionResults: [A, B, C]

右键菜单显示: [D] ← 只显示当前商品，A、B、C消失
```

### 修复后的行为
```
片段状态:
- itemId: "D" (手动选择)
- originalItemId: "A"
- allRecognitionResults: [A, B, C]

右键菜单显示: [A, B, C, D] ← 显示所有历史商品
置信度显示:
- A: 80% (来自allRecognitionResults)
- B: 60% (来自allRecognitionResults)
- C: 40% (来自allRecognitionResults)
- D: 无置信度 (不在当前片段识别结果中)
```

这样用户就可以在所有历史识别过的商品中自由选择，不会因为手动操作而丢失选项。
