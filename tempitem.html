<!doctype html>
<html>

<head pia-version="2">
    <script>window.gfdatav1 = { "env": "prod", "envName": "prod", "runtime": "node", "ver": "1.0.0.4175", "canary": 0, "extra": { "canaryType": null }, "idc": "lf", "region": "cn", "vdc": "lf", "vregion": "China-North" }</script>
    <script pia-manifest
        type="application/json">{"version":"1.0.0.4175","prefetch":{"url":"https://lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/index.45e4d978.pia.worker.js","name":"Prefetch"},"support":["log2"]}</script>
    <title></title>
    <script
        pia-monitor-impl>"use strict"; window.__PIA_MONITOR__ = { _mark: function (t, _) { }, _setAttribute: function (t, _) { }, _setMetrics: function (t, _) { } }</script>
    <link as="script" rel="preload"
        href="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/index.45e4d978.pia.worker.js">
    <script pia-meta
        type="application/json">{"app":{"connectionTimeout":1000,"jsbAuthTimeout":200,"diagnostic":{},"enableWorkerLog":false,"buildType":"online"},"currentPage":{"hasPrefetch":true,"pageName":"index","worker":{"url":"//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/index.45e4d978.pia.worker.js","globalName":"__pia_worker_index__"}}}</script>
    <script
        pia-scheduler>!function () { function n(n) { return void 0 === n && (n = window), n.__PIA__ } var e = function () { function n() { var n = this; this.preRuntimeMetricsMeta = {}, this.nsrSymbols = {}, this.ssrSymbols = {}, this._renderingRuntimeInfo = new Promise((function (e) { n._resolveRenderingRuntimeInfo = e })) } return n.prototype.getRenderingRuntimeInfo = function () { return this._renderingRuntimeInfo }, n.prototype.setRenderingRuntimeInfo = function (n) { this._resolveRenderingRuntimeInfo(n) }, n.prototype.setLauncher = function (n) { this.launcher = n }, n.prototype.getLauncher = function () { return this.launcher }, n }(); (function (n, e) { if (void 0 === e && (e = !1), n.__PIA__) throw new Error("Multiple runtime detected, which would cause data confusion."); var t = { register: function (n, i, r) { Object.defineProperty(t, n, { writable: r || e, value: i }) } }; n.__PIA__ = t })(window), n(window).register("piaKitVersion", "2.18.0"); var t = new e; n(window).register("scheduler", t) }()</script>
    <script>window.__remew_performance_data = { T_HTML_START: (new Date).getTime() }</script>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta http-equiv="x-dns-prefetch-control" content="on">
    <meta name="format-detection" content="telephone=no,address=no,email=no">
    <link href="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/css/index.bd5f25ed.css"
        rel="stylesheet">
    <script>    !function () { var e = "1522", t = "33666"; function n() { try { var n = "gfkadpd", o = e + "," + t, i = function (e) { for (var t = document.cookie.split(";"), n = 0; n < t.length; n++) { var o = t[n].trim(); if (o.startsWith(e + "=")) return o.substring(e.length + 1) } return null }(n); if (i) { if (-1 != i.indexOf(o)) return; o += "|" + i } document.cookie = n + "=" + o + "; expires=" + new Date((new Date).getTime() + 3 * 24 * 60 * 60 * 1e3).toUTCString() + "; path=/; SameSite=None; Secure;" } catch (e) { } } var o = function (n, o, i, a) { if (Math.ceil(100 * Math.random()) <= 100 * o) { var r = { ev_type: "batch", list: [{ ev_type: "custom", payload: { name: "sdk_glue_load", type: "event", metrics: {}, categories: { sdk_glue_load_status: n, sdk_glue_load_err_src: i, payload_bdms_aid: e, payload_bdms_page_id: t, duration: a } }, common: { context: { ctx_bdms_aid: e, ctx_bdms_page_id: t }, bid: "web_bdms_cn", pid: window.location.pathname, view_id: "/_1", user_id: "", session_id: "0-a-1-2-c", release: "", env: "production", url: window.location.href, timestamp: +new Date, sdk_version: "1.6.1", sdk_name: "SDK_SLARDAR_WEB" } }] }, d = new XMLHttpRequest; d.open("POST", "https://mon.zijieapi.com/monitor_browser/collect/batch/?biz_id=web_bdms_cn", !0), d.setRequestHeader("Content-type", "application/json"), d.send(JSON.stringify(r)) } }; !function () { try { n(), document.cookie = "wdglgl=; expires=Mon, 20 Sep 2010 00:00:00 UTC; path=/;", o("before_load", .001, "", "") } catch (e) { } var e = performance.now(); window.addEventListener("error", (function (t) { try { var n = t.target || t.srcElement; if (n instanceof HTMLElement && "SCRIPT" == n.nodeName) if (-1 != (n.src || "").indexOf("sdk-glue")) { var i = (performance.now() - e).toFixed(2); a = i, document.cookie = "wdglgl=; expires=Mon, 20 Sep 2010 00:00:00 UTC; path=/;", document.cookie = "wdglgl=" + a + "; expires=" + new Date((new Date).getTime() + 3 * 24 * 60 * 60 * 1e3).toUTCString() + "; path=/; SameSite=None; Secure;", o("load_error", 1, n.src, i) } } catch (e) { } var a }), !0), window.__glue_t = +new Date }() }();</script>
    <script src="https://lf-c-flwb.bytetos.com/obj/rc-client-security/web/glue/********/sdk-glue.js"></script>
    <script>; (function () { var sdkInfo = { csrf: { init: function (options) { window.secsdk.csrf.setOptions(options) }, isLoaded: function () { return !!window.secsdk }, srcList: ["https://lf1-cdn-tos.bytegoofy.com/obj/goofy/secsdk/secsdk-lastest.umd.js", "https://lf3-cdn-tos.bytegoofy.com/obj/goofy/secsdk/secsdk-lastest.umd.js", "https://lf6-cdn-tos.bytegoofy.com/obj/goofy/secsdk/secsdk-lastest.umd.js"], }, bdms: { init: function (options) { window.bdms.init(options) }, isLoaded: function () { return !!window.bdms }, srcList: ["https://lf-c-flwb.bytetos.com/obj/rc-client-security/web/stable/********/bdms.js", "https://lf-headquarters-speed.yhgfb-cn-static.com/obj/rc-client-security/web/stable/********/bdms.js"], }, verifyCenter: { init: function (options) { window.TTGCaptcha.init(options) }, isLoaded: function () { return !!window.TTGCaptcha }, srcList: ["https://lf-rc1.yhgfb-cn-static.com/obj/rc-verifycenter/sec_sdk_build/4.0.10/captcha/index.js", "https://lf-rc2.yhgfb-cn-static.com/obj/rc-verifycenter/sec_sdk_build/4.0.10/captcha/index.js"], } }; if (window._SdkGlueInit) { window._SdkGlueInit({ bdms: { "aid": 1522, "ddrt": 3, "pageId": 33666, "paths": { "include": ["/aweme/v2", "/passport"] } }, self: { aid: 1522, pageId: 33666, }, accountSDK: { 'douyinLoginVersion': '1.3.3-beta.0' } }, sdkInfo); } })()</script>
</head>

<body>
    <div id="root"></div>
    <script
        pia-monitor-quick-report>!function () { var e = { 229: function () { var e, i, t = "__PIA_MONITOR__", n = "use_pia_nsr", r = "use_pia_ssr", o = "pia_ssr_mode", s = "use_pia_snapshot"; if (window[t]._setAttribute({ version: (e = window, void 0 === e && (e = window), e.__PIA__).piaKitVersion }, "pia.kit"), window[t]._setAttribute({ enable: !!window[r] }, "pia.ssr"), window[t]._setAttribute({ enable: !!window[n] }, "pia.nsr"), window[t]._setAttribute({ enable: !!window[s] }, "pia.snapshot"), window[r] && (window[t]._setAttribute({ mode: window[o], hit: !!document.getElementById("__PIA_DATA__") || document.getElementsByName("pia-ssr-data").length > 0 }, "pia.ssr"), "string" === window[o] && performance.getEntriesByType && (performance.getEntriesByType("navigation").forEach((function (e) { var t, n = e.serverTiming; if (n) { var r = null === (t = n.find((function (e) { return e.name.startsWith("bd-pia-ssr-prefetch") }))) || void 0 === t ? void 0 : t.duration; r && (i = r) } })), window[t]._setMetrics("pia.rendering.prefetch.duration", i))), window[n]) { window[t]._setAttribute({ hit: !!window.pia_nsr_enable }, "pia.nsr"); var a = document.querySelector("#__NSR_METRIC__"); if (a) { var w = JSON.parse(a.innerText).prefetch; window[t]._setMetrics("pia.rendering.prefetch.duration", w) } } window[s] && window[t]._setAttribute({ hit: !!window.pia_snapshot_enable }, "pia.snapshot") } }, i = {}; function t(n) { var r = i[n]; if (void 0 !== r) return r.exports; var o = i[n] = { exports: {} }; return e[n](o, o.exports, t), o.exports } !function () { "use strict"; t(229) }() }()</script>
    <script
        src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/pia-runtime.6667ce05.js"></script>
    <script>function setRootPixel() { function n() { var n = document.documentElement, i = 50 * (window.innerWidth && n.clientWidth ? Math.min(window.innerWidth, n.clientWidth) : window.innerWidth || n.clientWidth || document.body && document.body.clientWidth || 375) / 375; i = Math.min(i, 64), window.ROOT_FONT_SIZE = i, n.style.fontSize = i + "px" } function i(i) { i ? n() : setTimeout(n, 30) } i(!0), window.addEventListener("resize", i, !1), "onorientationchange" in window && window.addEventListener("orientationchange", i, !1) } "undefined" != typeof window && setRootPixel()</script>
    <script>!function () { "use strict"; var e, t, n, r, o, i = {}, u = {}; function c(e) { var t = u[e]; if (void 0 !== t) return t.exports; var n = u[e] = { exports: {} }; return i[e].call(n.exports, n, n.exports, c), n.exports } c.m = i, e = [], c.O = function (t, n, r, o) { if (!n) { var i = 1 / 0; for (l = 0; l < e.length; l++) { n = e[l][0], r = e[l][1], o = e[l][2]; for (var u = !0, a = 0; a < n.length; a++)(!1 & o || i >= o) && Object.keys(c.O).every((function (e) { return c.O[e](n[a]) })) ? n.splice(a--, 1) : (u = !1, o < i && (i = o)); if (u) { e.splice(l--, 1); var f = r(); void 0 !== f && (t = f) } } return t } o = o || 0; for (var l = e.length; l > 0 && e[l - 1][2] > o; l--)e[l] = e[l - 1]; e[l] = [n, r, o] }, c.n = function (e) { var t = e && e.__esModule ? function () { return e.default } : function () { return e }; return c.d(t, { a: t }), t }, n = Object.getPrototypeOf ? function (e) { return Object.getPrototypeOf(e) } : function (e) { return e.__proto__ }, c.t = function (e, r) { if (1 & r && (e = this(e)), 8 & r) return e; if ("object" == typeof e && e) { if (4 & r && e.__esModule) return e; if (16 & r && "function" == typeof e.then) return e } var o = Object.create(null); c.r(o); var i = {}; t = t || [null, n({}), n([]), n(n)]; for (var u = 2 & r && e; "object" == typeof u && !~t.indexOf(u); u = n(u))Object.getOwnPropertyNames(u).forEach((function (t) { i[t] = function () { return e[t] } })); return i.default = function () { return e }, c.d(o, i), o }, c.d = function (e, t) { for (var n in t) c.o(t, n) && !c.o(e, n) && Object.defineProperty(e, n, { enumerable: !0, get: t[n] }) }, c.f = {}, c.e = function (e) { return Promise.all(Object.keys(c.f).reduce((function (t, n) { return c.f[n](e, t), t }), [])) }, c.u = function (e) { return "resource/js/async/" + e + "." + { 47: "f2ad7c4a", 204: "08333c16" }[e] + ".js" }, c.miniCssF = function (e) { return "resource/css/async/" + e + ".adc346d9.css" }, c.g = function () { if ("object" == typeof globalThis) return globalThis; try { return this || new Function("return this")() } catch (e) { if ("object" == typeof window) return window } }(), c.o = function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, r = {}, o = "detail:", c.l = function (e, t, n, i) { if (r[e]) r[e].push(t); else { var u, a; if (void 0 !== n) for (var f = document.getElementsByTagName("script"), l = 0; l < f.length; l++) { var s = f[l]; if (s.getAttribute("src") == e || s.getAttribute("data-webpack") == o + n) { u = s; break } } u || (a = !0, (u = document.createElement("script")).charset = "utf-8", u.timeout = 120, c.nc && u.setAttribute("nonce", c.nc), u.setAttribute("data-webpack", o + n), u.src = e), r[e] = [t]; var d = function (t, n) { u.onerror = u.onload = null, clearTimeout(p); var o = r[e]; if (delete r[e], u.parentNode && u.parentNode.removeChild(u), o && o.forEach((function (e) { return e(n) })), t) return t(n) }, p = setTimeout(d.bind(null, void 0, { type: "timeout", target: u }), 12e4); u.onerror = d.bind(null, u.onerror), u.onload = d.bind(null, u.onload), a && document.head.appendChild(u) } }, c.r = function (e) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) }, c.p = "//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/", function () { if ("undefined" != typeof document) { var e = { 691: 0 }; c.f.miniCss = function (t, n) { e[t] ? n.push(e[t]) : 0 !== e[t] && { 204: 1 }[t] && n.push(e[t] = function (e) { return new Promise((function (t, n) { var r = c.miniCssF(e), o = c.p + r; if (function (e, t) { for (var n = document.getElementsByTagName("link"), r = 0; r < n.length; r++) { var o = (u = n[r]).getAttribute("data-href") || u.getAttribute("href"); if ("stylesheet" === u.rel && (o === e || o === t)) return u } var i = document.getElementsByTagName("style"); for (r = 0; r < i.length; r++) { var u; if ((o = (u = i[r]).getAttribute("data-href")) === e || o === t) return u } }(r, o)) return t(); !function (e, t, n, r, o) { var i = document.createElement("link"); i.rel = "stylesheet", i.type = "text/css", c.nc && (i.nonce = c.nc), i.onerror = i.onload = function (n) { if (i.onerror = i.onload = null, "load" === n.type) r(); else { var u = n && n.type, c = n && n.target && n.target.href || t, a = new Error("Loading CSS chunk " + e + " failed.\n(" + u + ": " + c + ")"); a.name = "ChunkLoadError", a.code = "CSS_CHUNK_LOAD_FAILED", a.type = u, a.request = c, i.parentNode && i.parentNode.removeChild(i), o(a) } }, i.href = t, document.head.appendChild(i) }(e, o, 0, t, n) })) }(t).then((function () { e[t] = 0 }), (function (n) { throw delete e[t], n }))) } } }(), function () { var e = { 691: 0 }; c.f.j = function (t, n) { var r = c.o(e, t) ? e[t] : void 0; if (0 !== r) if (r) n.push(r[2]); else if (691 != t) { var o = new Promise((function (n, o) { r = e[t] = [n, o] })); n.push(r[2] = o); var i = c.p + c.u(t), u = new Error; c.l(i, (function (n) { if (c.o(e, t) && (0 !== (r = e[t]) && (e[t] = void 0), r)) { var o = n && ("load" === n.type ? "missing" : n.type), i = n && n.target && n.target.src; u.message = "Loading chunk " + t + " failed.\n(" + o + ": " + i + ")", u.name = "ChunkLoadError", u.type = o, u.request = i, r[1](u) } }), "chunk-" + t, t) } else e[t] = 0 }, c.O.j = function (t) { return 0 === e[t] }; var t = function (t, n) { var r, o, i = n[0], u = n[1], a = n[2], f = 0; if (i.some((function (t) { return 0 !== e[t] }))) { for (r in u) c.o(u, r) && (c.m[r] = u[r]); if (a) var l = a(c) } for (t && t(n); f < i.length; f++)o = i[f], c.o(e, o) && e[o] && e[o][0](), e[o] = 0; return c.O(l) }, n = self.webpackChunkdetail = self.webpackChunkdetail || []; n.forEach(t.bind(null, 0)), n.push = t.bind(null, n.push.bind(n)) }() }()</script>
    <script
        src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/lib-polyfill.8a9848ba.js"></script>
    <script src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/lib-axios.0ab086b5.js"></script>
    <script src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/lib-react.ad11d358.js"></script>
    <script
        src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/vendors-30cbd1ad.eaa9ef46.js"></script>
    <script
        src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/vendors-cd9b1e8b.9e177f00.js"></script>
    <script
        src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/vendors-1c156ad4.df556a2a.js"></script>
    <script
        src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/vendors-f3956634.8c308634.js"></script>
    <script src="//lf3-ecom-toc.jinritemai.com/obj/ecom-toc/ecommerce/gecko/resource/js/index.73096ca6.js"></script>
</body>

</html>