package com.fastclip.llm.ask;


public interface Ask {
    /**
     * 作品名称
     * @return
     */
    public String askWorksName(String itemName);

    /**
     * 作品描述
     * @return
     */
    public String askWorksDesc(String itemName);

    /**
     * 作品标签
     * @return
     */
    String askWorksTag(String itemName);

    /**
     * 作品标签
     * @return
     */
    public String askItemName(String itemName);

    /**
     * 商品特性
     * @return
     */
    String askItemFeature(String itemName);

    /**
     * 商品标签
     * @return
     */
    public String askItemTags(String itemName);
    public String askKeywords(String content);
}
