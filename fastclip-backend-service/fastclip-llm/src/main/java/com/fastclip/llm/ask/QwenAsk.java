package com.fastclip.llm.ask;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Semaphore;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.ResultCallback;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.utils.Constants;
import com.alibaba.dashscope.utils.JsonUtils;
import io.reactivex.Flowable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class QwenAsk implements Ask{

    @Value("${tongyi.systemSrtQ}")
    String systemQuestion;

    @Value("${tongyi.systemItemDescQ}")
    String systemItemDescQ;

    @Value("${tongyi.appKey}")
    String appKey;

    private final Logger logger = LoggerFactory.getLogger(QwenAsk.class);

    private void handleGenerationResult(GenerationResult message, StringBuilder fullContent) {
        fullContent.append(message.getOutput().getChoices().get(0).getMessage().getContent());
        logger.info("Received message: {}", JsonUtils.toJson(message));
    }

    public String streamCallWithMessage(Generation gen, List<Message> userMsgs)
            throws NoApiKeyException, ApiException, InputRequiredException {
        GenerationParam param = buildGenerationParam(userMsgs);
        Flowable<GenerationResult> result = gen.streamCall(param);
        StringBuilder fullContent = new StringBuilder();
        result.blockingForEach(message -> handleGenerationResult(message, fullContent));
        return fullContent.toString();
    }

    public void streamCallWithCallback(Generation gen, List<Message> userMsgs)
            throws NoApiKeyException, ApiException, InputRequiredException, InterruptedException {
        GenerationParam param = buildGenerationParam(userMsgs);
        Semaphore semaphore = new Semaphore(0);
        StringBuilder fullContent = new StringBuilder();


        gen.streamCall(param, new ResultCallback<GenerationResult>() {
            @Override
            public void onEvent(GenerationResult message) {
                handleGenerationResult(message, fullContent);
            }

            @Override
            public void onError(Exception err) {
                logger.error("Exception occurred: {}", err.getMessage());
                semaphore.release();
            }

            @Override
            public void onComplete() {
                logger.info("Completed");
                semaphore.release();
            }
        });

        semaphore.acquire();
        logger.info("Full content: \n{}", fullContent.toString());
    }

    private static GenerationParam buildGenerationParam(List<Message> userMsgs) {
        return GenerationParam.builder()
                .model("qwen-max-longcontext")
                .messages(userMsgs)
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .topP(0.8)
                .incrementalOutput(true)
                .build();
    }

    public String askWithDefaultSystemQ(String strPath) {
        return askSrtSummary(systemQuestion, strPath);
    }

    public String askSrtSummary(String systemQuestion, String strPath) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            BufferedReader bufferedReader = new BufferedReader(new FileReader(strPath));
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line + "\n");
            }
        }catch (Exception e) {
            log.error("read srt error", e);
        }
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(stringBuilder.toString()).build();
        Message userMsg2 = null;
        if (systemQuestion != null) {
            userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).content(systemQuestion).build();
        } else {
            userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).content(this.systemQuestion).build();
        }
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);

    }


    public String askItemDesc(String itemName) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(itemName).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).content(systemItemDescQ).build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    public String ask(List<Message> userMsgs) {
        try {
            Constants.apiKey = "sk-950f2e5232614db09cdd94b4a4e26ae3";
            Generation gen = new Generation();
            return streamCallWithMessage(gen, userMsgs);
        } catch (Exception e) {
            log.error("qwen error", e);
        }
        return null;
    }

    @Override
    public String askWorksName(String itemName) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(itemName).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).
                content("请根据用户提交的商品全称，提炼出商品简称，不要出现材质相关的内容，不要出现英文，尽可能关联相关的热门标签，24个字左右，只输出名称，其它不要输出。").build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    @Override
    public String askWorksDesc(String itemName) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(itemName).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).
                content("请根据用户提交问题，给出一段描述，只要文字不要特殊符号，800字以内。").build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    @Override
    public String askWorksTag(String itemName) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(itemName).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).
                content("请根据用户提交的商品全称，提炼出商品种类，只输出4个字以内的商品种类名称，其它不要输出。").build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    @Override
    public String askItemName(String itemName) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(itemName).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).
                content("请根据用户提交的商品全称，提炼出商品简称,20个字以内，只保留中文，不要英文和数字，只输出名称，其它不要输出。").build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    @Override
    public String askItemFeature(String itemName) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(itemName).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).
                content("请根据用户提交的商品全称，提炼出商品的特性,4个字，只保留中文，不要英文和数字，只输出商品特性，其它不要输出。").build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    @Override
    public String askItemTags(String itemName) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(itemName).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).
                content("请根据用户提交的商品全称，提炼出三个商品标签，只保留中文，不要数字，只输出标签名称，用#号开头，空格隔开，其它不要输出。").build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    @Override
    public String askKeywords(String content) {
        List<Message> userMsgs = new ArrayList<>();
        Message userMsg1 = Message.builder().role(Role.USER.getValue()).content(content).build();
        Message userMsg2 = Message.builder().role(Role.SYSTEM.getValue()).
                content("请根据用户提交的字幕，提取出关键词，关键词不能重复，只保留中文，不要英文和数字，只输出关键词，每个关键词用逗号,隔开，其它不要输出。").build();
        userMsgs.add(userMsg1);
        userMsgs.add(userMsg2);
        return ask(userMsgs);
    }

    public static void main(String[] args) {
        String itemName = new QwenAsk().askWorksTag("AMAZING IN | 1oo%棉 | 时尚百搭袖口压褶T恤女 A241121-JSY-7185");
        System.out.println(itemName);
    }
}
