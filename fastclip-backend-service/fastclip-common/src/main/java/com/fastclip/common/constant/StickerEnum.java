package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 贴纸类型
 */
@AllArgsConstructor
@Getter
public enum StickerEnum {
    Watermark1("watermark1","爱心+超美"),
    Watermark2("watermark2","点击购买+漂亮"),
    Watermark3("watermark3", "点击购买+好看");

    private String code;
    private String value;


    public static String WatermarkEnum(String code) {
        for (StickerEnum watermarkEnum : StickerEnum.values()) {
            if (watermarkEnum.getCode().equals(code)) {
                return watermarkEnum.value;
            }
        }
        return null;
    }
}
