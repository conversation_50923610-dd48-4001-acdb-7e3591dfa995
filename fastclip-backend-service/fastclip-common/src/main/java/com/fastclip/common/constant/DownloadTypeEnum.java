package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DownloadTypeEnum {
    VIDEO(1,"视频"),
    COVER(2,"封面"),
    VIDEOWITHOUTASS(3,"无字幕视频");

    private Integer code;
    private String value;

    public static String DownloadTypeEnum(String code) {
        for (DownloadTypeEnum downloadTypeEnum : DownloadTypeEnum.values()) {
            if (downloadTypeEnum.getCode().equals(code)) {
                return downloadTypeEnum.value;
            }
        }
        return null;
    }
}
