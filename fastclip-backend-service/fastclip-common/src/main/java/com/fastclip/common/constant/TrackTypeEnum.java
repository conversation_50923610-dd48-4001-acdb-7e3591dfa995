package com.fastclip.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TrackTypeEnum {
    Video("Video","视频轨道"),
    Audio("audio","音频轨道"),
    Text("text", "文本轨道"),
    SpecialEffect("specialeffect", "特效轨道"),
    Filter("filter", "滤镜轨道"),
    Sticker("sticker", "贴纸");

    private String code;
    private String value;


    public static String TrackTypeEnum(String code) {
        for (TrackTypeEnum trackTypeEnum : TrackTypeEnum.values()) {
            if (trackTypeEnum.getCode().equals(code)) {
                return trackTypeEnum.value;
            }
        }
        return null;
    }

}
