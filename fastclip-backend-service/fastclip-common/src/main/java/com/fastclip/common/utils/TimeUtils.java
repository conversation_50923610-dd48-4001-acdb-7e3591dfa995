package com.fastclip.common.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtils {
    public static Integer getTimes(String timeStr) {
        String[] timeStrs = timeStr.split(":");
        int hour = Integer.parseInt(timeStrs[0].trim());
        int minute = Integer.parseInt(timeStrs[1].trim());
        String[] secondsStrs = timeStrs[2].trim().split(",");
        int seconds = Integer.parseInt(secondsStrs[0].trim());
        int miliseconds = Integer.parseInt(secondsStrs[1].trim());
        return hour * 360000 + minute * 60000 + seconds * 1000 + miliseconds;
    }

    public static String getTimeStr(Long timestamp) {
        int timestampSeconds = (int)(timestamp/1000L);
        int hour = timestampSeconds/3600000;
        int minute = (timestampSeconds-hour*3600000)/60000;
        int seconds = (timestampSeconds-hour*3600000-minute*60000)/1000;
        int microSeconds = timestampSeconds-hour*3600000-minute*60000 - seconds *1000;
        return hour + ":" + minute + ":" + seconds + " " + microSeconds;
    }

    public static String getTimeStrForSrtFile(Integer timestamp) {
        int hour = timestamp/3600000;
        int minute = (timestamp-hour*3600000)/60000;
        int seconds = (timestamp-hour*3600000-minute*60000)/1000;
        int microSeconds = (timestamp-hour*3600000-minute*60000 - seconds *1000)/10;
        String hourStr = hour < 10 ? "0" + hour: "" + hour;
        String minuteStr = minute < 10 ? "0" + minute: "" + minute;
        String secondStr = seconds < 10 ? "0" + seconds: "" + seconds;
        String microSecondsStr = microSeconds < 10 ? "0" + microSeconds: "" + microSeconds;
        return hourStr + ":" + minuteStr + ":" + secondStr + "." + microSecondsStr;
    }

    public static String getDateTimeStrForSliceFile(Date date , Integer timestamp) {
        int hour = timestamp/3600000;
        int minute = (timestamp-hour*3600000)/60000;
        int seconds = (timestamp-hour*3600000-minute*60000)/1000;
        String hourStr = hour < 10 ? "0" + hour: "" + hour;
        String minuteStr = minute < 10 ? "0" + minute: "" + minute;
        String secondStr = seconds < 10 ? "0" + seconds: "" + seconds;
        return new SimpleDateFormat("yyyy-MM-dd") + "_" + hourStr + "-" + minuteStr + "-" + secondStr;
    }

    public static String getTimeStrForFFmpeg(Integer timestamp) {
        int hour = timestamp/3600000;
        int minute = (timestamp-hour*3600000)/60000;
        int seconds = (timestamp-hour*3600000-minute*60000)/1000;
        int microSeconds = timestamp-hour*3600000-minute*60000 - seconds *1000;
        String hourStr = hour < 10 ? "0" + hour: "" + hour;
        String minuteStr = minute < 10 ? "0" + minute: "" + minute;
        String secondStr = seconds < 10 ? "0" + seconds: "" + seconds;
        String microSecondsStr = microSeconds < 10 ? "00" + minuteStr: (microSeconds < 100 ? "0" + microSeconds : "" + microSeconds);
        return hourStr + ":" + minuteStr + ":" + secondStr + ":" + microSecondsStr;
    }

    public static Date getDate(String dateStr) {
        try {
            int indexOfMonth = dateStr.indexOf("月");
            int indexOfDay = dateStr.indexOf("日");
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String monthStr = dateStr.substring(0, indexOfMonth).trim();
            String  dayStr = dateStr.substring(indexOfMonth + 1, indexOfDay).trim();
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
//            int year = 2024;
            return dateFormat.parse(year + "-" + monthStr + "-" + dayStr);
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Integer timeDiff(String time1, String time2) {
        return strToInt(time1) - strToInt(time2);
    }

    private static Integer strToInt(String time) {
        try {
            int hour = Integer.parseInt(time.substring(0, 2));
            int minute = Integer.parseInt(time.substring(2, 4));
            int second = Integer.parseInt(time.substring(4, 6));
            return (hour * 3600 + minute * 60  + second) * 1000;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date getNowDate() {
        try {
            Date date = new Date();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = dateFormat.format(date);
            return dateFormat.parse(dateStr);
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getNowDateStr() {
        try {
            Date date = new Date();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = dateFormat.format(date);
            return dateStr;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public static String getNowDateDiffStr(Integer days) {
        try {
            Long timestamp = System.currentTimeMillis();
            Long newTimestamp = timestamp - days*3600*24*1000L;
            Date newDate = new Date(newTimestamp);
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = dateFormat.format(newDate);
            return dateStr;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date getNowDateDiff(Integer days) {
        try {
            Long timestamp = System.currentTimeMillis();
            Long newTimestamp = timestamp - days*3600*24*1000;
            Date newDate = new Date(newTimestamp);
            return newDate;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date getFirstDayOfThisMonth() {
        try {
            Date date = new Date();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            String dateStr = dateFormat.format(date) + "-01";
            return dateFormat.parse(dateStr);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date getFirstDayOfThisYear() {
        try {
            Date date = new Date();
            DateFormat dateFormat = new SimpleDateFormat("yyyy");
            String dateStr = dateFormat.format(date) + "-01-01";
            return dateFormat.parse(dateStr);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date getFirstDayOfLastMonth() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.add(2, -1);
            calendar.set(5, 1);
            Date date = calendar.getTime();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String firstDayOfLastMonth = format.format(date);
            return format.parse(firstDayOfLastMonth);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(getNowDateDiffStr(2));
//        System.out.println(timeDiff("123114","083443"));
//        System.out.println(timeDiff("132522","083443"));
//        System.out.println(timeDiff("135837","083443"));
    }
}
