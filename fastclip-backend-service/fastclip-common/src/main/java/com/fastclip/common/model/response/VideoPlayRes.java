package com.fastclip.common.model.response;

import lombok.Data;

import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.ShortBuffer;

@Data
public class VideoPlayRes {
    /**
     * 视频帧：1，音频帧：2
     */
    private Integer type;
    private String imageData;
    private Integer imageWidth;
    private Integer imageHeight;
    private Long videoTimestamp;
    private Long audioTimestamp;
    private String sampleData;
}
