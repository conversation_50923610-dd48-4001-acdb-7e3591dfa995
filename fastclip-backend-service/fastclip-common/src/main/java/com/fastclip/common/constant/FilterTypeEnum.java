package com.fastclip.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 滤镜类型
 */
@AllArgsConstructor
@Getter
public enum FilterTypeEnum {
    Filter1("filter1","滤镜1", 25, 10, 5, 10, -10, -10),
    Filter2("filter2","滤镜2", 20, 8, 2, 4, -8, -11),
    Filter3("filter3", "滤镜3", 15, 9, 10, 12, -9, -12);

    private String code;
    private String value;
    /**
     *
     */
    private Integer saturate;
    private Integer lignthness;
    private Integer contrast;
    private Integer shadow;
    private Integer highLight;
    private Integer temperature;


    public static String FilterTypeEnum(String code) {
        for (FilterTypeEnum filterTypeEnum : FilterTypeEnum.values()) {
            if (filterTypeEnum.getCode().equals(code)) {
                return filterTypeEnum.value;
            }
        }
        return null;
    }

}
