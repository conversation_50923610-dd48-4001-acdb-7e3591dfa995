package com.fastclip.common.model.dataobject;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ChanxuanItemDO {
    private Integer id;
    /**
     * 爬虫的账号
     */
    private String cxAccount;
    /**
     * 达人id
     */
    private String starId;
    /**
     * 达人名称
     */
    private String starName;
    private String productId;
    private String materialProductId;
    private Integer price;
    private String productName;
    private String productUrl;
    private List<String> tags;
    private String cjMaterialLink;
    private Date createDateTime;
    private Date updateDateTime;
    private Integer presellType;
    private String cutVideoPath;
    private Integer isPublished;
}
