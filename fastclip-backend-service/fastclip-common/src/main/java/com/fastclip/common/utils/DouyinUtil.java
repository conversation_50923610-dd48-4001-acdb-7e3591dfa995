package com.fastclip.common.utils;

import com.fastclip.common.model.ao.DouyinAO;
import com.fastclip.common.model.dataobject.DouyinDO;

public class DouyinUtil {
    public static DouyinDO dtoToDO(DouyinAO douyinAO) {
        DouyinDO douyinDO = new DouyinDO();
        douyinDO.setAuthor(douyinAO.getAweme_info().getAuthor().getNickname());
        douyinDO.setDes(douyinAO.getAweme_info().getDesc());
        douyinDO.setDuration(douyinAO.getAweme_info().getVideo().getDuration());
        douyinDO.setShareUrl(douyinAO.getAweme_info().getShare_info().getShare_url());
        douyinDO.setVideoId(douyinAO.getAweme_info().getAweme_id());
        douyinDO.setWidth(douyinAO.getAweme_info().getVideo().getWidth());
        douyinDO.setHeight(douyinAO.getAweme_info().getVideo().getHeight());
        return douyinDO;
    }
}
