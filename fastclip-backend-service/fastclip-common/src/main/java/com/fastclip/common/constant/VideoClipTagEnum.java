package com.fastclip.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VideoClipTagEnum {
    START("start","开头");
//    SIZE("size","尺码"),
//    DRESS("dress", "穿搭"),
//    MATERIAL("material", "材质"),
//    END("end", "结尾");

    private String code;
    private String value;


    public static String getName(String code) {
        for (VideoClipTagEnum trackTypeEnum : VideoClipTagEnum.values()) {
            if (trackTypeEnum.getCode().equals(code)) {
                return trackTypeEnum.value;
            }
        }
        return null;
    }

}
