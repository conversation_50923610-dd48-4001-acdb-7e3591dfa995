package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 订单信息实体类
 */
@Data
public class ApiOrdersDTO {
    private Long item_num;
    private String product_id;
    private Integer author_subsidy;
    private Integer estimated_inst_stepped_commission;
    private String flow_point;
    private String media_type;
    private String order_id;
    private Integer pay_subsidy;
    private Integer platform_subsidy;
    private String shop_name;
    private String app;
    private String author_short_id;
    private Date confirm_time; // 建议根据实际格式用 Date 或保持 String
    private String product_img;
    private Long pay_goods_amount;
    private Long settled_goods_amount;
    private String author_account;
    private Long estimated_tech_service_fee;
    private String product_activity_id;
    private Long real_commission;
    private Long settle_inst_stepped_commission;
    private Date settle_time;
    private Long commission_rate;
    private Long media_id;
    private String product_name;
    private Long total_pay_amount;
    private Integer estimated_user_stepped_commission;
    private Date pay_success_time;
    private Date update_time;
    private Date refund_time;
    private Integer app_id;
    private Long estimated_total_commission;
    private Long estimated_commission;
    private Boolean is_stepped_plan;
    private Long settle_user_stepped_commission;
    private Long shop_id;
    private String author_buyin_id;
    private String author_openid;
}
