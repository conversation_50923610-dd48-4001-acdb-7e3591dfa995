package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SubtitlesDTO {

    private Long id;

    private Long sellerId;

    private Long videoId;

    private String content;

    private Integer startTs;

    private Integer endTs;

    private Integer duration;

    private Integer length;

    private Date createTime;

    private Date updateTime;

    private Boolean isAdded;

}
