package com.fastclip.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 转场类型枚举
 */

@AllArgsConstructor
@Getter
public enum TransitionEnum {
       FADE("fade",""),
       WIPELEFT("wipeleft",""),
       WIPERIGNTH("wiperight",""),
       WIPEUP("wipeup",""),
       WIPEDOWN("wipedown",""),
       SLIDELEFT("slideleft",""),
       SLIDERIGHT("slideright",""),
       SLIDUP("slideup",""),
       SLIDEDOWN("slidedown",""),
       CIRCLECROP("circlecrop",""),
       RECTCROP("rectcrop",""),
       DISTANCE("distance",""),
       FADEBLACK("fadeblack",""),
       FADEWHITE("fadewhite",""),
       RADIAL("radial",""),
       SMOOTHLEFT("smoothleft",""),
       SMOOTHRIGHT("smoothright",""),
       SMOOTHUP("smoothup",""),
        SMOOTHDOWN("smoothdown",""),
       CIRCLEOPEN("circleopen",""),
       CIRCLECLOSE("circleclose",""),
       VERTOPEN("vertopen",""),
       VERTCLOSE("vertclose",""),
       HORZOPEN("horzopen",""),
       HORZCLOSE("horzclose",""),
       DISSOLVE("dissolve",""),
       PIXELIZE("pixelize",""),
       DIAGTL("diagtl",""),
       DIAGTR("diagtr",""),
       DIAGBL("diagbl",""),
       DIAGBR("diagbr",""),
       HLSLICE("hlslice",""),
       HRSLICE("hrslice",""),
       VUSLICE("vuslice",""),
       VDSLICE("vdslice",""),
       HBLUR("hblur",""),
       FADEGRAYS("fadegrays",""),
       WIPETL("wipetl",""),
       WIPETR("wipetr",""),
       WIPEBL("wipebl",""),
       WIPEBR("wipebr",""),
       SQUEEZEH("squeezeh",""),
       SQUEEZEV("squeezev",""),
       ZOOMIN("zoomin",""),
       FADEFAST("fadefast",""),
       FADESLOW("fadeslow",""),
       HLWLND("hlwind",""),
       HRWIND("hrwind",""),
       VUWIND("vuwind",""),
       VDWIND("vdwind",""),
       COVERLEFT("coverleft",""),
       COVERRIGHT("coverright",""),
       COVERUP("coverup",""),
       COVERDOWN("coverdown",""),
       REVEALEFT("revealleft",""),
       REVEALRIGHT("revealright",""),
       REVEALUP("revealup",""),
       REVEALDOWN("revealdown","");


    private String code;
    private String value;


    public static TransitionEnum TransitionEnum(String code) {
        for (TransitionEnum transitionEnum : TransitionEnum.values()) {
            if (transitionEnum.getCode().equals(code)) {
                return transitionEnum;
            }
        }
        return null;
    }


}
