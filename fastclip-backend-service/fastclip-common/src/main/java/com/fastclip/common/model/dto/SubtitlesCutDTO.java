package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 音频片段
 */
@Data
public class SubtitlesCutDTO {
    private Long id;

    private Long projectId;

    private Long videoId;

    private VideoMaterialDTO videoMaterialDTO;

    private VideoMaterialSliceDTO videoMaterialSliceDTO;

    private SubtitlesDTO subtitlesDTO;

    private Integer order;

    private Long subtitlesId;

    private String cutStartContent;

    private String cutEndContent;

    private String content;

    private Integer cutStartTs;

    private Integer cutEndTs;

    private Integer duration;

    private Integer length;

    private Date createTime;

    private Date updateTime;
}
