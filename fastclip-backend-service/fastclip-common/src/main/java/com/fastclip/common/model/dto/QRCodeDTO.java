package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class QRCodeDTO {
    private Long id;

    private Integer status;

    private String captcha;

    private String descUrl;

    private String description;

    private Integer errorCode;

    private Boolean isFrontier;

    private String qrcode;

    private String qrcodeIndexUrl;

    private String token;

    private Date createTime;

    private Date updateTime;
}
