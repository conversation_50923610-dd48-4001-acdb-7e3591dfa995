package com.fastclip.common.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class VideoMaterialDTO {

    private Long id;

    private Long sellerId;

    private SellerDTO seller;

    private Integer duration;

    private String path;

    private Integer size;

    private Boolean isSubtitlesDone;

    private Integer subtitlesBpTs;

    private Date createTime;

    private Date updateTime;

    private String videoName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    private Integer startTime;

    private Integer videoType;

    private String liveRoomId;

    private Integer status;

    private Integer latestSliceMergedId;

    private Integer sort;

    private Integer startScene;

    private Boolean startSceneFlag;

    private List<VideoMaterialSliceDTO> videoMaterialSliceDTOS;
}