package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 订单信息实体类
 */
@Data
public class OrdersDTO {
    private Long id;
    /**
     * 订单号 (示例值: 4737996432465788974)
     */
    private String orderId;

    /**
     * 商品ID (示例值: 3450632721376902816)
     */
    private String productId;

    /**
     * 商品名称 (示例值: 测试商品)
     */
    private String productName;

    /**
     * 商品图片URL (示例值: https://sf1-ecomcdn-tos.pstatp.com/obj/temai/FqO6G1yPxeWxP6qgyrGzF5qfseldwww634-226)
     */
    private String productImg;

    /**
     * 作者账号昵称(抖音/火山作者) (示例值: 我的昵称)
     */
    private String authorAccount;

    /**
     * 商家名称 (示例值: 我的店铺)
     */
    private String shopName;

    /**
     * 订单支付金额，单位分 (示例值: 2100)
     */
    private Long totalPayAmount;

    /**
     * 订单支付金额
     */
    private String totalPayAmountStr;

    /**
     * 达人佣金率（真实数据x1万后的值，如0.35→3500） (示例值: 2000)
     */
    private Long commissionRate;

    /**
     * 达人佣金率（真实数据x1万后的值，如0.35→3500） (示例值: 2000)
     */
    private String commissionRateStr;


    /**
     * 订单状态 (示例值: PAY_SUCC, 可选值: PAY_SUCC/REFUND/SETTLE/CONFIRM)
     */
    private String flowPoint;

    /**
     * App名称 (示例值: 抖音, 可选值: 抖音/火山)
     */
    private String app;

    /**
     * 更新时间 [联盟侧订单更新时间] (示例值: 2006-01-02 15:04:05)
     */
    private Date updateTime;

    /**
     * 确认时间
     */
    private Date confirmTime;


    /**
     * 付款时间 (示例值: 2006-01-02 15:04:05)
     */
    private Date paySuccessTime;

    /**
     * 结算时间（结算前为空字符串） (示例值: 2006-01-02 15:04:05)
     */
    private Date settleTime;

    /**
     * 预估参与结算金额 (示例值: 1100)
     */
    private Long payGoodsAmount;

    /**
     * 实际参与结算金额 (示例值: 0)
     */
    private Long settledGoodsAmount;

    /**
     * 达人实际佣金 (示例值: 0)
     */
    private Long realCommission;

    /**
     * 达人预估佣金 (示例值: 0)
     */
    private Long estimatedCommission;

    /**
     * 预估参与结算金额 (示例值: 1100)
     */
    private Long payGoodsAmountD;

    /**
     * 实际参与结算金额 (示例值: 0)
     */
    private Long settledGoodsAmountD;

    /**
     * 达人实际佣金 (示例值: 0)
     */
    private Long realCommissionD;

    /**
     * 达人预估佣金 (示例值: 0)
     */
    private String estimatedCommissionStr;

    /**
     * 商品数目 (示例值: 1)
     */
    private Long itemNum;

    /**
     * 店铺ID (示例值: 12345)
     */
    private Long shopId;

    /**
     * 退款订单退款时间 (示例值: 2006-01-02 15:04:05)
     */
    private Date refundTime;

    /**
     * 总佣金（预估），对应百应订单明细中的总佣金 (示例值: 580)
     */
    private Long estimatedTotalCommission;

    /**
     * 预估平台技术服务费 (示例值: 58)
     */
    private Long estimatedTechServiceFee;

    /**
     * 选品App client_key (示例值: jifji32rnu3jit43)
     */
    private String pickSourceClientKey;

    /**
     * 选品来源自定义参数 (示例值: dddd)
     */
    private String pickExtra;

    /**
     * 达人抖音号/火山号 (示例值: ********)
     */
    private String authorShortId;

    private DouyinAccountDTO douyinAccountDTO;
}
