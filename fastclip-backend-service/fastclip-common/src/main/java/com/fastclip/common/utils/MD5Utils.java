package com.fastclip.common.utils;

import com.fastclip.common.model.dto.UserDTO;
import sun.security.provider.MD5;

import java.nio.charset.Charset;
import java.security.MessageDigest;

public class MD5Utils {
    public static String getToken(UserDTO userDTO) {
        Long id = userDTO.getId();
        String userName = userDTO.getUserAccount();
        Long timestamp = System.currentTimeMillis();
        Integer ramdonNum = (int)(10000 * Math.random());
        return digest(id + userName + timestamp + ramdonNum);
    }

    public static String digest(String rawString) {
        return digest(rawString, "utf-8");
    }


    public static String digest(String rawString, String charset) {
        Charset cs = Charset.forName(charset);
        try {
            return compute(rawString, cs);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * Computes the MD5 fingerprint of a string.
     *
     * @return the MD5 digest of the input <code>String</code>
     */
    private static String compute(String inStr, Charset charset) throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] md5Bytes = md5.digest(inStr.getBytes(charset));
        return toHexString(md5Bytes);
    }

    public static String toHexString(byte[] bytes) {
        StringBuffer hexValue = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            int val = ((int) bytes[i]) & 0xff;
            if (val < 16) {
                hexValue.append("0");
            }
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();
    }

    public static void main(String[] args) {
        UserDTO userDTO = new UserDTO();
        userDTO.setId(1L);
        userDTO.setUserName("韦永壮");
        System.out.println(getToken(userDTO));
    }
}
