package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频素材的生成状态
 */
@AllArgsConstructor
@Getter
public enum ItemTypeEnum {
    Import(1,"导入商品"),
    Live(2,"直播商品");

    private final Integer value;
    private final String desc;


    public static ItemTypeEnum create(String code) {
        for (ItemTypeEnum itemTypeEnum : ItemTypeEnum.values()) {
            if (itemTypeEnum.getValue().equals(code)) {
                return itemTypeEnum;
            }
        }
        return null;
    }
}
