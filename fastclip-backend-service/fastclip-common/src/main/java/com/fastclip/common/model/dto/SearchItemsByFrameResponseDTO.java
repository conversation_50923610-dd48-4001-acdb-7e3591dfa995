package com.fastclip.common.model.dto;

import lombok.Data;
import java.util.List;

/**
 * Response DTO for searchItemsByFrame API
 * Replaces the original String return type with a structured response
 */
@Data
public class SearchItemsByFrameResponseDTO {
    
    /**
     * List of search results from Python backend
     */
    private List<ItemSearchResultDTO> result;
    
    /**
     * Status code from Python backend (optional)
     */
    private Integer statusCode;
    
    /**
     * Status message from Python backend (optional)
     */
    private String statusMessage;
    
    /**
     * Processing timestamp
     */
    private Long timestamp;
    
    /**
     * Whether the response has been enriched with database information
     */
    private Boolean enriched = false;
    
    /**
     * Default constructor
     */
    public SearchItemsByFrameResponseDTO() {
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * Constructor with result list
     * @param result List of search results
     */
    public SearchItemsByFrameResponseDTO(List<ItemSearchResultDTO> result) {
        this();
        this.result = result;
    }
    
    /**
     * Check if response has valid results
     * @return true if result list is not null and not empty
     */
    public boolean hasResults() {
        return result != null && !result.isEmpty();
    }
    
    /**
     * Get the top result (first item in the list)
     * @return Top search result or null if no results
     */
    public ItemSearchResultDTO getTopResult() {
        return hasResults() ? result.get(0) : null;
    }
    
    /**
     * Get the number of results
     * @return Number of results, 0 if no results
     */
    public int getResultCount() {
        return result != null ? result.size() : 0;
    }
}
