package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频素材的生成状态
 */
@AllArgsConstructor
@Getter
public enum VideoMaterialStatusEnum {
    DONE(1,"完成"),
    PROCESSING(2,"录制中");

    private final Integer value;
    private final String desc;


    public static VideoMaterialStatusEnum create(String code) {
        for (VideoMaterialStatusEnum videoMaterialStatusEnum : VideoMaterialStatusEnum.values()) {
            if (videoMaterialStatusEnum.getValue().equals(code)) {
                return videoMaterialStatusEnum;
            }
        }
        return null;
    }
}
