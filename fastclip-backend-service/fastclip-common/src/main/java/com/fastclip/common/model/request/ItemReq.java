package com.fastclip.common.model.request;

import lombok.Data;

import java.util.List;

@Data
public class ItemReq extends PagebleReq{
    private List<Long> sellerIds;
    private List<Long> itemIds;
    private List<String> outItemIds;
    private String itemName;
    private Boolean isPresell;
    private Boolean isAvailable;
    private Boolean isPublish;
    private Boolean hasProject;
    private Boolean hasMaterial;
    private Integer topN;
    /**
     * 商品类型：1，导入商品；2，直播商品。
     */
    private Integer itemType;
}
