package com.fastclip.common.model.dto;

import com.fastclip.common.effect.SpecialEffectAbstract;
import lombok.Data;

import java.util.List;

@Data
public class VideoClipToCut {
    /**
     * 开始时间字符串
     */
    private String start_time;
    /**
     * 结束时间字符串
     */
    private String end_time;
    /**
     * 开始时间，单位毫秒
     */
    private  Integer  startTimeMM;
    /**
     * 结束时间单位毫秒
     */
    private Integer endTimeMM;
    /**
     * 视频长度
     */
    private Integer duration;
    /**
     * 视频路径
     */
    private String path;
    /**
     * 视频特效
     */
    private List<SpecialEffectAbstract> specialEffects;
}
