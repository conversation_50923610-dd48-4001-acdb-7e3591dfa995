package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特效类型
 */
@AllArgsConstructor
@Getter
public enum SpecialEffectEnum {
    <PERSON><PERSON><PERSON><PERSON>("y<PERSON><PERSON><PERSON>ong","萤火虫"),
    <PERSON><PERSON><PERSON>("xingxing","星星"),
    <PERSON><PERSON><PERSON>("xuehua", "雪花");

    private String code;
    private String value;


    public static String SpecialEffectEnum(String code) {
        for (SpecialEffectEnum specialEffectEnum : SpecialEffectEnum.values()) {
            if (specialEffectEnum.getCode().equals(code)) {
                return specialEffectEnum.value;
            }
        }
        return null;
    }
}
