package com.fastclip.common.constant;

import java.util.HashMap;
import java.util.Map;

public class ChineseToNum {
    private static final Map<String, Integer> map = new HashMap() {{
        put("一", 1);
        put("二", 2);
        put("三", 3);
        put("四", 4);
        put("五", 5);
        put("六", 6);
        put("七", 7);
        put("八", 8);
        put("九", 9);
        put("十", 10);
    }};

    public static Integer getNum(String str) {
        return map.get(str);
    }
}
