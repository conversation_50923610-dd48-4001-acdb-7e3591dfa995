package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VideoPlayTypeEnum {
    VideoClip("videoClip","视频片段播放"),
    Works("works","作品播放"),
    Subtitles("subtitles","选择字幕播放");

    private String code;
    private String value;


    public static String VideoPlayTypeEnum(String code) {
        for (VideoPlayTypeEnum videoPlayTypeEnum : VideoPlayTypeEnum.values()) {
            if (videoPlayTypeEnum.getCode().equals(code)) {
                return videoPlayTypeEnum.value;
            }
        }
        return null;
    }
}