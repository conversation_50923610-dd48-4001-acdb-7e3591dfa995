package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class DouyinAccountDTO {

    private Long id;

    private String phone;

    private Long sellerId;

    private SellerDTO sellerDTO;

    private String douyinName;

    private Date createTime;

    private Date updateTime;

    private Integer worksCountToday;

    private Integer publishedWorksCountToday;

    private Integer unPublishedWorks;

    private String coverBackground;

    private String code;

    private String accessToken;

    private Date expireTime;

    private Date expiresIn;

    private String refreshToken;

    private Date refreshExpiresIn;

    private String openId;

    private String inviteToken;

    private Boolean inviteDone;

    private Integer type;

    private Long teamId;

    private TeamDTO teamDTO;

    private Long selfId;

    private UserDTO selfUserDTO;

    private Integer shareRatio;
}