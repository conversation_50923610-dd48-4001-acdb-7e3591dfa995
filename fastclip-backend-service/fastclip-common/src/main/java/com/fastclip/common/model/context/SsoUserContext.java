package com.fastclip.common.model.context;

import com.fastclip.common.model.dto.UserDTO;
import lombok.Data;

@Data
public class SsoUserContext {

    private static final ThreadLocal<UserDTO> userContext = new ThreadLocal<>();

    public static void setUser(UserDTO ssoUser){
        userContext.set(ssoUser);
    }

    public static void removeUser(){
        userContext.remove();
    }

    public static UserDTO getUser(){
        return userContext.get();
    }
}
