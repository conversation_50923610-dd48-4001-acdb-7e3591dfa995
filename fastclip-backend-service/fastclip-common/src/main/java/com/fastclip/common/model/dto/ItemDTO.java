package com.fastclip.common.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ItemDTO {
    private Long id;

    private String itemName;

    private String outItemId;

    private Long sellerId;

    private String des;

    private String creator;

    private String shareUrl;

    private Boolean isPresell;

    private Boolean isAvailable;

    private List<VideoMaterialClipDTO> videoMaterialClipDTOList;

    private SellerDTO seller;

    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Date updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date maxMaterialDate;

    /**
     * 商品类型：1，导入商品；2，直播商品。
     */
    private Integer itemType;

    private String materialInfo;

    private Long projectId;

    private ProjectDTO projectDTO;
}
