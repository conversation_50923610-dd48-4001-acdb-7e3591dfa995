package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class VideoClipDTO {
    private Long id;

    private Long projectId;

    private Long startSubtitlesCutId;

    private Long endSubtitlesCutId;

    private Long startSubtitlesId;

    private Long endSubtitlesId;

    private String subtitles;

    private Integer duration;

    private Integer sort;

    private Integer subtitlesCutCount;

    private Date createTime;

    private Date updateTime;

    private List<VideoClipTagDTO> tags;

    private List<SubtitlesCutDTO> subtitlesCutDTOList;
}
