package com.fastclip.common.model.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CreateVideoReq{
    /**
     * 素材临时目录
     */
    private String videoTmpPath;
    /**
     * 素材目录
     */
    private String videoPath;
    private Long sellerId;
    private Date startDate;
    /**
     * 如果liveRoomId不为空，这个字段表示视频录制的开始时间；否则，表示相对于其它分段素材的开始时间
     */
    private Integer startTime;
    private String startDateTimeStr;
    /**
     * 视频类型：1，素材下载；2，直播下载；3，分镜素材
     */
    private Integer videoType;
    private String liveRoomId;
    private Long videoId;
    private Long itemId;
    private String itemOutId;
    private String shareUrl;
    private String itemName;
}
