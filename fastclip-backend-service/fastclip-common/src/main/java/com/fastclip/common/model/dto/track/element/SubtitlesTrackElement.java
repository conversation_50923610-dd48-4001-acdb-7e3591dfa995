package com.fastclip.common.model.dto.track.element;

import com.fastclip.common.constant.FontTypeEnum;
import lombok.Data;

/**
 * 字幕轨道元素
 */
@Data
public class SubtitlesTrackElement extends TrackElement{
    /**
     * 字幕内容
     */
    String content;
    /**
     * 字体大小
     */
    Integer fontSize;
    /**
     * 字体
     */
    FontTypeEnum fontType;
    /**
     * 字幕所在位置x坐标
     */
    Integer xPosition;
    /**
     * 字幕所在位置y坐标
     */
    Integer yPosition;

    @Override
    public String getFilePath() {
        return null;
    }
}
