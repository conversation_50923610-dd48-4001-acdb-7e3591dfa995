package com.fastclip.common.model.dto.track.element;

import com.fastclip.common.model.dto.VideoClipDTO;
import lombok.Data;

/**
 * 视频轨道元素
 */
@Data
public class VideoTrackElement extends TrackElement{
    /**
     * 原视频片段
     */
    VideoClipDTO videoClipDTO;
    /**
     * 宽度
     */
    Integer width;
    /**
     * 高度
     */
    Integer height;
    /**
     * 音量
     */
    Integer volume;

    @Override
    public String getFilePath() {
//        return videoClipDTO.getVideoPath();
        return  null;
    }
}
