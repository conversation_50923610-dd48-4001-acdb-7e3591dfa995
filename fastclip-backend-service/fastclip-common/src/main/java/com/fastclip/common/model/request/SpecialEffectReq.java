package com.fastclip.common.model.request;

import com.fastclip.common.model.dto.FontEffectDTO;
import com.fastclip.common.model.dto.VideoClipSpeicalEffectDTO;
import lombok.Data;

import java.util.List;

@Data
public class SpecialEffectReq {
    String inputVideoPath;
    String musicPath;
    String stickerPath;
    String leftTopStickerPath;
    String leftBottomStickerPath;
    String rightTopStickerPath;
    String rightBottomStickerPath;
    String outputVideoPath;
    String outputVideoPathWithOutAss;
    String videoPts;
    String audioAtempo;
    String saturationStr;
    String contrastStr;
    String brightnessStr;
    String endingVideoPath;
    FontEffectDTO fontEffectDTO;
    String keywords;
    private Integer width;
    private Integer height;
    List<VideoClipSpeicalEffectDTO> videoClipSpeicalEffectDTOList;
}
