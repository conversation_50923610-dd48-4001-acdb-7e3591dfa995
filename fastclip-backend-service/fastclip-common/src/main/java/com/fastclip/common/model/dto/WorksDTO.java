package com.fastclip.common.model.dto;

import com.fastclip.common.model.dto.specialEffect.FilterEffectDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WorksDTO {
    private Long id;

    private Long projectId;

    private ProjectDTO projectDTO;

    private Long itemId;

    private ItemDTO item;

    private Long sellerId;

    private SellerDTO seller;

    private Integer duration;

    private String specialEffectsTemplateCode;

    private Boolean isComposed;

    private Boolean isPublished;

    private String videoPath;

    private String videoPathWithoutAss;

    private List<WorksDetailDTO> details;

    private MusicDTO musicDTO;

    private Integer musicId;

    private Integer filterId;

    private FilterEffectDTO filterEffectDTO;

    private Integer stickerId;

    private StickerDTO stickerDTO;

    private Integer speedUp;

    private String endingVideoPath;

    private String worksName;

    private String worksDesc;

    private String worksCover;

    private String simpleItemName;

    private String itemFeatures;

    private String itemTags;

    private String phone;

    private Long accountId;

    private String worksTag;

    private DouyinAccountDTO douyinAccountDTO;

    private Date createTime;

    private Date updateTime;

    private Long creatorId;

    private UserDTO creator;

    private Integer fontId;

    private FontEffectDTO fontEffectDTO;

    private String keywords;

    private Boolean isDescCompleted;

    private Integer leftTopStickerId;

    private StickerDTO leftTopStickerDTO;

    private Integer rightTopStickerId;

    private StickerDTO rightTopStickerDTO;

    private Integer leftBottomStickerId;

    private StickerDTO leftBottomStickerDTO;

    private Integer rightBottomStickerId;

    private StickerDTO rightBottomStickerDTO;

    private Integer composeStatus;

    private String downloadCoverUrl;

    private String downloadWorksUrl;

    private String downloadWorksWithoutAssUrl;

    private String localDownloadCoverUrl;

    private String localDownloadWorksUrl;

    private String lcoalDownloadWorksWithoutAssUrl;
}