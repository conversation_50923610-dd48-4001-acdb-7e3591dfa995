package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class HomeData {
    Integer worksCountToday;
    Integer publishedWorksCountToday;
    Integer unPublishedWorks;
    Integer publishedWorksCountThisMonth;
    Integer publishedWorksCountLastMonth;
    Integer publishedWorksCountTotal;
    List<DouyinAccountDTO> douyinAccountDTOS;
    /**
     * 绑定达人数量
     */
    Integer douyinAccountCount;
    /**
     * 当天订单数量
     */
    Long ordersCountToday;
    /**
     * 当天订单总额
     */
    Long totalPayAmountToday;

    /**
     * 当月订单数量
     */
    Long ordersCountThisMonth;
    /**
     * 当月订单总额
     */
    Long totalPayAmountThisMonth;
    /**
     * 当年订单数量
     */
    Long ordersCountThisYear;
    /**
     * 当年订单总额
     */
    Long totalPayAmountThisYear;
}
