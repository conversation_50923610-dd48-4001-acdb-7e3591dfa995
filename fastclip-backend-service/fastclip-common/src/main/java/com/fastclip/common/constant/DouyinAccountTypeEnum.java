package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频素材的生成状态
 */
@AllArgsConstructor
@Getter
public enum DouyinAccountTypeEnum {
    PRIVATE(1,"个人号"),
    TEAM(2,"团队号"),
    SELF(3,"自营号");

    private final Integer value;
    private final String desc;


    public static DouyinAccountTypeEnum create(String code) {
        for (DouyinAccountTypeEnum itemTypeEnum : DouyinAccountTypeEnum.values()) {
            if (itemTypeEnum.getValue().equals(code)) {
                return itemTypeEnum;
            }
        }
        return null;
    }
}
