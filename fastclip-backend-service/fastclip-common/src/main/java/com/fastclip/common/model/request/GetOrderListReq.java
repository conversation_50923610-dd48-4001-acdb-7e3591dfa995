package com.fastclip.common.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GetOrderListReq extends PagebleReq {
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    Date endDate;
    String flowPoint;
    Long teamId;
    Integer accountType;
    String douyinAccountCode;
    String orderId;
    Long selfCutterId;
}
