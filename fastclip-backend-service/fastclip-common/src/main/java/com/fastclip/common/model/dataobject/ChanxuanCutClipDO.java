package com.fastclip.common.model.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class ChanxuanCutClipDO {
    private Integer id;
    /**
    * 爬虫的账号
    */
    private String cxAccount;
    /**
     * 达人id
     */
    private String starId;
    /**
     * 达人名称
     */
    private String starName;
    private String taskId;
    private Integer clipSize;
    private Integer duration;
    private String title;
    private String productId;
    private Boolean isPublish;
    private String materialProductId;
    private String productName;
    private String productUrl;
    private String cutVideoPath;
    private String description;
    private Date createDateTime;
    private Date updateDateTime;
}
