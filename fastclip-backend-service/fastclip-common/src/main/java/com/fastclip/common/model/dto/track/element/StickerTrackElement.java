package com.fastclip.common.model.dto.track.element;

import com.fastclip.common.constant.FontTypeEnum;
import com.fastclip.common.constant.StickerEnum;
import lombok.Data;

/**
 * 贴纸类型
 */
@Data
public class StickerTrackElement extends TrackElement{
    /**
     * 贴纸类型
     */
    StickerEnum stickerEnum;
    /**
     * 字幕所在位置x坐标
     */
    Integer xPosition;
    /**
     * 字幕所在位置y坐标
     */
    Integer yPosition;
    /**
     * 宽度
     */
    Integer width;
    /**
     * 高度
     */
    Integer height;

    @Override
    public String getFilePath() {
        return null;
    }
}
