package com.fastclip.common.model.request;

import lombok.Data;

import java.util.List;

@Data
public class ProjectReq extends PagebleReq{
    private Long id;
    private List<Long> sellerIds;
    private List<Long> itemIds;
    private String projectName;
    private String itemName;
    private String sellerName;
    private Integer worksCount;
    private List<Integer> status;
    private String maxMaterialDate;
    private String orderByClause;
    private String ascOrDesc;
    private Long creatorId;
    private Boolean isAdmin;
}
