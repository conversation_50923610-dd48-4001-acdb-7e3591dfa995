package com.fastclip.common.utils;

import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.common.model.dto.VideoMaterialClipDTO;

import java.util.ArrayList;
import java.util.List;

public class VideoMaterialUtils {
    public static List<VideoMaterialClipDTO> getVideoMaterialClips(List<SubtitlesCutDTO> subtitlesCutDTOList) {
        List<VideoMaterialClipDTO> videoMaterialClipDTOS = new ArrayList<>();
        Integer totalDuration = 0;
        for(SubtitlesCutDTO subtitlesCutDTO: subtitlesCutDTOList) {
            VideoMaterialClipDTO videoMaterialClipDTO = new VideoMaterialClipDTO();
            videoMaterialClipDTO.setVideoId(subtitlesCutDTO.getVideoId());
            videoMaterialClipDTO.setOrginStartTs(subtitlesCutDTO.getCutStartTs());
            videoMaterialClipDTO.setOriginEndTs(subtitlesCutDTO.getCutEndTs());
            videoMaterialClipDTO.setStartTs(totalDuration);
            videoMaterialClipDTO.setEndTs(totalDuration + subtitlesCutDTO.getDuration());
            totalDuration +=  subtitlesCutDTO.getDuration();
            videoMaterialClipDTOS.add(videoMaterialClipDTO);
        }
        return videoMaterialClipDTOS;
    }
}
