package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 去重特效模板code
 */
@AllArgsConstructor
@Getter
public enum SpecialEffectTemplateEnum {
    <PERSON><PERSON><PERSON><PERSON>("<PERSON><PERSON><PERSON><PERSON><PERSON>","萤火虫"),
    <PERSON><PERSON><PERSON>("xingxing","星星"),
    <PERSON><PERSON><PERSON>("xuehua", "雪花");

    private String code;
    private String value;


    public static String SpecialEffectEnum(String code) {
        for (SpecialEffectTemplateEnum specialEffectEnum : SpecialEffectTemplateEnum.values()) {
            if (specialEffectEnum.getCode().equals(code)) {
                return specialEffectEnum.value;
            }
        }
        return null;
    }
}
