package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class UserDTO {

    private Long id;

    private String userAccount;

    private String userName;

    private Boolean isAdmin;

    private Boolean isSelfCutter;

    private Boolean hasUploadPrevilege;

    private Date createTime;

    private Date updateTime;

    private Integer maxDownloadOneday;

    private Integer maxWorksOneday;

    private String token;
}