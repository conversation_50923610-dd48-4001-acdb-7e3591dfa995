package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class WorksDetailDTO {
    private Long id;

    private Long projectId;

    private Long worksId;

    private Long sellerId;

    private Long videoClipId;

    private VideoClipDTO videoClipDTO;

    private Integer sort;

    private Integer duration;

    private Boolean flipFlag;

    private String tranCode;

    private String tagCode;

    private Integer cutPercent;

    private Integer transId;

    private Date createTime;

    private Date updateTime;
}