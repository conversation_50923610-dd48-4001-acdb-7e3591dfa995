package com.fastclip.common.utils;

/**
 * 字幕相关处理工具
 */
public class SrtUtils {
    /**
     * 从时间字符串获取时间，精确到毫秒，输入格式：hh:tt:ss.mmm
     * @param timeStr
     * @return
     */
    public static Integer getTimeFromTimeStr(String timeStr) {
        int indexOfHour = timeStr.indexOf(":");
        int  hour = Integer.parseInt(timeStr.substring(0, indexOfHour));
        int  indexOfMinute = timeStr.indexOf(":", indexOfHour + 1);
        int minute = Integer.parseInt(timeStr.substring(indexOfHour + 1, indexOfMinute));
        int indexOfSecond = timeStr.indexOf(".", indexOfMinute);
        int second = Integer.parseInt(timeStr.substring(indexOfMinute + 1, indexOfSecond));
        int millisecond = Integer.parseInt(timeStr.substring(indexOfSecond + 1));
        return hour * 3600000 + minute * 60000 +  second * 1000 + millisecond;
    }

    public static void main(String[] args) {
        System.out.println(getTimeFromTimeStr("01:01:01.123"));
    }
}
