package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频素材的合并状态
 */
@AllArgsConstructor
@Getter
public enum VideoMaterialCombineStatusEnum {
    DONE(1,"合并完成"),
    PROCESSING(2,"合并中");

    private final Integer value;
    private final String desc;


    public static VideoMaterialCombineStatusEnum create(String code) {
        for (VideoMaterialCombineStatusEnum videoMaterialStatusEnum : VideoMaterialCombineStatusEnum.values()) {
            if (videoMaterialStatusEnum.getValue().equals(code)) {
                return videoMaterialStatusEnum;
            }
        }
        return null;
    }
}
