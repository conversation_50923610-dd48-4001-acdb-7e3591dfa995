package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字体类型
 */

@AllArgsConstructor
@Getter
public enum FontTypeEnum {
    Font1("font1","字体1"),
    Font2("font2","字体2"),
    Font3("font3", "字体3");

    private String code;
    private String value;


    public static String FontTypeEnum(String code) {
        for (FontTypeEnum fontTypeEnum : FontTypeEnum.values()) {
            if (fontTypeEnum.getCode().equals(code)) {
                return fontTypeEnum.value;
            }
        }
        return null;
    }
}
