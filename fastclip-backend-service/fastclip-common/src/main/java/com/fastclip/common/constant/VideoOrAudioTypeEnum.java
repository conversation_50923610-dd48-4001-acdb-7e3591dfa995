package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VideoOrAudioTypeEnum {
    VIDEO("video","视频"),
    AUDIO("audio","音频");

    private final String code;
    private final String value;


    public static VideoOrAudioTypeEnum createVideoOrAudioTypeEnum(String code) {
        for (VideoOrAudioTypeEnum webSocketReqTypeEnum : VideoOrAudioTypeEnum.values()) {
            if (webSocketReqTypeEnum.getCode().equals(code)) {
                return webSocketReqTypeEnum;
            }
        }
        return null;
    }
}
