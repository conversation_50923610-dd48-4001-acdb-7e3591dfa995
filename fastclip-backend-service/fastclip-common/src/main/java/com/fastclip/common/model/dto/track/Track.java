package com.fastclip.common.model.dto.track;

import com.fastclip.common.model.dto.track.element.TrackElement;
import lombok.Data;

import java.util.List;

/**
 * 轨道
 */
@Data
public class Track {
    /**
     * 轨道持续时间
     */
    Integer duration;
    /**
     * 是否隐藏
     */
    Integer isHide;
    /**
     * 是否锁定
     */
    Integer isLock;
    /**
     * 是否主轨道
     */
    Boolean isMain;
    /**
     * 轨道元素
     */
    List<TrackElement> trackElementList;
}
