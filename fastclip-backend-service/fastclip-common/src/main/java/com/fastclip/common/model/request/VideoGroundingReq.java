package com.fastclip.common.model.request;

import lombok.Data;

@Data
public class VideoGroundingReq {
    
    /**
     * Video material ID
     */
    private Integer videoId;
    
    /**
     * Video file path (alternative to videoId)
     */
    private String videoPath;
    
    /**
     * Item type filter (optional)
     * 1: clothing, 2: shoes, 3: accessories
     */
    private Integer itemType;
    
    /**
     * Seller ID filter (optional)
     */
    private Integer sellerId;
    
    /**
     * Task unique identifier for WebSocket communication
     */
    private String taskId;
    
    /**
     * Processing options
     */
    private Boolean enableRealTimeUpdates = true;
    
    /**
     * Confidence threshold for recognition results
     */
    private Double confidenceThreshold = 0.5;
}
