package com.fastclip.common.model.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class VideoClipDO {
    private Integer id;
    /**
     * 爬虫的账号
     */
    private String cxAccount;
    /**
     * 达人id
     */
    private String starId;
    /**
     * 达人名称
     */
    private String starName;
    private Integer clipSize;
    private Integer duration;
    private String clipTag;
    private String cover;
    private String roomId;
    private Date startTs;
    private String taskId;
    private String title;
    private String url;
    private String productId;
    private String productName;
    private String localAudioPath;
    private String localVideoPath;
    private String localSrtPath;
    private Date createDateTime;
    private Date updateDateTime;
}
