package com.fastclip.common.model.dto;

import lombok.Data;

/**
 * Item search result for product recognition
 */
@Data
public class ItemSearchResultDTO {
    private String itemId;
    private String itemName;
    private Double score;
    private Integer sort;
    private Integer itemType;
    private Long databaseId; // Internal database ID
    private String outItemId; // External item ID

    /**
     * Default constructor
     */
    public ItemSearchResultDTO() {
    }

    /**
     * Constructor with basic fields from Python backend
     * @param itemId Item ID from Python backend
     * @param score Confidence score
     */
    public ItemSearchResultDTO(String itemId, Double score) {
        this.itemId = itemId;
        this.score = score;
    }

    /**
     * Constructor with all basic fields
     * @param itemId Item ID from Python backend
     * @param score Confidence score
     * @param sort Sort order
     */
    public ItemSearchResultDTO(String itemId, Double score, Integer sort) {
        this.itemId = itemId;
        this.score = score;
        this.sort = sort;
    }

    /**
     * Check if this result has been enriched with database information
     * @return true if itemName is not null
     */
    public boolean isEnriched() {
        return itemName != null;
    }

    /**
     * Check if the score meets the confidence threshold
     * @param threshold Confidence threshold (e.g., 0.5)
     * @return true if score is above threshold
     */
    public boolean meetsThreshold(double threshold) {
        return score != null && score >= threshold;
    }
}