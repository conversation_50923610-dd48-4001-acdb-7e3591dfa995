package com.fastclip.common.model.template;


public class RequestTemplate {
    public RequestTemplate() {
    }

    public <T> RequestTemplate.Response<T> doRequest(RequestTemplate.RequestAction<T> requestAction) {
        try {
            RequestTemplate.Response<T> response = new RequestTemplate.Response();
            response.setResultCode(0);
            response.setResult(requestAction.doAction());
            return response;
        } catch (RequestTemplate.IrtcException var5) {
            int code = var5.getCode() != 0 ? var5.getCode() : 1;
            RequestTemplate.Response<T> response = new RequestTemplate.Response();
            response.setResultCode(code);
            response.setResultDesc(var5.getMessage());
            return response;
        } catch (Exception var6) {
            LogHolder.IRTC_COMMONS_LOGGER.error("doRequest error:{}", var6.getMessage(), var6);
            RequestTemplate.Response<T> response = new RequestTemplate.Response();
            response.setResultCode(1);
            response.setResultDesc(var6.getMessage());
            return response;
        }
    }

    public interface RequestAction<T> {
        T doAction();
    }

    public static class IrtcException extends RuntimeException {
        public static final int SYS_ERROR = 1;
        private int code;

        public IrtcException(int code, String message) {
            super(message);
            this.code = code;
        }

        public IrtcException(String message, Throwable cause) {
            super(message, cause);
        }

        public int getCode() {
            return this.code;
        }

        public void setCode(int code) {
            this.code = code;
        }
    }

    public static class Response<T> {
        public static final int SUCC_CODE = 0;
        private Integer resultCode;
        private String resultDesc;
        private T result;

        public Response() {
        }

        public Integer getResultCode() {
            return this.resultCode;
        }

        public void setResultCode(Integer resultCode) {
            this.resultCode = resultCode;
        }

        public String getResultDesc() {
            return this.resultDesc;
        }

        public void setResultDesc(String resultDesc) {
            this.resultDesc = resultDesc;
        }

        public T getResult() {
            return this.result;
        }

        public void setResult(T result) {
            this.result = result;
        }

        public boolean success() {
            return this.resultCode == 0;
        }
    }
}