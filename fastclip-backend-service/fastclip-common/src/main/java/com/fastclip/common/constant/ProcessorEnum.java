package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ProcessorEnum {
    GPU("gpu","gpu"),
    CPU("cpu","cpu");

    private final String value;
    private final String desc;


    public static ProcessorEnum create(String code) {
        for (ProcessorEnum processorEnum : ProcessorEnum.values()) {
            if (processorEnum.getValue().equals(code)) {
                return processorEnum;
            }
        }
        return null;
    }
}
