package com.fastclip.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频素材的类型
 */
@AllArgsConstructor
@Getter
public enum VideoMaterialTypeEnum {
    DOWNLOAD(1,"素材下载"),
    LIVE(2,"直播间获取"),
    SPLIT(3,"分镜素材");

    private final Integer value;
    private final String desc;


    public static VideoMaterialTypeEnum create(String code) {
        for (VideoMaterialTypeEnum videoMaterialTypeEnum : VideoMaterialTypeEnum.values()) {
            if (videoMaterialTypeEnum.getValue().equals(code)) {
                return videoMaterialTypeEnum;
            }
        }
        return null;
    }
}
