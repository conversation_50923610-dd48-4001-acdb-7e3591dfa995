package com.fastclip.common.model.ao;

import lombok.Data;

import java.util.Date;

@Data
public class VideoClipAO {
    private String cxAccount;
    private String starId;
    private String starName;
    private Date date;
    private Integer clip_size;
    private String clip_tag;
    private Integer duration;
    private String cover;
    private Integer is_download;
    private String room_id;
    private Date start_ts;
    private String task_id;
    private String title;
    private String url;
    private String product_id;
    private String product_name;
    private String localAudioPath;
    private String localVideoPath;
    private String localCutVideoPath;
    private String localSrtPath;
    private String localCutSrtPath;
}
