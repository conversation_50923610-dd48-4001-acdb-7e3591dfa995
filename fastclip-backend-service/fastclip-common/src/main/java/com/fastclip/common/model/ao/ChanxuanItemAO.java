package com.fastclip.common.model.ao;

import lombok.Data;

import java.util.List;

@Data
public class ChanxuanItemAO {
    private String cx_account;
    private String star_name;
    private String star_id;
    private String product_id;
    private Integer price;
    private String product_name;
    private String product_url;
    private List<String> tags;
    private String cj_material_link;
    private Integer presell_type;

    public String getMaterialProductId(){
        if(cj_material_link == null) {
            return null;
        }
        int startIndexOfProduct = cj_material_link.indexOf("product_id=");
        if(startIndexOfProduct<0) {
            return null;
        }
        int endIndexOfProduct = cj_material_link.indexOf("\\u0026", startIndexOfProduct);
        if(endIndexOfProduct<0) {
            endIndexOfProduct = cj_material_link.indexOf("&", startIndexOfProduct);
        }
        if(endIndexOfProduct<0)
            return cj_material_link.substring(startIndexOfProduct + 11);
        else
            return cj_material_link.substring(startIndexOfProduct + 11, endIndexOfProduct);
    }
}
