package com.fastclip.common.utils;

import com.fastclip.common.model.ao.VideoClipAO;
import com.fastclip.common.model.ao.ChanxuanClipDatasAO;
import com.fastclip.common.model.ao.ChanxuanItemAO;
import com.fastclip.common.model.dataobject.ChanxuanItemDO;
import com.fastclip.common.model.dataobject.VideoClipDO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.VideoClipToCut;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ChanxuanUtil {
    public static VideoClipDO dtoToDO(VideoClipAO chanxuanClipAO) {
        VideoClipDO chanxuanClipDO = new VideoClipDO();
        BeanUtils.copyProperties(chanxuanClipAO, chanxuanClipDO);
        return chanxuanClipDO;
    }

    public static ChanxuanItemDO itemAoToDO(ChanxuanItemAO itemAO) {
        ChanxuanItemDO itemDO = new ChanxuanItemDO();
        BeanUtils.copyProperties(itemAO, itemDO);
        itemDO.setCxAccount(itemAO.getCx_account());
        itemDO.setProductId(itemAO.getProduct_id());
        itemDO.setProductName(itemAO.getProduct_name());
        itemDO.setProductUrl(itemAO.getProduct_url());
        itemDO.setStarId(itemAO.getStar_id());
        itemDO.setStarName(itemAO.getStar_name());
        itemDO.setMaterialProductId(itemAO.getMaterialProductId());
        itemDO.setPresellType(itemAO.getPresell_type());
        return itemDO;
    }

    public static List<VideoClipDO> dtosToDOs(List<VideoClipAO> chanxuanClipAOS) {
        if(CollectionUtils.isEmpty(chanxuanClipAOS)) {
            return new ArrayList<>();
        }
        return chanxuanClipAOS.stream().map(ChanxuanUtil::dtoToDO).collect(Collectors.toList());
    }

    public static List<VideoClipAO> getClipAOS(List<ChanxuanClipDatasAO> list) {
        List<VideoClipAO> datas = new ArrayList<>();
        for(ChanxuanClipDatasAO clipDatasDTO: list) {
            List<VideoClipAO> tmpDatas = clipDatasDTO.getList();
            if(CollectionUtils.isEmpty(tmpDatas)) {
                continue;
            }
            for(VideoClipAO clipDTO: tmpDatas) {
                clipDTO.setDate(clipDatasDTO.getDate());
                clipDTO.setRoom_id(clipDatasDTO.getRoom_id());
            }
            datas.addAll(tmpDatas);
        }
        return datas;
    }

    public static VideoClipDO clipAoToDO(VideoClipAO clipAO) {
        VideoClipDO clipDO = new VideoClipDO();
        BeanUtils.copyProperties(clipAO, clipDO);
        clipDO.setCxAccount(clipAO.getCxAccount());
        clipDO.setProductId(clipAO.getProduct_id());
        clipDO.setProductName(clipAO.getProduct_name());
        clipDO.setClipSize(clipAO.getClip_size());
        clipDO.setClipTag(clipAO.getClip_tag());
        clipDO.setCover(clipAO.getCover());
        clipDO.setDuration(clipAO.getDuration());
        clipDO.setRoomId(clipAO.getRoom_id());
        clipDO.setTaskId(clipAO.getTask_id());
        clipDO.setStartTs(clipAO.getStart_ts());
        return clipDO;
    }

    public static VideoClipDTO clipDoToDTO(VideoClipDO clipDO) {
        VideoClipDTO clipDTO = new VideoClipDTO();
        BeanUtils.copyProperties(clipDO, clipDTO);
        return clipDTO;
    }

    public static List<VideoClipDTO> clipsDoToDTOs(List<VideoClipDO> clipDOs) {
        if(CollectionUtils.isEmpty(clipDOs)) {
            return new ArrayList<>();
        }
        return clipDOs.stream().map(ChanxuanUtil::clipDoToDTO).collect(Collectors.toList());
    }
}
