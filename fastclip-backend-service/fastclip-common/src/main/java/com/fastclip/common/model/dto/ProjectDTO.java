package com.fastclip.common.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ProjectDTO {
    private Long id;

    private String projectName;

    private Integer itemType;

    private Long itemId;

    private String itemName;

    private Long sellerId;

    private String sellerName;

    private String des;

    private Integer status;

    private Date createTime;

    private Date updateTime;

    private Integer worksCount;

    private String shareUrl;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date maxMaterialDate;

    private String cover;

    private Long creatorId;

    private UserDTO creator;

}
