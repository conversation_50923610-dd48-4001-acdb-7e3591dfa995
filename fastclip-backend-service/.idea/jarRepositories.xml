<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nesc-dev-test" />
      <option name="name" value="nesc-dev-test" />
      <option name="url" value="http://10.211.96.56:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nesc-dev-irtc" />
      <option name="name" value="nesc-dev-irtc" />
      <option name="url" value="http://10.211.96.56:8081/repository/maven-irtc/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="http://repo1.maven.org/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jitpack.io" />
      <option name="name" value="jitpack.io" />
      <option name="url" value="https://jitpack.io" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nesc-dev-xc" />
      <option name="name" value="nesc-dev-xc" />
      <option name="url" value="http://10.211.96.56:8081/repository/xc-maven/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="com.e-iceblue" />
      <option name="name" value="com.e-iceblue" />
      <option name="url" value="https://repo.e-iceblue.cn/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="qcloud-central" />
      <option name="name" value="qcloud mirror central" />
      <option name="url" value="http://mirrors.cloud.tencent.com/nexus/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="hengsheng" />
      <option name="name" value="hengsheng mirror central" />
      <option name="url" value="http://10.211.96.56:8081/repository/maven-hengsheng/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="douyin-openapi-repo" />
      <option name="name" value="douyin-openapi-repo" />
      <option name="url" value="https://artifacts-cn-beijing.volces.com/repository/douyin-openapi/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="dashscope-sdk" />
      <option name="name" value="dashscope-sdk" />
      <option name="url" value="https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/repositories/central/" />
    </remote-repository>
  </component>
</project>