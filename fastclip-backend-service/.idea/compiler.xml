<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="fastclip-biz" />
        <module name="fastclip-opencv" />
        <module name="fastclip-dao" />
        <module name="fastclip-common" />
        <module name="fastclip-service" />
        <module name="fastclip-starter" />
        <module name="fastclip-llm" />
      </profile>
    </annotationProcessing>
  </component>
</project>