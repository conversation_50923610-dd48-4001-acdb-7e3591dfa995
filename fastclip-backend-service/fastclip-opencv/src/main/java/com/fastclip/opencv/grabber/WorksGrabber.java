package com.fastclip.opencv.grabber;

import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.model.dto.MusicDTO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.common.model.dto.WorksDetailDTO;
import com.fastclip.opencv.filter.BaseFilter;
import org.bytedeco.javacv.*;

import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class WorksGrabber extends Grabber {

    WorksDTO worksDTO;

    VideoClipGrabber videoClipGrabber;

    List<WorksDetailDTO> worksDetailDTOS;

    VideoClipDTO curVideoClipDTO;

    FFmpegFrameGrabber musicGrabber;

    int curIndexOfWorksDetail = 0;

    @Override
    public byte[] grabRange(Integer startTs, Integer endTs) {
        return videoClipGrabber.grabRange(startTs, endTs);
    }

    @Override
    public Frame grabImage() {
        Frame frame = videoClipGrabber.grabImage();
        System.out.println(curIndexOfWorksDetail);
        while (frame == null) {
            curIndexOfWorksDetail++;
            if (curIndexOfWorksDetail >= worksDetailDTOS.size()) {
                break;
            }
            curVideoClipDTO = worksDetailDTOS.get(curIndexOfWorksDetail).getVideoClipDTO();
            start(worksDetailDTOS.get(curIndexOfWorksDetail));
            frame = videoClipGrabber.grabImage();
        }
        return frame;
    }

    @Override
    public Frame grabSamples() {
        try {
            Frame leftFrame = videoClipGrabber.grabSamples();
            while (leftFrame == null) {
                curIndexOfWorksDetail++;
                if (curIndexOfWorksDetail >= worksDetailDTOS.size()) {
                    break;
                }
                curVideoClipDTO = worksDetailDTOS.get(curIndexOfWorksDetail).getVideoClipDTO();
                start(worksDetailDTOS.get(curIndexOfWorksDetail));
                leftFrame = videoClipGrabber.grabSamples();
            }
            if(leftFrame == null) {
                return null;
            }
            return leftFrame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabMusicSamples() {
        try {
            return musicGrabber.grabSamples();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public int getImageWidth() {
        return videoClipGrabber.getImageWidth();
    }

    @Override
    public int getImageHeight() {
        return videoClipGrabber.getImageHeight();
    }

    @Override
    public void setImageWidth(int imageWidth) {
        videoClipGrabber.setImageWidth(imageWidth);
    }

    @Override
    public void setImageHeight(int imageHeight) {
        videoClipGrabber.setImageHeight(imageHeight);
    }

    @Override
    public long getStartTs() {
        return 0;
    }

    @Override
    public Frame grabVideoAndSamples() {
        Frame frame = videoClipGrabber.grabVideoAndSamples();
        while (frame == null) {
            curIndexOfWorksDetail++;
            if (curIndexOfWorksDetail >= worksDetailDTOS.size()) {
                break;
            }
            curVideoClipDTO = worksDetailDTOS.get(curIndexOfWorksDetail).getVideoClipDTO();
            start(worksDetailDTOS.get(curIndexOfWorksDetail));
            frame = videoClipGrabber.grabVideoAndSamples();
        }
        return frame;
    }

    @Override
    public void setTimestamp(Long timestamp, Boolean isVideo) {
    }

    @Override
    public void setImageRate(int rate) {

    }

    @Override
    public void start() {
        try {
            List<WorksDetailDTO> worksDetailDTOS = worksDTO.getDetails();
            curIndexOfWorksDetail = 0;
            curVideoClipDTO = worksDetailDTOS.get(0).getVideoClipDTO();
            start(worksDetailDTOS.get(0));
            MusicDTO musicDTO = worksDTO.getMusicDTO();
            musicGrabber = new FFmpegFrameGrabber(musicDTO.getPath());
            musicGrabber.setSampleRate(44100);
            musicGrabber.start();
        }catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void start(WorksDetailDTO worksDetailDTO) {
        try {
            if(ItemTypeEnum.Live.getValue().equals(worksDTO.getProjectDTO().getItemType())){
                videoClipGrabber = new LiveVideoClipGrabber();
            }
            else{
                videoClipGrabber = new VideoClipGrabber();
            }
            videoClipGrabber.setVideoOrAudio(this.getVideoOrAudio().getCode());
            videoClipGrabber.setVideoClipDTO(worksDetailDTO.getVideoClipDTO());
            videoClipGrabber.start();
            videoClipGrabber.setMaxCutPercent(worksDetailDTO.getCutPercent());
            videoClipGrabber.setFilter(new BaseFilter(worksDTO.getFilterEffectDTO()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stop() {

    }

    @Override
    public double getFrameRate() {
        return videoClipGrabber.getFrameRate();
    }

    @Override
    public int getSampleRate() {
//        return videoClipGrabber.getSampleRate() + musicGrabber.getSampleRate();
        return videoClipGrabber.getSampleRate();

    }

    @Override
    public int getVideoBitrate() {
        return videoClipGrabber.getVideoBitrate();
    }

    @Override
    public void close() {
        try {
            videoClipGrabber.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getBaseTime() {
        return 0;
    }

    @Override
    public long getCurSubtitlesId() {
        return videoClipGrabber.getCurSubtitlesId();
    }

    @Override
    public int getAudioBitrate() {
        return 0;
    }

    @Override
    public int getChannels() {
        return videoClipGrabber.getChannels();
    }

    public void setWorksDTO(WorksDTO worksDTO) {
        this.worksDTO = worksDTO;
        this.worksDetailDTOS = worksDTO.getDetails();
    }

    public boolean isCurFrameStartOfClip() {
        return videoClipGrabber.isCurFrameStartOfClip();
    }

    private Frame mergeSamples(Frame leftFrame, Frame rightFrame) {
        if(leftFrame == null || rightFrame == null) {
            return null;
        }
        try {
            FFmpegLogCallback.set();
            // 音频滤镜
            String audioFilter = "[0:a][1:a]amix=inputs=2[a]";
            FFmpegFrameFilter filter = new FFmpegFrameFilter(audioFilter, 1);
            filter.setAudioInputs(2);
            // 设置 音频比特率 (注意这个要一致，否则可能合并的音频过快或过慢)
            filter.setSampleRate(leftFrame.sampleRate);
            filter.start();
            filter.push(0, leftFrame);
            filter.push(1, rightFrame);
            Frame pull = filter.pull();
            return pull;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
