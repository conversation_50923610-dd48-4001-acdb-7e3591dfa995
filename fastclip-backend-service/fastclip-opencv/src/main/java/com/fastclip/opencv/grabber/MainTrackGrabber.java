package com.fastclip.opencv.grabber;

import com.fastclip.common.model.dto.track.Track;
import com.fastclip.common.model.dto.track.element.TrackElement;

import java.awt.*;

public class MainTrackGrabber extends TrackGrabber{

    public MainTrackGrabber(Track track) {
        super(track);
    }

    /**
     * 获取下一个图像帧
     * @return
     */
    public Frame grabImageFrame(){
        TrackElement trackElement = this.curTrackElement;
        return null;
    }

    /**
     * 获取下一个音频帧
     * @return
     */
    public Frame grabAudioFrame(){
        return null;
    }

}
