package com.fastclip.opencv.grabber;

import org.bytedeco.javacv.*;


import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;


public class FFmpegUtils {
    public static byte[] getAudios(String videoPath, Long start, Long end) throws FFmpegFrameGrabber.Exception, FFmpegFrameRecorder.Exception {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputStream, 1);
            recorder.setFormat("mp3");
            recorder.start();
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoPath);
            grabber.start();
            Frame frame = grabber.grabSamples();
            if(frame == null) {
                return null;
            }
            long firstFrameTime = frame.timestamp;
            grabber.restart();
            grabber.setTimestamp(start-firstFrameTime);
            frame = grabber.grabSamples();
            while (frame != null) {
                if(frame.timestamp>end) {
                    break;
                }else if (frame.timestamp<=end && frame.timestamp>=start) {
                    recorder.record(frame);
                }
                frame = grabber.grabSamples();
            }
            grabber.close();
            recorder.close();
            return outputStream.toByteArray();
        }catch(Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] getAudios(Frame frame) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputStream, 1);
            recorder.setFormat("mp3");
            recorder.start();
            if(frame == null) {
                return null;
            }
            if(frame.imageWidth ==0  && frame.imageHeight ==0 ) {
                recorder.record(frame);
            }

            recorder.close();
            return outputStream.toByteArray();
        }catch(Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static byte[]  encodeImageFrame(Frame frame) {
        // 将byte数组转换为ByteBuffer
        Java2DFrameConverter java2DFrameConverter = new Java2DFrameConverter();
        BufferedImage bufferedImage = java2DFrameConverter.getBufferedImage(frame);
        return imageToBytes(bufferedImage, "jpg");
    }

    /**
     * 图片转字节数组
     *
     * @param bImage 图片数据
     * @param format 格式
     * @return 图片字节码
     */
    private static byte[] imageToBytes(BufferedImage bImage, String format) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            ImageIO.write(bImage, format, out);
        } catch (IOException e) {
            return null;
        }
        return out.toByteArray();
    }
}
