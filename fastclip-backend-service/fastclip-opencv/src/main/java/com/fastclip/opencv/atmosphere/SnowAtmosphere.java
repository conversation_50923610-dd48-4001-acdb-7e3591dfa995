package com.fastclip.opencv.atmosphere;


import org.bytedeco.javacpp.BytePointer;
import org.bytedeco.javacpp.indexer.UByteRawIndexer;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.RNG;
import org.bytedeco.opencv.opencv_core.Rect;
import org.bytedeco.opencv.opencv_core.Scalar;

import static org.bytedeco.opencv.global.opencv_core.CV_8UC3;

public class SnowAtmosphere implements Atmosphere{

    @Override
    public Mat process(Mat img) {
        int height = img.rows();
        int width = img.cols();

        // 获取 Frame 的 UByteRawIndexer
        UByteRawIndexer indexer = img.createIndexer();

        // 添加雪花特效
        for (int i = 0; i < 100; i++) {
            int x = (int) (Math.random() * width);
            int y = (int) (Math.random() * height);
            drawSnow(img, x, y, 2, width, height);
        }
        return img;
    }

    private void drawSnow(Mat img, int x, int y, int s, int width, int height) {
        int startX = Math.max(0, x-s);
        int startY = Math.max(0, y-s);
        int endX = Math.min(x+s, width);
        int endY = Math.min(y+s, height);
        for(int i = startX; i <= endX; i++) {
            for(int j = startY; j <= endY; j++) {
                if(((i -x)*(i-x) + (j-y)*(j-y)) <= (s * s)) {
                    BytePointer ptr = img.ptr(i, j);
                    ptr.put((byte) 255, (byte) 255, (byte)255).put((byte)220);
                }
            }
        }
    }
}
