package com.fastclip.opencv.grabber;

import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.dto.track.Track;
import com.fastclip.common.model.dto.track.element.TrackElement;
import org.springframework.util.CollectionUtils;

import java.awt.*;
import java.util.List;

public class TrackGrabber {

    Track track;

    VideoOrAudioTypeEnum videoOrAudio;

    List<TrackElement> elementList;

    TrackElement curTrackElement;

    Integer curElementIndex;

    Integer elementSize;

    Integer duration;

    public TrackGrabber(Track track) {
        this.track = track;
        this.elementList = track.getTrackElementList();
        if(CollectionUtils.isEmpty(track.getTrackElementList())){
            elementSize = 0;
        }else {
            elementSize = track.getTrackElementList().size();
        }
    }

    public void start(){
        curElementIndex = 0;
        if(CollectionUtils.isEmpty(elementList)) {
            curTrackElement = null;
        }else {
            curTrackElement = elementList.get(0);
        }
    }

    public void restart(){
        curElementIndex = 0;
        if(CollectionUtils.isEmpty(elementList)) {
            curTrackElement = null;
        }else {
            curTrackElement = elementList.get(0);
        }
    }

    public boolean isMainTrack(){
        return track.getIsMain();
    }

    /**
     * 根据时间获取当前图像帧
     * @param startTs
     * @return
     */
    public Frame grabImageFrame(Integer  startTs){
        return null;
    }

    /**
     * 根据时间获取当前音频帧
     * @param startTs
     * @return
     */
    public Frame grabAudioFrame(Integer startTs){
        return null;
    }
}
