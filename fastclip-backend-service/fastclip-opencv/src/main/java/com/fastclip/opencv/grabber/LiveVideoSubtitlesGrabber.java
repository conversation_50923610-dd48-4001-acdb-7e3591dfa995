package com.fastclip.opencv.grabber;

import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.dto.SubtitlesDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.model.dto.VideoMaterialSliceDTO;
import com.fastclip.dao.model.dataobject.VideoMaterialSlice;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class LiveVideoSubtitlesGrabber extends VideoSubtitlesGrabber {

    SubtitlesDTO subtitlesDTO;

    VideoMaterialDTO videoMaterialDTO;

    FFmpegFrameGrabber grabber;

    Integer curSliceNumber = 0;

    List<VideoMaterialSliceDTO> videoMaterialSliceDTOs;

    VideoMaterialSliceDTO curVideoMaterialSliceDTO;

    Long firstVideoFrameTime = 0L;

    Long firstAudioFrameTime = 0L;

    @Override
    public byte[] grabRange(Integer startTs, Integer endTs) {
        try {
            Integer originStartTs = startTs + subtitlesDTO.getStartTs();
            Integer originEndTs = endTs + subtitlesDTO.getStartTs();
            return FFmpegUtils.getAudios(videoMaterialDTO.getPath(), originStartTs * 1000L, originEndTs * 1000L);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabImage() {
        try {
            System.out.println(" curSliceNumber=" + curSliceNumber);
            Frame frame = grabber.grabImage();
            if(frame == null) {
                curSliceNumber++;
                if(curSliceNumber >= videoMaterialSliceDTOs.size()) {
                    return null;
                }
                setVideoMaterialSlice(videoMaterialSliceDTOs.get(curSliceNumber));
                frame = grabber.grabImage();
            }
            System.out.println("frameTime=" + frame.timestamp + " curSliceNumber=" + curSliceNumber);
            frame.timestamp = curVideoMaterialSliceDTO.getStartTs() * 1000000 + frame.timestamp;
            return frame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabSamples() {
        try {
            Frame frame = grabber.grabSamples();
            if(frame == null || frame.timestamp * 1000L > subtitlesDTO.getEndTs()) {
                return null;
            }
//            if(frame == null) {
//                curSliceNumber++;
//                if(curSliceNumber >= videoMaterialSliceDTOs.size()) {
//                    return null;
//                }
//                setVideoMaterialSlice(videoMaterialSliceDTOs.get(curSliceNumber));
//                frame = grabber.grabSamples();
//            }
            frame.timestamp = curVideoMaterialSliceDTO.getStartTs() * 1000000 + frame.timestamp;
            return frame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabMusicSamples() {
        return null;
    }

    @Override
    public int getImageWidth() {
        return grabber.getImageWidth();
    }

    @Override
    public int getImageHeight() {
        return grabber.getImageHeight();
    }

    @Override
    public void setImageWidth(int imageWidth) {
        grabber.setImageWidth(imageWidth);
    }

    @Override
    public void setImageHeight(int imageHeight) {
        grabber.setImageHeight(imageHeight);
    }

    @Override
    public long getStartTs() {
        return subtitlesDTO.getStartTs() * 1000L;
    }

    @Override
    public Frame grabVideoAndSamples() {
        try {
            return grabber.grab();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void setTimestamp(Long timestamp, Boolean isVideo) {
    }

    @Override
    public void setVideoTimestamp(Long timestamp) {
        setGrabberVideoTimestamp(timestamp);
    }

    @Override
    public void setImageRate(int rate) {
        grabber.setFrameRate(rate);
    }

    @Override
    public void start() {
        try {
            if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(getVideoOrAudio().getCode())) {
                Frame firstAudioFrame = grabber.grabSamples();
                firstAudioFrameTime = firstAudioFrame.timestamp;
                grabber.restart();
                Frame firstVideoFrame = grabber.grabImage();
                firstVideoFrameTime = firstVideoFrame.timestamp;
                grabber.restart();
                this.setGrabberVideoTimestamp(subtitlesDTO.getStartTs() * 1000L - curVideoMaterialSliceDTO.getStartTs() * 1000000L);
            }else {
                Frame firstFrame = grabber.grabSamples();
                long firstTimestamp = firstFrame.timestamp;
                grabber.restart();
                grabber.setAudioTimestamp(subtitlesDTO.getStartTs() * 1000L - firstTimestamp - curVideoMaterialSliceDTO.getStartTs() * 1000000L);
            }

        }catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void setGrabberVideoTimestamp(Long timestamp) {
        try {
            Long timeForEveryFrame = (long)(1000000/getFrameRate());
            Long stepFrameTime = timeForEveryFrame;
            if(firstVideoFrameTime > 0) {
                stepFrameTime = firstVideoFrameTime;
            }
            grabber.setVideoTimestamp(timestamp - stepFrameTime);
            Frame frame = grabber.grabImage();
            int i = 1;
            while(frame.timestamp > timestamp) {
                i++;
                grabber.restart();
                if(timestamp - i*firstVideoFrameTime > 0) {
                    grabber.setVideoTimestamp(timestamp - i * stepFrameTime);
                }else{
                    break;
                }
                frame = grabber.grabImage();
            }
            while(frame.timestamp < (timestamp-timeForEveryFrame)) {
                frame = grabber.grabImage();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stop() {
        try {
            grabber.stop();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public double getFrameRate() {
        if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(getVideoOrAudio().getCode())) {
            return grabber.getFrameRate();
        }else{
            return 1000000/(grabber.getLengthInTime()/grabber.getLengthInAudioFrames());
        }
    }

    @Override
    public int getSampleRate() {
        return grabber.getSampleRate();
    }

    @Override
    public int getVideoBitrate() {
        return grabber.getVideoBitrate();
    }

    @Override
    public void close() {
        try {
            grabber.close();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getBaseTime() {
        return videoMaterialDTO.getStartTime();
    }

    public void setSubtitlesDTO(SubtitlesDTO subtitlesDTO) {
        this.subtitlesDTO = subtitlesDTO;
        setCurVideoMaterialSliceDTO(subtitlesDTO.getStartTs());
    }

    private void setCurVideoMaterialSliceDTO(int startTs){
        for(int i=0; i<videoMaterialSliceDTOs.size()-1; i++ ) {
            if(videoMaterialSliceDTOs.get(i).getStartTs() * 1000<=startTs && videoMaterialSliceDTOs.get(i+1).getStartTs() *1000>= startTs) {
                curSliceNumber = i;
                setVideoMaterialSlice(videoMaterialSliceDTOs.get(i));
                break;
            }
        }
    }

    public void setVideoMaterialDTO(VideoMaterialDTO videoMaterialDTO) {
        this.videoMaterialDTO = videoMaterialDTO;
        this.videoMaterialSliceDTOs = videoMaterialDTO.getVideoMaterialSliceDTOS();
    }

    public void setVideoMaterialSlice(VideoMaterialSliceDTO videoMaterialSliceDTO) {
        this.curVideoMaterialSliceDTO = videoMaterialSliceDTO;
        try {
            grabber = new FFmpegFrameGrabber(videoMaterialSliceDTO.getPath());
            grabber.start();
            int width = grabber.getImageWidth();
            int height = grabber.getImageHeight();
            int newHeight = 0;
            if(getIsPlay()) {
                grabber.setImageWidth(480);
                newHeight = 480 * height / width;
            }else{
                grabber.setImageWidth(1920);
                newHeight = 1920 * height / width;
            }
            grabber.setImageHeight( (newHeight%2 == 0)?newHeight:(newHeight + 1));
            start();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
}
