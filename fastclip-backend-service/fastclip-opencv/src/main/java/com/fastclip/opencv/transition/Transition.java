package com.fastclip.opencv.transition;

import org.bytedeco.javacpp.Loader;
import org.bytedeco.javacv.*;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Rect;
import org.bytedeco.opencv.opencv_core.Size;
import org.bytedeco.opencv.opencv_java;


public class Transition{
    public static void main(String[] args) {
        Loader.load(opencv_java.class);
        try {
            // 初始化FFmpegFrameGrabber来读取视频文件
            FFmpegFrameGrabber grabber1 = new FFmpegFrameGrabber("/Users/<USER>/Downloads/1.mp4");
            grabber1.start();

            FFmpegFrameGrabber grabber2 = new FFmpegFrameGrabber("/Users/<USER>/Downloads/2.mp4");
            grabber2.start();

            // 创建OpenCVFrameConverter来转换帧
            OpenCVFrameConverter.ToMat converter = new OpenCVFrameConverter.ToMat();

            // 读取第一个视频的第一帧
            Frame frame1 = grabber1.grabFrame();
            Mat mat1 = converter.convertToMat(frame1);
            while(mat1 == null && frame1 != null) {
                frame1 = grabber1.grabFrame();
                mat1 = converter.convert(frame1);
            }
            // 读取第二个视频的第一帧
            Frame frame2 = grabber2.grabFrame();
            Mat mat2 = converter.convert(frame2);
            while(mat2 == null && frame2 != null) {
                frame2 = grabber1.grabFrame();
                mat2 = converter.convert(frame2);
            }

            // 应用转场效果
            Mat transitionMat = applyTransitionEffect(mat1, mat2);

            // 将转场后的帧转换回Frame格式以写入输出视频
            Frame transitionFrame = converter.convert(transitionMat);

            // 使用FFmpegFrameRecorder来写入输出视频
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder("/Users/<USER>/Downloads/output.mp4", 1024, 1280);
            recorder.setFormat("mp4");
            recorder.start();

            // 写入转场后的帧
            recorder.record(transitionFrame);

            // 释放资源
            recorder.stop();
            recorder.release();
            grabber1.stop();
            grabber2.stop();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Mat applyTransitionEffect(Mat firstFrame, Mat secondFrame) {
        Mat transitionMat = new Mat(new Size(firstFrame.size().width(), firstFrame.size().height()), firstFrame.type());
        // 这里可以实现各种转场效果，例如淡入淡出，渐变，渐变加渐动等
        // 简单的淡入淡出示例：
        // 第一帧全图显示
        firstFrame.copyTo(transitionMat);
        // 第二帧从上到下逐渐显示
        for (int i = 0; i < secondFrame.size().height(); i++) {
            Rect rect = new Rect(0, i, secondFrame.size().width(), 1);
            Mat subMat = new Mat(transitionMat, rect);
            subMat.setTo(secondFrame.apply(new Rect(0, secondFrame.size().height() - i - 1, secondFrame.size().width(), 1)));
        }
        return transitionMat;
    }
}