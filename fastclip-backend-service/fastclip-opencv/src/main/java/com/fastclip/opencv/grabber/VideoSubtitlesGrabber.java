package com.fastclip.opencv.grabber;

import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.dto.SubtitlesDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;

public class VideoSubtitlesGrabber extends Grabber {

    SubtitlesDTO subtitlesDTO;

    VideoMaterialDTO videoMaterialDTO;

    FFmpegFrameGrabber grabber;

    Long firstVideoFrameTime = 0L;

    Long firstAudioFrameTime = 0L;

    @Override
    public byte[] grabRange(Integer startTs, Integer endTs) {
        try {
            Integer originStartTs = startTs + subtitlesDTO.getStartTs();
            Integer originEndTs = endTs + subtitlesDTO.getStartTs();
            return FFmpegUtils.getAudios(videoMaterialDTO.getPath(), originStartTs * 1000L, originEndTs * 1000L);

        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabImage() {
        try {
            return grabber.grabImage();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabSamples() {
        try {
            return grabber.grabSamples();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabMusicSamples() {
        return null;
    }

    @Override
    public int getImageWidth() {
        return grabber.getImageWidth();
    }

    @Override
    public int getImageHeight() {
        return grabber.getImageHeight();
    }

    @Override
    public void setImageWidth(int imageWidth) {
        grabber.setImageWidth(imageWidth);
    }

    @Override
    public void setImageHeight(int imageHeight) {
        grabber.setImageHeight(imageHeight);
    }

    @Override
    public long getStartTs() {
        return subtitlesDTO.getStartTs() * 1000L;
    }

    @Override
    public Frame grabVideoAndSamples() {
        try {
            return grabber.grab();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void setTimestamp(Long timestamp, Boolean isVideo) {
    }

    @Override
    public void setImageRate(int rate) {
        grabber.setFrameRate(16);
    }

    @Override
    public void start() {
        try {
            grabber.start();
            if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(getVideoOrAudio().getCode())) {
                int width = grabber.getImageWidth();
                int height = grabber.getImageHeight();
                grabber.setImageWidth(480);
                grabber.setImageHeight(480 * height/width);
                Frame firstAudioFrame = grabber.grabSamples();
                firstAudioFrameTime = firstAudioFrame.timestamp;
                grabber.restart();
                Frame firstVideoFrame = grabber.grabImage();
                firstVideoFrameTime = firstVideoFrame.timestamp;
                grabber.restart();
                this.setGrabberVideoTimestamp(subtitlesDTO.getStartTs() * 1000L);
            }else {
                Frame firstFrame = grabber.grabSamples();
                long firstTimestamp = firstFrame.timestamp;
                grabber.restart();
                grabber.setAudioTimestamp(subtitlesDTO.getStartTs() * 1000L - firstTimestamp);
            }

        }catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void setGrabberVideoTimestamp(Long timestamp) {
        try {
            Long timeForEveryFrame = (long)(1000000/getFrameRate());
            grabber.setVideoTimestamp(timestamp - firstVideoFrameTime);
            Frame frame = grabber.grabImage();
            int i = 1;
            long preTimestamp = 0;
            while(frame.timestamp > timestamp && frame.timestamp>preTimestamp) {
                i++;
                grabber.restart();
                if(timestamp - i*firstVideoFrameTime > 0) {
                    grabber.setVideoTimestamp(timestamp - i * firstVideoFrameTime);
                }
                preTimestamp = frame.timestamp;
                frame = grabber.grabImage();
            }
            while(frame.timestamp < (timestamp-timeForEveryFrame)) {
                grabber.grabImage();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stop() {
        try {
            grabber.stop();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public double getFrameRate() {
        if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(getVideoOrAudio().getCode())) {
            return grabber.getFrameRate();
        }else{
            return 1000000/(grabber.getLengthInTime()/grabber.getLengthInAudioFrames());
        }
    }

    @Override
    public int getSampleRate() {
        return grabber.getSampleRate();
    }

    @Override
    public int getVideoBitrate() {
        return grabber.getVideoBitrate();
    }

    @Override
    public void close() {
        try {
            grabber.close();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getBaseTime() {
        return videoMaterialDTO.getStartTime();
    }

    @Override
    public long getCurSubtitlesId() {
        return subtitlesDTO.getId();
    }

    @Override
    public int getAudioBitrate() {
        return grabber.getAudioBitrate();
    }

    @Override
    public int getChannels() {
        return grabber.getAudioChannels();
    }

    public void setSubtitlesDTO(SubtitlesDTO subtitlesDTO) {
        this.subtitlesDTO = subtitlesDTO;
    }

    public void setVideoMaterialDTO(VideoMaterialDTO videoMaterialDTO) {
        this.videoMaterialDTO = videoMaterialDTO;
        grabber = new FFmpegFrameGrabber(videoMaterialDTO.getPath());
    }
}
