package com.fastclip.opencv.grabber;

import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import org.bytedeco.javacv.Frame;

import java.util.List;
public abstract class Grabber {

    private VideoOrAudioTypeEnum videoOrAudio;

    private Long videoTimestamp = 0L;

    private Long audioTimestamp = 0L;

    private Long id;

    private Boolean isPlay = false;

    private MainTrackGrabber mainTrackGrabber;

    private List<TrackGrabber> trackGrabbers;

    public Long getId(){
        return id;
    }

    public void setVideoOrAudio(String code) {
        videoOrAudio = VideoOrAudioTypeEnum.createVideoOrAudioTypeEnum(code);
    }

    public VideoOrAudioTypeEnum getVideoOrAudio() {
        return videoOrAudio;
    }

    public abstract byte[] grabRange(Integer startTs, Integer endTs);

    public abstract Frame grabImage();

    public void setIsPlay(Boolean isPlay) {
        this.isPlay = isPlay;
    }

    public boolean getIsPlay() {
        return this.isPlay;
    }

    public abstract Frame grabSamples();

    public abstract Frame grabMusicSamples();

    public abstract int getImageWidth();

    public abstract int getImageHeight();

    public abstract void setImageWidth(int imageWidth);

    public abstract void setImageHeight(int imageHeight);

    public abstract long getStartTs();

    public abstract Frame grabVideoAndSamples();

    public Frame grab() {
        if(getVideoOrAudio() == null) {
            return grabVideoAndSamples();
        }
        if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(getVideoOrAudio().getCode())) {
            return grabImage();
        }
        return grabSamples();
    }

    public Long getAudioTimestamp() {
        return audioTimestamp;
    }

    public Long getVideoTimestamp() {
        return videoTimestamp;
    }

    public void setAudioTimestamp(Long audioTimestamp) {
        this.audioTimestamp = audioTimestamp;
    }

    public void setVideoTimestamp(Long videoTimestamp) {
        this.videoTimestamp = videoTimestamp;
    }

    public abstract void setTimestamp(Long timestamp, Boolean isVideo);

    public abstract void setImageRate(int rate);

    public abstract void start();

    public abstract void stop();

    public abstract double getFrameRate();

    public abstract int getSampleRate();

    public abstract int getVideoBitrate();

    public abstract void close();

    public abstract int getBaseTime();

    //获取当前正在读取的字幕视频片段id
    public abstract long getCurSubtitlesId();

    public abstract int getAudioBitrate();

    public abstract  int getChannels();

}
