package com.fastclip.opencv.utils;

import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.opencv.grabber.FrameWithSpecialEffectParams;
import org.bytedeco.javacv.*;
import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Rect;
import org.bytedeco.opencv.opencv_core.Size;
import sun.misc.BASE64Decoder;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.*;

import static org.bytedeco.opencv.global.opencv_core.flip;
import static org.bytedeco.opencv.global.opencv_highgui.imshow;
import static org.bytedeco.opencv.global.opencv_highgui.waitKey;
import static org.bytedeco.opencv.global.opencv_imgcodecs.*;
import static org.bytedeco.opencv.global.opencv_imgproc.*;

public class FrameUtils {

    static OpenCVFrameConverter.ToMat openCVConverter = new OpenCVFrameConverter.ToMat();

    public static ThreadPoolExecutor threadPoolExecutor =
            new ThreadPoolExecutor(100, 100, 20,
                    TimeUnit.SECONDS, new LinkedBlockingDeque<>(100));


    public static Frame processFrameWithSpecialEffect(FrameWithSpecialEffectParams params) {
        Mat img = params.getFrame();
        Double percent = params.getCutPercent();
        int width = img.cols();
        int height = img.rows();
        int cropWidth = width - (int)(width * percent / 100);
        int cropHeight = height - (int)(height * percent /100);
        int x =  (width - cropWidth) / 2;
        int y = (height - cropHeight) / 2;
        Mat flippedMat = new Mat();
        flip(img, flippedMat, 1);
        Mat cropMat = new Mat(flippedMat, new Rect(x, y, cropWidth, cropHeight));
        Mat resizedMat = new Mat();
        resize(cropMat, resizedMat, new Size(width, height), 0,0, INTER_NEAREST);
        img.release();
        cropMat.release();
        flippedMat.release();
        Frame frame = openCVConverter.convert(resizedMat);
        resizedMat.release();
        return frame;
    }


    public static Frame putText(Mat mat, String sellerName, String content, int x, int y, int fontSize) {
        OpenCVFrameConverter.ToMat openCVConverter = new OpenCVFrameConverter.ToMat();
        Java2DFrameConverter java2DConverter = new Java2DFrameConverter();
        BufferedImage bufferedImage = java2DConverter.convert(openCVConverter.convert(mat));

        Font font = new Font("宋体", Font.BOLD, fontSize);
        Graphics2D g2d = bufferedImage.createGraphics();
        g2d.setFont(font);
        g2d.setColor(new Color(255, 255, 255));
        int maxNumOfTextPerRow = mat.cols()/fontSize;
        g2d.drawString("【" + sellerName + "穿搭分享】", 20, y - fontSize - 10);
        if(x > 0) {
            g2d.drawString(content, x, y);
        }else{
            String tmpSubText = content;
            int rowOfText = 0;
            while(tmpSubText.length() > maxNumOfTextPerRow) {
                g2d.drawString(tmpSubText.substring(0,maxNumOfTextPerRow), 0, y + rowOfText * (fontSize + 5));
                rowOfText ++;
                tmpSubText = tmpSubText.substring(maxNumOfTextPerRow);
            }
            g2d.drawString(tmpSubText, (mat.cols() - tmpSubText.length()*fontSize)/2, y + rowOfText * (fontSize + 5));
        }
        g2d.dispose();
        return java2DConverter.convert(bufferedImage);
    }

    public static Frame putTextAndCoverContent(Mat baseContent, Mat coverContent, String sellerName, String itemName,
                                               String itemType, String itemTag) {
        int baseWidth = 1242;
        int baseHeight = 1656;
        OpenCVFrameConverter.ToMat openCVConverter = new OpenCVFrameConverter.ToMat();
        Java2DFrameConverter java2DConverter = new Java2DFrameConverter();
        BufferedImage baseImage = java2DConverter.convert(openCVConverter.convert(baseContent));
        Graphics2D g2d = baseImage.createGraphics();

        Mat resizedMat = new Mat();
        resize(coverContent, resizedMat, new Size(baseWidth - 160, baseHeight - 160), 0,0, INTER_NEAREST);
        BufferedImage coverImage = java2DConverter.convert(openCVConverter.convert(resizedMat));
        g2d.drawImage(coverImage, 150, 150, null);

        Font font = new Font("ZHIZUN", Font.ITALIC, 100);
        g2d.setFont(font);
        g2d.setColor(new Color(255, 255, 255));
        g2d.drawString(sellerName , 20, 120);

        font = new Font("ZHIZUN", Font.PLAIN, 70);
        g2d.setColor(new Color(122, 122, 122));
        g2d.setFont(font);
        for(int i=0; i<itemName.length(); i++) {
            String s = itemName.substring(i,i+1);
            g2d.drawString(s, 40, 250 + (i * 70)+5);
        }

        font = new Font("Serif", Font.ITALIC, 50);
        g2d.setFont(font);
        g2d.setColor(new Color(128, 64, 0));
        g2d.drawString("【" + itemType + "】", 100 * (sellerName.length() + 2), 120);

        font = new Font("Serif", Font.BOLD, 70);
        g2d.setFont(font);
        g2d.setColor(new Color(255, 255, 255));
        int x =  baseWidth - itemTag.length() * 70 - 150;
        g2d.drawString(itemTag,  (Math.min(x, 100))/2 + 150, baseHeight -150);
        g2d.dispose();
        return java2DConverter.convert(baseImage);
    }


    public static String createWorksCover(String backgroundPath, String coverBasePath, String projectCover, WorksDTO worksDTO) throws FFmpegFrameRecorder.Exception {
        String coverPath = coverBasePath + "/" + worksDTO.getCreator().getUserName() + "_" +
                worksDTO.getSeller().getSellerName() + "_" + worksDTO.getProjectId() + "_" + worksDTO.getId() + ".jpg";

        try {// 读取图像
            Mat backgroundImage = imread(backgroundPath);
            Mat baseCoverImage = imread(projectCover);
            // 检查图像是否成功加载
            if (baseCoverImage.empty()) {
                System.err.println("Error loading image");
                return null;
            }
            Frame frame = putTextAndCoverContent(backgroundImage, baseCoverImage, worksDTO.getSeller().getSellerName(),
                    worksDTO.getSimpleItemName(), worksDTO.getItemFeatures(), worksDTO.getItemTags());
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(coverPath, frame.imageWidth, frame.imageHeight, 0);
            recorder.setFormat("jpg");
            recorder.start();
            recorder.record(frame);
            recorder.stop();
            recorder.release();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return coverPath;
    }

    public static String convertBase64ToFile(String fileBase64String, String filePath, String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file;
        try {
            File dir = new File(filePath);
            if (!dir.exists() && dir.isDirectory()) {//判断文件目录是否存在
                dir.mkdirs();
            }
            BASE64Decoder decoder = new BASE64Decoder();
            byte[] bfile = decoder.decodeBuffer(fileBase64String);
            String fullPath = filePath + File.separator + fileName;
            file = new File(fullPath);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bfile);
            return fullPath;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return null;
    }

//    public static List<Frame> processFramesWithSpecialEffect(List<FrameWithSpecialEffectParams> frameWithParams) {
//       try {
//           Frame[] newFrames = new Frame[frameWithParams.size()];
//           List<Future<Frame>> futures=  new ArrayList<>();
//           for (int i = 0; i < frameWithParams.size(); i++) {
//               FrameWithSpecialEffectParams frame = frameWithParams.get(i);
//               Callable<Frame> task = () -> {
//                   return processFrameWithSpecialEffect(frame.getFrame(), frame.getCutPercent(), frame.getSubtitiles());
//               };
//               Future<Frame> future = threadPoolExecutor.submit(task);
//               futures.add(future);
//           }
//           for(int i=0; i< futures.size(); i++) {
//               newFrames[i] = futures.get(i).get();
//           }
//           return Arrays.stream(newFrames).collect(Collectors.toList());
//       }catch (Exception e) {
//           e.printStackTrace();
//       }
//        return new ArrayList<>();
//    }

    public static void main(String[] args) {
        for(int i=1; i<=4; i++) {
            try {
                // 图像文件路径
                String basePath = "/Users/<USER>/Documents/douyin/cover/baseCover/base_cover" + i +".jpg";
                String imagePath = "/Users/<USER>/Documents/douyin/cover/雪儿.jpg";
                Mat baseIamge = imread(basePath);
                Mat coverContent = imread(imagePath);
                Frame frame = FrameUtils.putTextAndCoverContent(baseIamge, coverContent, "雪儿",
                        "时尚牛仔帆布单肩休闲百搭潮流包", "时尚休闲", "牛仔帆布 单肩休闲 百搭潮流");
                FFmpegFrameRecorder recorder = new FFmpegFrameRecorder("/Users/<USER>/Documents/douyin/cover/雪儿_test" + i +".jpg", frame.imageWidth, frame.imageHeight, 0);
                recorder.setFormat("jpg");
                recorder.start();
                recorder.record(frame);
                recorder.stop();
                recorder.release();

                basePath = "/Users/<USER>/Documents/douyin/cover/baseCover/base_cover" + i +".jpg";
                baseIamge = imread(basePath);
                imagePath = "/Users/<USER>/Documents/douyin/cover/yoyo.jpg";
                coverContent = imread(imagePath);
                frame = FrameUtils.putTextAndCoverContent(baseIamge, coverContent, "yoyo",
                        "时尚牛仔帆布单肩休闲百搭潮流包", "时尚休闲", "牛仔帆布 单肩休闲 百搭潮流");
                recorder = new FFmpegFrameRecorder("/Users/<USER>/Documents/douyin/cover/yoyo_test" + i +".jpg", frame.imageWidth, frame.imageHeight, 0);
                recorder.setFormat("jpg");
                recorder.start();
                recorder.record(frame);
                recorder.stop();
                recorder.release();

                basePath = "/Users/<USER>/Documents/douyin/cover/baseCover/base_cover" + i +".jpg";
                baseIamge = imread(basePath);
                imagePath = "/Users/<USER>/Documents/douyin/cover/浩哥.jpg";
                coverContent = imread(imagePath);
                frame = FrameUtils.putTextAndCoverContent(baseIamge, coverContent, "浩哥",
                        "时尚牛仔帆布单肩休闲百搭潮流包", "时尚休闲", "牛仔帆布 单肩休闲 百搭潮流");
                recorder = new FFmpegFrameRecorder("/Users/<USER>/Documents/douyin/cover/浩哥_test" + i +".jpg", frame.imageWidth, frame.imageHeight, 0);
                recorder.setFormat("jpg");
                recorder.start();
                recorder.record(frame);
                recorder.stop();
                recorder.release();

                basePath = "/Users/<USER>/Documents/douyin/cover/baseCover/base_cover" + i +".jpg";
                baseIamge = imread(basePath);
                imagePath = "/Users/<USER>/Documents/douyin/cover/邱莹莹.jpg";
                coverContent = imread(imagePath);
                frame = FrameUtils.putTextAndCoverContent(baseIamge, coverContent, "邱莹莹",
                        "时尚牛仔帆布单肩休闲百搭潮流包", "时尚休闲", "牛仔帆布 单肩休闲 百搭潮流");
                recorder = new FFmpegFrameRecorder("/Users/<USER>/Documents/douyin/cover/邱莹莹_test" + i +".jpg", frame.imageWidth, frame.imageHeight, 0);
                recorder.setFormat("jpg");
                recorder.start();
                recorder.record(frame);
                recorder.stop();
                recorder.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
