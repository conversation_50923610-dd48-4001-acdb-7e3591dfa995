package com.fastclip.opencv.player;

import com.fastclip.common.model.dto.track.Track;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 作品播放器
 */

@Service
public class WorksVideoPlayer extends FFmpegVideoPlayer{

    private List<Track> trackList;

    @Override
    public void play() {

    }

    @Override
    public void stop() {

    }
}
