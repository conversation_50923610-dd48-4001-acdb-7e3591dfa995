package com.fastclip.opencv.grabber;

import com.fastclip.common.model.dto.track.element.AudioTrackElement;
import com.fastclip.common.model.dto.track.element.TrackElement;
import com.fastclip.common.model.dto.track.element.VideoTrackElement;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;

@Slf4j
public class TrackElementGrabber {

    TrackElement trackElement;

    FFmpegFrameGrabber ffFmpegFrameGrabber;

    public TrackElementGrabber(TrackElement trackElement) {
        try {
            this.trackElement = trackElement;
            if (trackElement instanceof VideoTrackElement || trackElement instanceof AudioTrackElement) {
                ffFmpegFrameGrabber = new FFmpegFrameGrabber(trackElement.getFilePath());
                ffFmpegFrameGrabber.setTimestamp(trackElement.getStartTs());
            }
        }catch (Exception e) {
            log.error("create track element grabber error", e);
        }
    }

    public Frame grabImageFrame() {
        if(ffFmpegFrameGrabber == null) {
            return null;
        }
        try {
            return ffFmpegFrameGrabber.grabImage();
        }catch (Exception e) {
            log.error("grab image frame error", e);
            return null;
        }
    }

    public Frame grabAudioFrame() {
        if(ffFmpegFrameGrabber == null) {
            return null;
        }
        try {
            return ffFmpegFrameGrabber.grabSamples();
        }catch (Exception e) {
            log.error("grab audio frame error", e);
            return null;
        }
    }
}
