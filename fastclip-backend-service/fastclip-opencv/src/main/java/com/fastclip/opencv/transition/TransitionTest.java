package com.fastclip.opencv.transition;

import org.bytedeco.javacpp.Loader;
import org.bytedeco.opencv.opencv_java;
import org.opencv.core.Core;
import org.opencv.core.Mat;

import static org.opencv.core.Core.addWeighted;
import static org.opencv.imgcodecs.Imgcodecs.imread;
import static org.opencv.imgcodecs.Imgcodecs.imwrite;

public class TransitionTest {
    public static void main(String[] args){
        Loader.load(opencv_java.class);
        Mat image1 = imread("/Users/<USER>/Documents/douyin/1.png");
        Mat image2 = imread("/Users/<USER>/Documents/douyin/2.png");

        Mat blended = new Mat();
        double alpha = 0.0;

        for(int i=0; i<=10; i++) {
            alpha = i / 10.0;
            addWeighted(image1, alpha, image2, 1 - alpha, 0, blended);
            imwrite(String.format("/Users/<USER>/Documents/douyin/3.png", i), blended);
        }
    }
}
