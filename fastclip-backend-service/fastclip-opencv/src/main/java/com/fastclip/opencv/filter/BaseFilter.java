package com.fastclip.opencv.filter;

import com.fastclip.common.constant.FilterTypeEnum;
import com.fastclip.common.model.dto.specialEffect.FilterEffectDTO;
import com.fastclip.dao.model.dataobject.FilterEffect;
import com.fastclip.opencv.atmosphere.FrostedGlassAtmosphere;
import com.fastclip.opencv.atmosphere.SnowAtmosphere;
import com.fastclip.opencv.atmosphere.StarlitSkyAtmosphere;
import org.bytedeco.javacv.FFmpegFrameFilter;
import org.bytedeco.javacv.FFmpegLogCallback;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.FrameFilter;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.opencv_core.Mat;

import java.text.DecimalFormat;

import static org.bytedeco.opencv.global.opencv_imgcodecs.imwrite;
import static org.opencv.imgcodecs.Imgcodecs.imread;

public class BaseFilter extends Filter{

    FrameFilter frameFilter;

    public BaseFilter(FilterEffectDTO filterEffect){
        this.setContrastFactor(filterEffect.getContrast());
        this.setHighLightFactor(filterEffect.getHighlight());
        this.setLignthnessFactor(filterEffect.getLightness());
        this.setSaturateFactor(filterEffect.getSaturate());
        this.setShadowFactor(filterEffect.getShadow());
        this.setTemperatureFactor(filterEffect.getTemperature());
        // 创建FFmpegFrameFilter对象，用于应用滤镜
        // 这里应用的滤镜是 "movie=logo.png [watermark]; [in] [watermark] overlay=main_w-overlay_w-10:main_h-overlay_h-10 [out]"
        // 它将在视频的右下角添加水印图片logo.png
        DecimalFormat df = new DecimalFormat("#.00");
        String contrastStr = df.format(filterEffect.getContrast()/(double)100 + 1);
        String brightnessStr = df.format(filterEffect.getLightness()/(double)100 + 1);
        String saturationStr = df.format(filterEffect.getSaturate()/(double)100 + 1);
//        String filterStr = "eq=brightness=" +  brightnessStr + ":contrast=" + contrastStr + ":saturation=" + saturationStr;
        String filterStr = "eq=saturation=1.5";
        frameFilter = new FFmpegFrameFilter( filterStr, 1080,1980);
    }

    @Override
    public Frame filter(Frame frame) {
        try {
            return frame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        Mat img = opencv_imgcodecs.imread("/Users/<USER>/Documents/tmp/2.jpeg");
//        Mat adjustedImage = new BaseFilter(FilterTypeEnum.Filter3).filter(img);
        SnowAtmosphere starAtmosphere = new SnowAtmosphere();
        Mat target = starAtmosphere.process(img);
        // 保存或显示调整后的图像
        imwrite("/Users/<USER>/Documents/tmp/adjusted_image4.jpg", target);
    }
}

