package com.fastclip.opencv.grabber;

import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.VideoMaterialClipDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.utils.VideoMaterialUtils;
import com.fastclip.opencv.filter.Filter;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;

import java.util.List;

public class LiveVideoClipGrabber extends VideoClipGrabber{

    VideoClipDTO videoClipDTO;

    LiveVideoSubtitlesGrabber grabber;

    List<SubtitlesCutDTO> subtitlesCutDTOList;

    Integer subtitlesCutIndex = 0;

    SubtitlesCutDTO curSubtitlesCutDTO;

    Long firstVideoFrameTime = 0L;

    Long firstAudioFrameTime = 0L;

    double curCutPercent = 0;

    double cutPerForEveryFrame = 0;

    int maxCutPercent = 0;

    Filter filter;

    public void setVideoClipDTO(VideoClipDTO videoClipDTO) {
        this.videoClipDTO = videoClipDTO;
        subtitlesCutDTOList = videoClipDTO.getSubtitlesCutDTOList();
    }

    @Override
    public byte[] grabRange(Integer startTs, Integer endTs) {
        try {
            List<VideoMaterialClipDTO> materialClipDTOS = VideoMaterialUtils.getVideoMaterialClips(videoClipDTO.getSubtitlesCutDTOList());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            for (VideoMaterialClipDTO videoMaterialClipDTO : materialClipDTOS) {
                Long startTsOfThisClip = 0L;
                Long endtTsOfThisClip = 0L;
                //如果endTs视频片段的startTs，则遍历结束
                if (endTs < videoMaterialClipDTO.getStartTs()) {
                    break;
                }
                //如果视频片段在startTs和endTs之间，做全部获取这个视频片段的数据
                else if (startTs <= videoMaterialClipDTO.getStartTs() && endTs >= videoMaterialClipDTO.getEndTs()) {
                    startTsOfThisClip = videoMaterialClipDTO.getStartTs() * 1000L;
                    endtTsOfThisClip = videoMaterialClipDTO.getEndTs()  * 1000L;
                }//如果视频片段在startTs和endTs之间，做全部获取这个视频片段的数据
                else if (startTs > videoMaterialClipDTO.getStartTs() && endTs >= videoMaterialClipDTO.getEndTs()) {
                    startTsOfThisClip = startTs  * 1000L;
                    endtTsOfThisClip = videoMaterialClipDTO.getEndTs()  * 1000L;
                } else if (startTs <= videoMaterialClipDTO.getStartTs() && endTs <= videoMaterialClipDTO.getEndTs()) {
                    startTsOfThisClip = videoMaterialClipDTO.getStartTs()  * 1000L;
                    endtTsOfThisClip = endTs  * 1000L;
                }
                byte[] bytes = FFmpegUtils.getAudios(videoMaterialClipDTO.getVideoPath(), startTsOfThisClip, endtTsOfThisClip);
                if(bytes != null) {
                    outputStream.write(bytes);
                }
                return outputStream.toByteArray();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabImage() {
        try {
            Frame frame = this.grabImageFrame();
            if (frame != null && frame.timestamp <= getLastTiemstampNeeded()) {
                return frame;
            }
            subtitlesCutIndex++;
            if (subtitlesCutIndex >= subtitlesCutDTOList.size()) {
                return null;
            }
            this.curSubtitlesCutDTO = subtitlesCutDTOList.get(subtitlesCutIndex);
            startGrabber(curSubtitlesCutDTO);
            return this.grabImageFrame();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabSamples() {
        try {
            Frame frame = this.grabSampleFrame();
            if (frame != null && frame.timestamp <= getLastTiemstampNeeded()) {
                return frame;
            }
            subtitlesCutIndex++;
            if (subtitlesCutIndex >= subtitlesCutDTOList.size()) {
                return null;
            }
            this.curSubtitlesCutDTO = subtitlesCutDTOList.get(subtitlesCutIndex);
            startGrabber(curSubtitlesCutDTO);
            return this.grabSampleFrame();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Long getLastTiemstampNeeded() {
        //如果是最后一个片段，需要延长0.5秒，用于后续转场。
        if(subtitlesCutIndex == subtitlesCutDTOList.size() - 1) {
            return curSubtitlesCutDTO.getCutEndTs() * 1000L + 500000L;
        }
        return curSubtitlesCutDTO.getCutEndTs() * 1000L;
    }

    @Override
    public Frame grabMusicSamples() {
        return null;
    }

    @Override
    public Frame grabVideoAndSamples() {
        Frame frame = grabVideoAndSampleFrame();
        if(frame != null) {
            return frame;
        }
        subtitlesCutIndex++;
        if(subtitlesCutIndex >= subtitlesCutDTOList.size()) {
            return null;
        }
        this.curSubtitlesCutDTO = subtitlesCutDTOList.get(subtitlesCutIndex);
        startGrabber(curSubtitlesCutDTO);
        return grabVideoAndSampleFrame();
    }

    private Frame grabImageFrame() {
        try {
            Frame frame = grabber.grabImage();
            this.setVideoTimestamp(frame.timestamp);
            return frame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Frame grabSampleFrame() {
        try {
            Frame frame = grabber.grabSamples();
            this.setAudioTimestamp(frame.timestamp);
            return frame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Frame grabVideoAndSampleFrame() {
        if(this.getVideoTimestamp() < curSubtitlesCutDTO.getCutEndTs()) {
            Frame frame = grabImageFrame();
            if (frame != null && frame.timestamp <= curSubtitlesCutDTO.getCutEndTs() * 1000L) {
                return frame;
            }
        }
        if(this.getAudioTimestamp() < curSubtitlesCutDTO.getCutEndTs()) {
            Frame frame = grabSampleFrame();
            if (frame != null && frame.timestamp <= curSubtitlesCutDTO.getCutEndTs() * 1000L) {
                return frame;
            }
        }
        return null;
    }

    @Override
    public void setTimestamp(Long timestamp, Boolean isVideo) {
        try {
            if (isVideo) {
                grabber.setTimestamp(timestamp - firstVideoFrameTime, true);
            } else {
                grabber.setTimestamp(timestamp - firstAudioFrameTime, true);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void setImageRate(int rate) {
    }

    private void startGrabber(SubtitlesCutDTO subtitlesCutDTO) {
        try {
            if(grabber != null) {
                grabber.close();
            }
            grabber = new LiveVideoSubtitlesGrabber();
            grabber.setVideoMaterialDTO(subtitlesCutDTO.getVideoMaterialDTO());
            grabber.setVideoOrAudio(getVideoOrAudio().getCode());
            grabber.setSubtitlesDTO(subtitlesCutDTO.getSubtitlesDTO());
            grabber.start();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getVideoPath(SubtitlesCutDTO subtitlesCutDTO) {
        VideoMaterialDTO videoMaterialDTO = subtitlesCutDTO.getVideoMaterialDTO();
        if(videoMaterialDTO.getVideoType().equals(VideoMaterialTypeEnum.LIVE.getValue()) &&
                videoMaterialDTO.getStatus().equals(VideoMaterialCombineStatusEnum.PROCESSING.getValue())) {
            return subtitlesCutDTO.getVideoMaterialSliceDTO().getPath();
        }else{
            return videoMaterialDTO.getPath();
        }
    }

    private Integer getStartTs(SubtitlesCutDTO subtitlesCutDTO) {
        VideoMaterialDTO videoMaterialDTO = subtitlesCutDTO.getVideoMaterialDTO();
        if(videoMaterialDTO.getVideoType().equals(VideoMaterialTypeEnum.LIVE.getValue()) &&
                videoMaterialDTO.getStatus().equals(VideoMaterialCombineStatusEnum.PROCESSING.getValue())) {
            return subtitlesCutDTO.getCutStartTs() - subtitlesCutDTO.getVideoMaterialSliceDTO().getStartTs();
        }else{
            return subtitlesCutDTO.getCutStartTs();
        }
    }

    @Override
    public void start() {
        curSubtitlesCutDTO = subtitlesCutDTOList.get(0);
        startGrabber(curSubtitlesCutDTO);
    }

    @Override
    public void stop() {

    }

    @Override
    public double getFrameRate() {
        return grabber.getFrameRate();
    }

    @Override
    public int getSampleRate() {
        return grabber.getSampleRate();
    }

    @Override
    public int getVideoBitrate() {
        return grabber.getVideoBitrate();
    }

    @Override
    public void close() {
        try {
            grabber.close();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getBaseTime() {
        return 0;
    }

    @Override
    public int getImageWidth() {
        return grabber.getImageWidth();
    }

    @Override
    public int getImageHeight() {
        return grabber.getImageHeight();
    }

    @Override
    public void setImageWidth(int imageWidth) {
        grabber.setImageWidth(imageWidth);
    }

    @Override
    public void setImageHeight(int imageHeight) {
        grabber.setImageHeight(imageHeight);
    }

    @Override
    public long getStartTs() {
        return 0L;
    }

    public void setMaxCutPercent(int maxCutPercent) {
        this.maxCutPercent = maxCutPercent;
        this.cutPerForEveryFrame =  maxCutPercent * 1000/(getFrameRate() * videoClipDTO.getDuration());
    }

    public void setFilter(Filter filter) {
        this.filter = filter;
    }
}
