package com.fastclip.opencv.atmosphere;

import org.bytedeco.javacpp.BytePointer;
import org.bytedeco.opencv.opencv_core.Mat;

import java.util.Random;

public class FrostedGlassAtmosphere implements Atmosphere{

    private Integer offsets;

    public void setOffsets(Integer offsets) {
        this.offsets = offsets;
    }

    public Integer getOffsets() {
        return offsets;
    }

    @Override
    public Mat process(Mat img) {
        int height = img.rows();
        int width = img.cols();

        int randomNum = 0;

        for(int i = 0; i < height - offsets; i++) {
            for (int j = 0; j < width - offsets; j++) {
                BytePointer ptr = img.ptr(i, j);
                int b = ptr.get(0) < 0 ? (ptr.get(0) + 256) : ptr.get(0);
                int g = ptr.get(1) < 0 ? (ptr.get(1) + 256) : ptr.get(1);
                int r = ptr.get(2) < 0 ? (ptr.get(2) + 256) : ptr.get(2);
                // 随机生成 0 - offset 之间的数值
                Random rand = new Random();
                randomNum = rand.nextInt(offsets) % (offsets + 1);
                int B = b + randomNum;
                int G = g + randomNum;
                int R = r + randomNum;
                ptr.put((byte) Math.max(0, Math.min(B, 255)), (byte) Math.max(0, Math.min(G, 255)), (byte) Math.max(0, Math.min(R, 255)));
            }
        }
        return img;
    }
}
