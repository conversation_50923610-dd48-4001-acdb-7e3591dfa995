package com.fastclip.opencv.grabber;

import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.VideoMaterialClipDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.common.utils.VideoMaterialUtils;
import com.fastclip.opencv.FFmpegCmd;
import com.fastclip.opencv.filter.Filter;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.bytedeco.javacpp.indexer.UByteRawIndexer;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.*;
import org.bytedeco.opencv.opencv_core.Point;
import org.bytedeco.opencv.opencv_videoio.VideoCapture;
import org.opencv.core.Core;
import org.opencv.core.CvType;
import org.opencv.core.MatOfByte;
import org.opencv.imgproc.Imgproc;
import org.opencv.videoio.Videoio;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.awt.image.WritableRaster;
import java.io.File;
import java.util.List;

import static org.bytedeco.opencv.global.opencv_core.CV_8UC3;
import static org.bytedeco.opencv.global.opencv_core.flip;
import static org.bytedeco.opencv.global.opencv_imgcodecs.imread;
import static org.bytedeco.opencv.global.opencv_imgproc.*;

public class VideoClipGrabber extends Grabber{

    VideoClipDTO videoClipDTO;

    FFmpegFrameGrabber grabber;

    List<SubtitlesCutDTO> subtitlesCutDTOList;

    Integer subtitlesCutIndex = 0;

    SubtitlesCutDTO curSubtitlesCutDTO;

    Long firstVideoFrameTime = 0L;

    Long firstAudioFrameTime = 0L;

    double curCutPercent = 0;

    double cutPerForEveryFrame = 0;

    int maxCutPercent = 0;

    int bitrate = 0;

    double tbr = 0;

    Filter filter;

    boolean isCurFrameStartOfClip = false;

    boolean startNewClip = false;

    public void setVideoClipDTO(VideoClipDTO videoClipDTO) {
        this.videoClipDTO = videoClipDTO;
        subtitlesCutDTOList = videoClipDTO.getSubtitlesCutDTOList();
    }

    @Override
    public byte[] grabRange(Integer startTs, Integer endTs) {
        try {
            List<VideoMaterialClipDTO> materialClipDTOS = VideoMaterialUtils.getVideoMaterialClips(videoClipDTO.getSubtitlesCutDTOList());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            for (VideoMaterialClipDTO videoMaterialClipDTO : materialClipDTOS) {
                Long startTsOfThisClip = 0L;
                Long endtTsOfThisClip = 0L;
                //如果endTs视频片段的startTs，则遍历结束
                if (endTs < videoMaterialClipDTO.getStartTs()) {
                    break;
                }
                //如果视频片段在startTs和endTs之间，做全部获取这个视频片段的数据
                else if (startTs <= videoMaterialClipDTO.getStartTs() && endTs >= videoMaterialClipDTO.getEndTs()) {
                    startTsOfThisClip = videoMaterialClipDTO.getStartTs() * 1000L;
                    endtTsOfThisClip = videoMaterialClipDTO.getEndTs()  * 1000L;
                }//如果视频片段在startTs和endTs之间，做全部获取这个视频片段的数据
                else if (startTs > videoMaterialClipDTO.getStartTs() && endTs >= videoMaterialClipDTO.getEndTs()) {
                    startTsOfThisClip = startTs  * 1000L;
                    endtTsOfThisClip = videoMaterialClipDTO.getEndTs()  * 1000L;
                } else if (startTs <= videoMaterialClipDTO.getStartTs() && endTs <= videoMaterialClipDTO.getEndTs()) {
                    startTsOfThisClip = videoMaterialClipDTO.getStartTs()  * 1000L;
                    endtTsOfThisClip = endTs  * 1000L;
                }
                byte[] bytes = FFmpegUtils.getAudios(videoMaterialClipDTO.getVideoPath(), startTsOfThisClip, endtTsOfThisClip);
                if(bytes != null) {
                    outputStream.write(bytes);
                }
                return outputStream.toByteArray();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabImage() {
        try {
            Frame frame = this.grabImageFrame();
            if (frame != null && (frame.timestamp <= getLastTiemstampNeeded())) {
                return frame;
            }
            subtitlesCutIndex++;
            if (subtitlesCutIndex >= subtitlesCutDTOList.size()) {
                return null;
            }
            this.curSubtitlesCutDTO = subtitlesCutDTOList.get(subtitlesCutIndex);
            startGrabber(curSubtitlesCutDTO);
            return this.grabImageFrame();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Frame grabSamples() {
        try {
            Frame frame = this.grabSampleFrame();
            if (frame != null && frame.timestamp <= getLastTiemstampNeeded()) {
                return frame;
            }
            subtitlesCutIndex++;
            if (subtitlesCutIndex >= subtitlesCutDTOList.size()) {
                return null;
            }
            this.curSubtitlesCutDTO = subtitlesCutDTOList.get(subtitlesCutIndex);
            startGrabber(curSubtitlesCutDTO);
            return this.grabSampleFrame();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Long getLastTiemstampNeeded() {
        //如果是最后一个片段，需要延长0.5秒，用于后续转场。
        if(subtitlesCutIndex == subtitlesCutDTOList.size() - 1) {
            return curSubtitlesCutDTO.getCutEndTs() * 1000L + 500000L;
        }
        return curSubtitlesCutDTO.getCutEndTs() * 1000L;
    }

    @Override
    public Frame grabMusicSamples() {
        return null;
    }

    @Override
    public Frame grabVideoAndSamples() {
        Frame frame = grabVideoAndSampleFrame();
        if(frame != null) {
            return frame;
        }
        subtitlesCutIndex++;
        if(subtitlesCutIndex >= subtitlesCutDTOList.size()) {
            return null;
        }
        this.curSubtitlesCutDTO = subtitlesCutDTOList.get(subtitlesCutIndex);
        startGrabber(curSubtitlesCutDTO);
        return grabVideoAndSampleFrame();
    }

    private Frame grabImageFrame() {
        try {
            Frame frame = grabber.grabImage();
            this.setVideoTimestamp(frame.timestamp);
            if(startNewClip) {
                isCurFrameStartOfClip = true;
                startNewClip =false;
            }else {
                isCurFrameStartOfClip = false;
            }
            return frame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Frame grabSampleFrame() {
        try {
            Frame frame = grabber.grabSamples();
            grabber.setSampleRate(44100);
            this.setAudioTimestamp(frame.timestamp);
            return frame;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Frame grabVideoAndSampleFrame() {
        if(this.getVideoTimestamp() < curSubtitlesCutDTO.getCutEndTs()) {
            Frame frame = grabImageFrame();
            if (frame != null && frame.timestamp <= curSubtitlesCutDTO.getCutEndTs() * 1000L) {
                return frame;
            }
        }
        if(this.getAudioTimestamp() < curSubtitlesCutDTO.getCutEndTs()) {
            Frame frame = grabSampleFrame();
            if (frame != null && frame.timestamp <= curSubtitlesCutDTO.getCutEndTs() * 1000L) {
                return frame;
            }
        }
        return null;
    }

    @Override
    public void setTimestamp(Long timestamp, Boolean isVideo) {
        try {
            if (isVideo) {
                grabber.setTimestamp(timestamp - firstVideoFrameTime, true);
            } else {
                grabber.setTimestamp(timestamp - firstAudioFrameTime, true);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void setImageRate(int rate) {
    }

    private void startGrabber(SubtitlesCutDTO subtitlesCutDTO) {
        try {
            if(grabber != null) {
                grabber.close();
            }
            grabber = new FFmpegFrameGrabber(getVideoPath(subtitlesCutDTO));
            grabber.setSampleRate(44100);
            grabber.start();
            int width = grabber.getImageWidth();
            int height = grabber.getImageHeight();
            grabber.setImageWidth(width);
//            int newHeight = 1920 * height / width;
//            grabber.setImageHeight( (newHeight%2 == 0)?newHeight:(newHeight + 1));
            grabber.setImageHeight(height);
            Frame firstAudioFrame = grabber.grabSamples();
            firstAudioFrameTime = firstAudioFrame.timestamp;
            grabber.restart();
            Frame firstVideoFrame = grabber.grabImage();
            firstVideoFrameTime = firstVideoFrame.timestamp;
            grabber.restart();
            if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(getVideoOrAudio().getCode())) {
                this.setGrabberVideoTimestamp(getStartTs(curSubtitlesCutDTO) * 1000L);
            }else{
                grabber.setAudioChannels(2);
                this.setGrabberAudioTimestamp(getStartTs(curSubtitlesCutDTO)  * 1000L);
            }
            startNewClip = true;
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getVideoPath(SubtitlesCutDTO subtitlesCutDTO) {
        VideoMaterialDTO videoMaterialDTO = subtitlesCutDTO.getVideoMaterialDTO();
        if(videoMaterialDTO.getVideoType().equals(VideoMaterialTypeEnum.LIVE.getValue()) &&
                videoMaterialDTO.getStatus().equals(VideoMaterialCombineStatusEnum.PROCESSING.getValue())) {
            return subtitlesCutDTO.getVideoMaterialSliceDTO().getPath();
        }else{
            return videoMaterialDTO.getPath();
        }
    }

    private Integer getStartTs(SubtitlesCutDTO subtitlesCutDTO) {
        VideoMaterialDTO videoMaterialDTO = subtitlesCutDTO.getVideoMaterialDTO();
        if(videoMaterialDTO.getVideoType().equals(VideoMaterialTypeEnum.LIVE.getValue()) &&
                videoMaterialDTO.getStatus().equals(VideoMaterialCombineStatusEnum.PROCESSING.getValue())) {
            return subtitlesCutDTO.getCutStartTs() - subtitlesCutDTO.getVideoMaterialSliceDTO().getStartTs();
        }else{
            return subtitlesCutDTO.getCutStartTs();
        }
    }

    private void setGrabberVideoTimestamp(Long timestamp) {
        try {
            Long timeForEveryFrame = (long)(1000000/getFrameRate());
            Long loopTime = firstVideoFrameTime > 0 ? firstVideoFrameTime: timeForEveryFrame;
            grabber.setVideoTimestamp(timestamp - firstVideoFrameTime);
            Frame frame = grabber.grabImage();
            int i = 1;
            long preTimestamp = 0;
            while(frame.timestamp >= timestamp && frame.timestamp > preTimestamp) {
                i++;
                grabber.restart();
                grabber.setVideoTimestamp(timestamp - i*loopTime);
                preTimestamp = frame.timestamp;
                frame = grabber.grabImage();
            }
            while(frame.timestamp < (timestamp-timeForEveryFrame)) {
                grabber.grabImage();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isCurFrameStartOfClip() {
        return isCurFrameStartOfClip;
    }

    private void setGrabberAudioTimestamp(Long timestamp) {
        try {
            Long timeForEveryFrame = grabber.getLengthInTime()/grabber.getLengthInAudioFrames();
            Long loopTime = firstVideoFrameTime > 0 ? firstVideoFrameTime: timeForEveryFrame;
            grabber.setAudioTimestamp(timestamp - firstAudioFrameTime);
            Frame frame = grabber.grabSamples();
            int i = 1;
            long preTimestamp = 0;
            while(frame.timestamp >= timestamp && frame.timestamp > preTimestamp) {
                i++;
                grabber.restart();
                grabber.setAudioTimestamp(timestamp - i*loopTime);
                preTimestamp = frame.timestamp;
                frame = grabber.grabSamples();
            }
            while(frame.timestamp < (timestamp-timeForEveryFrame)) {
                grabber.grabSamples();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void start() {
        curSubtitlesCutDTO = subtitlesCutDTOList.get(0);
        startGrabber(curSubtitlesCutDTO);
    }

    @Override
    public void stop() {

    }

    @Override
    public double getFrameRate() {
        // 获取帧率
        if(tbr <= 0) {
            Double fps = FFmpegCmd.getTBR(getVideoPath(curSubtitlesCutDTO));
            tbr = (fps != null)?fps:0;
        }
        if(tbr <= 0) {
            tbr = grabber.getFrameRate();
        }
        return tbr;
    }

    @Override
    public int getSampleRate() {
        return grabber.getSampleRate();
    }

    @Override
    public int getVideoBitrate() {
        if(bitrate <= 0) {
            bitrate= FFmpegCmd.getBitrate(getVideoPath(curSubtitlesCutDTO));
        }
        return bitrate;
    }

    @Override
    public void close() {
        try {
            grabber.close();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getBaseTime() {
        return 0;
    }

    @Override
    public long getCurSubtitlesId() {
        return curSubtitlesCutDTO.getSubtitlesId();
    }

    @Override
    public int getAudioBitrate() {
        return grabber.getAudioBitrate();
    }

    @Override
    public int getChannels() {
        return grabber.getAudioChannels();
    }

    @Override
    public int getImageWidth() {
        return grabber.getImageWidth();
    }

    @Override
    public int getImageHeight() {
        return grabber.getImageHeight();
    }

    @Override
    public void setImageWidth(int imageWidth) {
        grabber.setImageWidth(imageWidth);
    }

    @Override
    public void setImageHeight(int imageHeight) {
        grabber.setImageHeight(imageHeight);
    }

    @Override
    public long getStartTs() {
        return 0L;
    }

    public void setMaxCutPercent(int maxCutPercent) {
        this.maxCutPercent = maxCutPercent;
        this.cutPerForEveryFrame =  maxCutPercent * 1000/(getFrameRate() * videoClipDTO.getDuration());
    }

    public void setFilter(Filter filter) {
        this.filter = filter;
    }
}
