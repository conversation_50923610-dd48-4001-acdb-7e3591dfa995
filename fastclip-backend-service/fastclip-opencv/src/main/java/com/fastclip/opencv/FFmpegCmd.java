package com.fastclip.opencv;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;

@Service
@Slf4j
public class FFmpegCmd {
    public void printProcessExecResult(Process process) {
        try {
            // 读取FFmpeg进程的错误流
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            while ((line = errorReader.readLine()) != null) {
                log.info(line);
            }
            // 等待FFmpeg进程结束
            process.waitFor();

            log.info("Conversion completed successfully.");
        }catch (Exception e) {
            log.error("print process exec result error .........", e);

        }
    }

    public static Double getTBR(String videoPath) {
        ProcessBuilder process = new ProcessBuilder(
                "ffmpeg",
                "-i", videoPath);
        process.redirectErrorStream(true);
        try {
            String result = getProcessExecResult(process.start());
            int indexOfFps = result.indexOf("fps,");
            int indexOfTbr = result.indexOf("tbr,");
            String tbr = result.substring(indexOfFps+4, indexOfTbr).trim();
            return Double.parseDouble(tbr);
        }catch (Exception e){
            log.error("insertSrt error", e);
            return null;
        }

    }

    public static int getBitrate(String videoPath) {
        ProcessBuilder process = new ProcessBuilder(
                "ffmpeg",
                "-i", videoPath);
        process.redirectErrorStream(true);
        try {
            String result = getProcessExecResult(process.start());
            int startIndex = result.indexOf("bitrate: ");
            int endIndex = result.indexOf("kb", startIndex + 8);
            String tbr = result.substring(startIndex+8, endIndex).trim();
            return Integer.parseInt(tbr) * 1000;
        }catch (Exception e){
            log.error("insertSrt error", e);
            return 0;
        }

    }


    public static String getProcessExecResult(Process process) {
        try {
            // 读取FFmpeg进程的错误流
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            StringBuilder stringBuilder =  new StringBuilder();
            while ((line = reader.readLine()) != null) {
                log.info(line);
                stringBuilder.append(line);
            }
            // 等待FFmpeg进程结束
            process.waitFor();
            log.info("Conversion completed successfully.");
            return stringBuilder.toString();
        }catch (Exception e) {
            log.error("print process exec result error .........", e);
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(getBitrate("/Volumes/共享文件夹/douyin/潘一秀/2025-03-02 (1-2).ts"));
    }
}
