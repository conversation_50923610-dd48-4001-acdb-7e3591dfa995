package com.fastclip.opencv;

import com.fastclip.common.constant.ProcessorEnum;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.SpecialEffectReq;
import com.fastclip.common.utils.NumberUtils;
import com.fastclip.common.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.opencv_core.Mat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import java.awt.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;
import java.util.List;

import static org.bytedeco.opencv.global.opencv_imgcodecs.imread;

@Service
@Slf4j
public class FFmpegService {

    @Autowired
    FFmpegCmd fFmpegCmd;

    @Value("${ffmpeg.processor}")
    String processor;

    /**
     *
     * @apiNote 切割视频指定的位置
     * @param videoPath 视频路径
     * @param start 视频开始时间 ，单位秒

     * @return 生成文件路径
     * */

    public SubtitlesFetchResultDTO getAudio(String videoPath, Integer start, String audioTmpPath) throws FFmpegFrameGrabber.Exception, FFmpegFrameRecorder.Exception {
        SubtitlesFetchResultDTO res = new SubtitlesFetchResultDTO();
        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoPath);
        grabber.start();
        grabber.setAudioTimestamp(start * 1000L);
        FFmpegFrameRecorder ffmpegFrameRecorder = new FFmpegFrameRecorder(audioTmpPath, 0, 0, 1);
        ffmpegFrameRecorder.setSampleRate(16000);
        ffmpegFrameRecorder.setFormat("wav");
        ffmpegFrameRecorder.setAudioBitrate(grabber.getAudioBitrate());
        ffmpegFrameRecorder.setAudioOption("crf", "0");
        ffmpegFrameRecorder.start();
        Frame frame = grabber.grabSamples();
        if(frame != null) {
            res.setStartTs((int)(frame.timestamp/1000L));
            res.setEndTs((int)(frame.timestamp/1000L));
        }
        int countOfFrame = 1;
        while (frame != null && countOfFrame <= 3000){
            countOfFrame++;
            ffmpegFrameRecorder.record(frame);
            res.setEndTs((int)(frame.timestamp/1000L));
            frame = grabber.grabSamples();

        }
        ffmpegFrameRecorder.stop();
        ffmpegFrameRecorder.release();
        return res;
    }

    public boolean specialEffect(SpecialEffectReq effectReq) {
        //进行视频切割与合并
//        mergeSplits(effectReq);
        //进行逐帧放大和翻转并且合并字幕
        String cropTmpFilePath = cropAndFlip(effectReq);
        //插入字幕
        String assTmpFilePath = insertAss(effectReq, cropTmpFilePath);
        //无字幕视频增加背景音乐，转场，结尾等
        speedUpXFadeMusicAndTails(effectReq, cropTmpFilePath, effectReq.getOutputVideoPathWithOutAss());
        //有字幕视频增加背景音乐，转场，结尾等
        return speedUpXFadeMusicAndTails(effectReq, assTmpFilePath, effectReq.getOutputVideoPath());
    }


    public String mergeSplits(SpecialEffectReq effectReq) {
        List<VideoClipSpeicalEffectDTO> effectDTOS = effectReq.getVideoClipSpeicalEffectDTOList();
        StringBuilder videoCutCmdStrBuilder = new StringBuilder();
        StringBuilder videoMergeCmdStrBuilder = new StringBuilder();
        StringBuilder audioCutCmdStrBuilder = new StringBuilder();
        StringBuilder audioMergeCmdStrBuilder =new StringBuilder();
        List<String> videoPaths = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        int n = 0;
        for(int i = 0; i < effectDTOS.size(); i++) {
            VideoClipSpeicalEffectDTO effectDTO = effectDTOS.get(i);
            VideoClipDTO videoClipDTO = effectDTO.getVideoClipDTO();
            List<SubtitlesCutDTO> subtitlesCutDTOs = videoClipDTO.getSubtitlesCutDTOList();
            for(SubtitlesCutDTO subtitlesCutDTO: subtitlesCutDTOs) {
                double startTs = subtitlesCutDTO.getCutStartTs()/1000;
                double endTs = subtitlesCutDTO.getCutEndTs()/1000;
                String videoPath = subtitlesCutDTO.getVideoMaterialDTO().getPath();
                Integer index;
                if(map.containsKey(videoPath)) {
                    index = map.get(videoPath);
                }else{
                    videoPaths.add(videoPath);
                    index = videoPaths.size() - 1;
                    map.put(videoPath, index);
                }
                String startTsStr = NumberUtils.doubleToStr(startTs);
                String endTsStr = NumberUtils.doubleToStr(endTs);
                videoCutCmdStrBuilder.append("[" + index + ":v]trim=start=" + startTsStr + ":end=" + endTsStr +
                        ",setpts=PTS-STARTPTS[v" + n + "];");
                videoMergeCmdStrBuilder.append("[v" + n + "]");
                audioCutCmdStrBuilder.append("[0:a]"+"atrim=start=" + (startTsStr)  + ":end=" + endTsStr+ "[a" + n + "];");
                audioMergeCmdStrBuilder.append("[a" + n + "]");
                n++;
            }
        }
        videoMergeCmdStrBuilder.append("concat=n="+n+":v=1:a=0[vOut];");
        audioMergeCmdStrBuilder.append("concat=n="+n+":v=0:a=1[aOut];");
        String tmpCropVideoPath = effectReq.getInputVideoPath();
        int size = videoPaths.size();
        String[] inputs = new String[ size* 2 + 11];
        inputs[0] = "ffmpeg";
        inputs[1] = "-c:v";
        if(ProcessorEnum.GPU.getValue().equals(processor)) {
            inputs[2] = "h264_nvenc";
        }else{
            inputs[2] = "h264";
        }
        for(int i=0; i<videoPaths.size(); i++) {
            inputs[2*i +3] = "-i";
            inputs[2*i + 4] = videoPaths.get(i);
        }
        inputs[2*(size-1) +5] = "-filter_complex";
        inputs[2*(size-1) +6] = videoCutCmdStrBuilder.toString() + videoMergeCmdStrBuilder +
                audioCutCmdStrBuilder + audioMergeCmdStrBuilder;
        inputs[2*(size-1) + 7] = "-map";
        inputs[2*(size-1)+ 8] = "[vOut]";
        inputs[2*(size-1) + 9] = "-map";
        inputs[2*(size-1) + 10] = "[aOut]";
        inputs[2*(size-1) + 11] = tmpCropVideoPath;
        inputs[2*(size-1) + 12] = "-y";
        ProcessBuilder process = new ProcessBuilder(inputs);
        try {
            fFmpegCmd.printProcessExecResult(process.start());
            return tmpCropVideoPath;
        }catch (Exception e){
            log.error("insertSrt error", e);
            return null;
        }
    }


    public String cropAndFlip(SpecialEffectReq effectReq) {
        String videoCutCmdStr = "";
        String videoMergeCmdStr = "";

        List<VideoClipSpeicalEffectDTO> effectDTOS = effectReq.getVideoClipSpeicalEffectDTOList();
        double preDuration = 0;
        for(int i = 1; i <= effectDTOS.size(); i++) {
            VideoClipSpeicalEffectDTO effectDTO = effectDTOS.get(i - 1);
            double startTs = preDuration;
            double endTs = preDuration + effectDTO.getVideoClipDTO().getDuration()/(double)1000 + 0.5;
            String startTsStr = NumberUtils.doubleToStr(startTs);
            String endTsStr = NumberUtils.doubleToStr(endTs);
            Integer maxCutPercent = effectDTO.getMaxCutPercent();
            Double scalePercent = (100 - maxCutPercent)/100d;
            videoCutCmdStr = videoCutCmdStr + "[0:v]trim=start=" + startTsStr + ":end=" + endTsStr +
                    ",setpts=PTS-STARTPTS[v" + i + "];";
            if(effectDTO.getFlipFlag()) {
                videoCutCmdStr += "[v"+i+"]hflip[v" + i + "];";
            }
            videoMergeCmdStr += "[v" + i + "]";
            preDuration = endTs;
        }
        videoMergeCmdStr = videoMergeCmdStr + "concat=n="+effectDTOS.size()+":v=1:a=0[out];";
        String tmpCropVideoPath = getTmpCropVideoPath(effectReq.getInputVideoPath());
        String codec;
        if(ProcessorEnum.GPU.getValue().equals(processor)) {
            codec = "h264_nvenc";
        }else{
            codec = "h264";
        }
        ProcessBuilder process = new ProcessBuilder(
                "ffmpeg",
                "-i", effectReq.getInputVideoPath(),
                "-filter_complex", videoCutCmdStr + videoMergeCmdStr,
                "-c:v", codec,
                "-map","[out]",
                "-map","a:0",
                tmpCropVideoPath, "-y");
        try {
            fFmpegCmd.printProcessExecResult(process.start());
            return tmpCropVideoPath;
        }catch (Exception e){
            log.error("insertSrt error", e);
            return null;
        }
    }

    public String insertAss(SpecialEffectReq effectReq, String inputPath) {
        String[] keywordsArr;
        if(effectReq.getKeywords() == null) {
            keywordsArr = new String[0];
        }else {
            keywordsArr = effectReq.getKeywords().split(",");
        }
        String assFile= createAssFiles(effectReq.getInputVideoPath(), keywordsArr,
                effectReq.getVideoClipSpeicalEffectDTOList(), effectReq.getFontEffectDTO());
        String insertAssCmd = "subtitles=" + assFile + ";";

        String tmpSrtVideoPath = getTmpSrtVideoPath(effectReq.getInputVideoPath());
        String codec;
        if(ProcessorEnum.GPU.getValue().equals(processor)) {
            codec = "h264_nvenc";
        }else{
            codec = "h264";
        }
        ProcessBuilder process = new ProcessBuilder(
                "ffmpeg",
                "-i", inputPath,
                "-filter_complex", insertAssCmd,
                "-c:v", codec,
                "-c:a","copy",
                tmpSrtVideoPath, "-y");
        try {
            fFmpegCmd.printProcessExecResult(process.start());
            return tmpSrtVideoPath;
        }catch (Exception e){
            log.error("insertSrt error", e);
            return null;
        }
    }

    public String cropAndFlipAndInsertAss(SpecialEffectReq effectReq) {
        String[] keywordsArr;
        if(effectReq.getKeywords() == null) {
            keywordsArr = new String[0];
        }else {
            keywordsArr = effectReq.getKeywords().split(",");
        }
        String assFile= createAssFiles(effectReq.getInputVideoPath(), keywordsArr,
                effectReq.getVideoClipSpeicalEffectDTOList(), effectReq.getFontEffectDTO());
        String videoCutCmdStr = "subtitles=" + assFile + ";";
        String videoMergeCmdStr = "";

        List<VideoClipSpeicalEffectDTO> effectDTOS = effectReq.getVideoClipSpeicalEffectDTOList();
        double preDuration = 0;
        for(int i = 1; i <= effectDTOS.size(); i++) {
            VideoClipSpeicalEffectDTO effectDTO = effectDTOS.get(i - 1);
            double startTs = preDuration;
            double endTs = preDuration + effectDTO.getVideoClipDTO().getDuration()/(double)1000;
            String startTsStr = NumberUtils.doubleToStr(startTs);
            String endTsStr = NumberUtils.doubleToStr(endTs);
            Integer maxCutPercent = effectDTO.getMaxCutPercent();
            Double scalePercent = (100 - maxCutPercent)/100d;
            videoCutCmdStr = videoCutCmdStr + "[0:v]trim=start=" + startTsStr + ":end=" + endTsStr +
                    ",setpts=PTS-STARTPTS[v" + i + "];";
//                    "[v" + i + "]crop=iw*"+scalePercent+":ih*"+scalePercent+"[v" + i + "];" +
//                    "[v" + i + "]scale="+effectDTO.getWidth()+":"+effectDTO.getHeight()+",setsar=1[v" + i + "];";
            if(effectDTO.getFlipFlag()) {
                videoCutCmdStr += "[v"+i+"]hflip[v" + i + "];";
            }
            videoMergeCmdStr += "[v" + i + "]";
            preDuration = endTs;
        }
        videoMergeCmdStr = videoMergeCmdStr + "concat=n="+effectDTOS.size()+":v=1:a=0[out];";
        String tmpCropVideoPath = getTmpCropVideoPath(effectReq.getInputVideoPath());
        String codec;
        if(ProcessorEnum.GPU.getValue().equals(processor)) {
            codec = "h264_nvenc";
        }else{
            codec = "h264";
        }
        ProcessBuilder process = new ProcessBuilder(
                "ffmpeg",
                "-i", effectReq.getInputVideoPath(),
                "-filter_complex", videoCutCmdStr + videoMergeCmdStr,
                "-c:v", codec,
                "-map","[out]",
                "-map","a:0",
                tmpCropVideoPath, "-y");
        try {
            fFmpegCmd.printProcessExecResult(process.start());
            return tmpCropVideoPath;
        }catch (Exception e){
            log.error("insertSrt error", e);
            return null;
        }
    }
//
//    public String insertSrt(SpecialEffectReq effectReq, String input) {
//        String tmpSubtitlesFilePath = createTmpSubtitlesFile(effectReq.getInputVideoPath(), effectReq.getVideoClipSpeicalEffectDTOList());
//        String tmpPathWithSrt = getTmpSrtVideoPath(effectReq.getInputVideoPath());
//        ProcessBuilder process = new ProcessBuilder(
//                "ffmpeg",
//                "-hwaccel", "videotoolbox",
//                "-i", input,
//                "-i", tmpSubtitlesFilePath,
//                "-lavfi",
//                "subtitles="+tmpSubtitlesFilePath+":force_style='Fontname="+effectReq.getFontFamily()+"" +
//                        ",Alignment=2,MarginV=50,Fontsize=8,PrimaryColour="+effectReq.getFontPrimaryColor()+",Outline=0",
//                tmpPathWithSrt, "-y");
//        try {
//            fFmpegCmd.printProcessExecResult(process.start());
//            return tmpPathWithSrt;
//        }catch (Exception e){
//            log.error("insertSrt error", e);
//            return null;
//        }
//    }


    public boolean speedUpXFadeMusicAndTails(SpecialEffectReq effectReq, String input, String output) {
        String videoCutCmdStr = "";
        String audioCutCmdStr = "";
        String transitionCmdStr = "";
        String audioMergeCmdStr = "";

        List<VideoClipSpeicalEffectDTO> effectDTOS = effectReq.getVideoClipSpeicalEffectDTOList();
        double preDuration = 0;
        for(int i = 1; i <= effectDTOS.size(); i++) {
            double startTs = preDuration;
            //原始视频裁剪的时候每个片段加了0.5s，这里结束时间要加0.5s
            double endTs = preDuration + effectDTOS.get(i - 1).getVideoClipDTO().getDuration()/(double)1000 + 0.5;
            String startTsStr = NumberUtils.doubleToStr(startTs);
            String endTsStr = NumberUtils.doubleToStr(endTs);
            videoCutCmdStr = videoCutCmdStr + "[0:v]trim=start=" + startTsStr + ":end=" + endTsStr + "[v" + i + "];";
            audioCutCmdStr = audioCutCmdStr + "[0:a]" + "atrim=start=" + (startTsStr) + ":end=" + NumberUtils.doubleToStr(endTs - 0.5) + "[a" + i + "];";
            audioMergeCmdStr += "[a" + i + "]";

            double offset = 0;
            if(i >= 2) {
                offset = preDuration - (i -1) * 0.5;
            }
            String offsetStr = NumberUtils.doubleToStr(offset);
            if(i==2) {
                String tranCode = effectDTOS.get(i - 2).getXfadeTransitionCode();
                transitionCmdStr = transitionCmdStr + "[v" + (i-1) + "]" + "[v" + i + "]xfade=transition=" + tranCode +
                        ":duration=0.5s:offset="+ offsetStr+"[v" +(i-1) + (i) +"_m];";
            }else if(i > 2 && i <effectDTOS.size()) {
                String tranCode = effectDTOS.get(i - 2).getXfadeTransitionCode();
                transitionCmdStr = transitionCmdStr + "[v" + (i-2) + (i-1)+ "_m]" + "[v" + i + "]xfade=transition=" + tranCode +
                        ":duration=0.5s:offset="+ offsetStr +"[v" +(i-1) + (i) +"_m];";
            }else if(i == effectDTOS.size()) {
                String tranCode = effectDTOS.get(i - 2).getXfadeTransitionCode();
                transitionCmdStr = transitionCmdStr + "[v" + (i-2) + (i-1)+ "_m]" + "[v" + i + "]xfade=transition=" + tranCode +
                        ":duration=0.5s:offset="+ offsetStr +"[tranV];";
            }
            preDuration = endTs;
        }
        audioMergeCmdStr = audioMergeCmdStr + "concat=n="+effectDTOS.size()+":v=0:a=1[a11];";
        String widthAndHeight = effectReq.getWidth() + ":"  + effectReq.getHeight();
        Integer stickerScale = (effectReq.getWidth() * 250) / 1980;
        //视频filter_complex参数拼接
        String filterCmd = videoCutCmdStr + transitionCmdStr +
                "[tranV]setpts=" + effectReq.getVideoPts() + "*PTS,eq=saturation=" + effectReq.getSaturationStr() + ":brightness=" +
                effectReq.getBrightnessStr() + ":contrast=" + effectReq.getContrastStr() +
                "[speedUpV];[2:v]scale=" + stickerScale + ":-1[stickerV];" +
                "[3:v]scale=280:-1[leftTopStickerV];" +
                "[4:v]scale=280:-1[leftBottomStickerV];" +
                "[5:v]scale=280:-1[rightTopStickerV];" +
                "[6:v]scale=280:-1[rightBottomStickerV];" +
                "[speedUpV][leftTopStickerV]overlay=50:50:shortest=1[leftTopV];" +
                "[leftTopV][leftBottomStickerV]overlay=50:H-280:shortest=1[leftBottomV];" +
                "[leftBottomV][rightTopStickerV]overlay=W-280:50:shortest=1[rightTopV];" +
                "[rightTopV][rightBottomStickerV]overlay=W-280:H-280:shortest=1[rightBottomV];" +
                "[rightBottomV][stickerV]overlay=W-w-50:H/3+50:shortest=1[out];";
//                "[3:v]scale="+widthAndHeight +",setsar=1[tailV];[overlayV][tailV]concat=n=2:v=1:a=0[out];";
        //音频filter_complex参数拼接
        filterCmd = filterCmd + audioCutCmdStr + audioMergeCmdStr + "[a11]volume=2[a111];[a111]atempo=" + effectReq.getAudioAtempo()+
                "[a12];[1:a]volume=0.1[a13];[a12][a13]amix=inputs=2:duration=first:dropout_transition=3[a];";
//                "[a14][3:a]concat=n=2:v=0:a=1[a]";
        String codec;
        if(ProcessorEnum.GPU.getValue().equals(processor)) {
            codec = "h264_nvenc";
        }else{
            codec = "h264";
        }
        ProcessBuilder process = new ProcessBuilder(
                "ffmpeg",
                "-i", input,
                "-i", effectReq.getMusicPath(),
                "-ignore_loop","0",
                "-i", effectReq.getStickerPath(),
                "-ignore_loop","0",
                "-i", effectReq.getLeftTopStickerPath(),
                "-ignore_loop","0",
                "-i", effectReq.getLeftBottomStickerPath(),
                "-ignore_loop","0",
                "-i", effectReq.getRightTopStickerPath(),
                "-ignore_loop","0",
                "-i", effectReq.getRightBottomStickerPath(),
//                        "-i", effectReq.getEndingVideoPath(),
                "-filter_complex", filterCmd ,
                "-c:v", codec,
                "-map","[out]",
                "-map","[a]",
                output, "-y");
        try {
            fFmpegCmd.printProcessExecResult(process.start());
        }catch (Exception e){
            log.error("insertSrt error", e);
            return false;
        }
        return true;
    }

    private String getTmpSrtVideoPath(String path) {
        int index = path.lastIndexOf(".");
        return path.substring(0, index) + "_srt.mp4";
    }

    private String getTmpCropVideoPath(String path) {
        int index = path.lastIndexOf(".");
        return path.substring(0, index) + "_flip.mp4";
    }

    private String createTmpSubtitlesFile(String tmpVideoPath, List<VideoClipSpeicalEffectDTO> videoEffectDTOs) {
        try {
            String path = tmpVideoPath + ".srt";
            File file = new File(path);
            BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(file));
            int i = 0;
            Integer duration = 0;
            for (VideoClipSpeicalEffectDTO effectDTO : videoEffectDTOs) {
                i++;
                VideoClipDTO videoClipDTO = effectDTO.getVideoClipDTO();
                List<SubtitlesCutDTO> cutDTOs = videoClipDTO.getSubtitlesCutDTOList();
                for (SubtitlesCutDTO subtitlesCutDTO : cutDTOs) {
                    bufferedWriter.write(i + "\n");
                    String startTs = TimeUtils.getTimeStrForSrtFile(duration);
                    String endTs = TimeUtils.getTimeStrForSrtFile(duration + (subtitlesCutDTO.getCutEndTs() -
                            subtitlesCutDTO.getCutStartTs()));
                    bufferedWriter.write(startTs + " --> " +  endTs + "\n");
                    bufferedWriter.write(subtitlesCutDTO.getContent() + "\n\n");
                    duration += subtitlesCutDTO.getDuration();
                }
                duration += 500;
            }
            bufferedWriter.flush();
            bufferedWriter.close();
            return path;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String createAssFiles(String tmpVideoPath, String[] keyswords, List<VideoClipSpeicalEffectDTO> videoEffectDTOs,
                                 FontEffectDTO fontEffectDTO) {
        // 创建Ass字幕头部信息
        StringBuilder assContent = new StringBuilder();
        assContent.append("[Script Info]\n");
        assContent.append("ScriptType: v4.00+\n");
        assContent.append("Collisions: Normal\n\n");
        assContent.append("[V4+ Styles]\n");
        assContent.append("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n");
        assContent.append("Style: Default,"+fontEffectDTO.getFontFamily()+",8,&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,2,2,10,10,10,1\n\n");
        assContent.append("[Events]\n");
        assContent.append("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n");

        try {
            String path = tmpVideoPath + ".ass";
            Integer duration = 0;
            for (VideoClipSpeicalEffectDTO effectDTO : videoEffectDTOs) {
                VideoClipDTO videoClipDTO = effectDTO.getVideoClipDTO();
                List<SubtitlesCutDTO> cutDTOs = videoClipDTO.getSubtitlesCutDTOList();
                for (SubtitlesCutDTO subtitlesCutDTO : cutDTOs) {
                    String line = subtitlesCutDTO.getContent();
                    if(keyswords != null) {
                        for(String keyword: keyswords) {
                            line = line.replaceAll(keyword, "{\\\\fs9\\\\c&H"+
                                    fontEffectDTO.getPrimaryColor()+"&}" + keyword +"{\\\\fs8\\\\c&HFFFFFF&}");
                        }
                    }
                    String startTs = TimeUtils.getTimeStrForSrtFile(duration);
                    String endTs = TimeUtils.getTimeStrForSrtFile(duration + (subtitlesCutDTO.getCutEndTs() -
                            subtitlesCutDTO.getCutStartTs()));
                    duration += subtitlesCutDTO.getDuration();
                    assContent.append(String.format("Dialogue: 0,%s,%s,Default,,0,0,50,,%s\n", startTs, endTs, line));
                }
                //因转场特效，每个片段之间的字幕需要加500毫秒的延迟
                duration += 500;
            }
            Files.write(Paths.get(path), assContent.toString().getBytes());
            return path;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }



    public static void main(String[] args) {
        try {
            String path = "/Users/<USER>/Documents/douyin/ending/dianjiguanzhu.mp4";
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(path);
            grabber.start();
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder("/Users/<USER>/Documents/douyin/dianjiguanzhu.mp4", grabber.getImageWidth(), grabber.getImageHeight(),grabber.getAudioChannels());
            recorder.setFormat("mp4");
            recorder.start();
            Frame frame;
            while((frame = grabber.grab())!= null) {
                recorder.record(frame);
            }
            recorder.release();
            recorder.stop();
        }catch (Exception e) {
            e.printStackTrace();
        }

    }

}
