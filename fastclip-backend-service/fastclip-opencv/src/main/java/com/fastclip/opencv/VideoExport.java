package com.fastclip.opencv;

import com.fastclip.common.effect.SpecialEffect;
import com.fastclip.common.model.dto.track.Track;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class VideoExport {

    public boolean export(List<Track> tracks){
        return true;
    }

}
