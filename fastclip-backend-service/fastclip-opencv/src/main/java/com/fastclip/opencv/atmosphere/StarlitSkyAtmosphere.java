package com.fastclip.opencv.atmosphere;


import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.RNG;
import org.bytedeco.opencv.opencv_core.Rect;
import org.bytedeco.opencv.opencv_core.Scalar;

import java.util.Random;

import static org.bytedeco.opencv.global.opencv_core.CV_8UC3;

public class StarlitSkyAtmosphere implements Atmosphere{

    private Integer offsets;

    public void setOffsets(Integer offsets) {
        this.offsets = offsets;
    }

    public Integer getOffsets() {
        return offsets;
    }

    @Override
    public Mat process(Mat img) {
        int height = img.rows();
        int width = img.cols();

        Mat mat = new Mat(width, height, CV_8UC3);

        RNG rng = new RNG(0xFFFFFFFF);

        // 循环处理每个像素
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                // 随机生成一个星星的大小
                int starSize = rng.uniform(2, 4);

                // 确定星星的位置
                Rect rect = new Rect(x - starSize / 2, y - starSize / 2, starSize, starSize);

                // 检查星星是否在图像边界内
                rect.x(Math.max(0, Math.min(width - 1, rect.x())));
                rect.y(Math.max(0, Math.min(height - 1, rect.y())));
                rect.width(Math.min(width - rect.x(), rect.width()));
                rect.height(Math.min(height - rect.y(), rect.height()));

                // 将星星的颜色填充到mat对象中
                Scalar color = new Scalar(rng.uniform(0, 255), rng.uniform(0, 255), rng.uniform(0, 255), rng.uniform(0, 255));
                mat.put(color);
            }
        }
        return mat;
    }
}
