<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- 数据库驱动-->
    <classPathEntry  location="/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar"/>
    <context id="DB2Tables"  targetRuntime="MyBatis3">
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin" />
        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <!--数据库链接URL，用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver" connectionURL="************************************" userId="root" password="root" />
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- 生成模型的包名和位置-->
        <javaModelGenerator targetPackage="com.fastclip.dao.model.dataobject" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!-- 生成映射文件的包名和位置-->
        <sqlMapGenerator targetPackage="mappers" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!-- 生成DAO的包名和位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.fastclip.dao.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!--        <table tableName="subtitles" domainObjectName="Subtitles" enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true"></table>-->
<!--                <table tableName="video_material" domainObjectName="VideoMaterial"><generatedKey column="id" sqlStatement="MySql" identity="true"/></table>-->
        <!--        <table tableName="video_material_slice" domainObjectName="VideoMaterialSlice"><generatedKey column="id" sqlStatement="MySql" identity="true"/></table>-->
        <!--        <table tableName="video_material_clip" domainObjectName="VideoMaterialClip" enableCountByExample="true" enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true"></table>-->
<!--                    <table tableName="project" domainObjectName="Project"></table>-->
<!--                <table tableName="seller" domainObjectName="Seller"></table>-->
<!--                <table tableName="item" domainObjectName="Item"><generatedKey column="id" sqlStatement="MySql" identity="true"/></table>-->
        <!--        <table tableName="subtitles_cut" domainObjectName="SubtitlesCut"><generatedKey column="id" sqlStatement="MySql" identity="true"/></table>-->
        <!--        <table tableName="video_clip_details" domainObjectName="VideoClipDetails"></table>-->
        <!--        <table tableName="video_clip" domainObjectName="VideoClip"><generatedKey column="id" sqlStatement="MySql" identity="true"/></table>-->
        <!--        <table tableName="video_clip_tag" domainObjectName="VideoClipTag"></table>-->
<!--                    <table tableName="works" domainObjectName="Works"><generatedKey column="id" sqlStatement="MySql" identity="true"/></table>-->
        <!--            <table tableName="works_detail" domainObjectName="WorksDetail"></table>-->
        <!--        <table tableName="music" domainObjectName="Music"><generatedKey column="id" sqlStatement="MySql" identity="true"/></table>-->
        <!--        <table tableName="filter_effect" domainObjectName="FilterEffect"></table>-->
<!--                    <table tableName="sticker" domainObjectName="Sticker"></table>-->
<!--                    <table tableName="douyin_account" domainObjectName="DouyinAccount"></table>-->
        <!--        <table tableName="item_on_live" domainObjectName="ItemOnLive"></table>-->
        <table tableName="f_user" domainObjectName="FUser"></table>
        <!--        <table tableName="token" domainObjectName="Token"></table>-->
<!--        <table tableName="font_effect" domainObjectName="FontEffect"></table>-->
<!--                <table tableName="download_log" domainObjectName="DownloadLog"></table>-->
<!--        <table tableName="base_cover" domainObjectName="BaseCover"></table>-->
<!--                <table tableName="orders" domainObjectName="Orders"></table>-->
<!--                        <table tableName="qr_code" domainObjectName="QRCode"></table>-->
<!--        <table tableName="team" domainObjectName="Team"></table>-->

    </context>
</generatorConfiguration>