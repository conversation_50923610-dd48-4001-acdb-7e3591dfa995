<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fastclip.dao.mapper.ChanxuanMapper">

    <insert id="insertOrUpdateCxClip" parameterType="com.fastclip.common.model.dataobject.VideoClipDO" useGeneratedKeys="true" keyProperty="id">
        insert into cx_clip(cx_account, star_id, star_name, clip_size, duration, clip_tag, cover, room_id, start_ts,
                             task_id, title, url, product_id, product_name,
                             local_audio_path, local_video_path, local_srt_path)
        values
        <foreach collection ="chanxuanClipDOs" item="clip" separator =",">
        (#{clip.cxAccount}, #{clip.starId}, #{clip.starName}, #{clip.clipSize}, #{clip.duration},#{clip.clipTag}, #{clip.cover},
         #{clip.roomId},#{clip.startTs},#{clip.taskId},#{clip.title}, #{clip.url},#{clip.productId},#{clip.productName},
         #{clip.localAudioPath},#{clip.localVideoPath},#{clip.localSrtPath})
        </foreach>
        ON DUPLICATE KEY UPDATE
        local_audio_path = VALUES(local_audio_path),
        local_video_path = VALUES(local_video_path),
        local_srt_path = VALUES(local_srt_path),
        update_time=now()
    </insert>


    <insert id="insertOrUpdateCxItem" parameterType="com.fastclip.common.model.dataobject.ChanxuanItemDO" useGeneratedKeys="true" keyProperty="id">
        insert into cx_item(cx_account, star_id, star_name, product_id, material_product_id, price, product_name,
                            product_url, cj_material_link, presell_type, cut_video_path)
        values
        <foreach collection ="chanxuanItems" item="item" separator =",">
            (#{item.cxAccount}, #{item.starId}, #{item.starName}, #{item.productId}, #{item.materialProductId},
             #{item.price}, #{item.productName},#{item.productUrl},#{item.cjMaterialLink},#{item.presellType},
               #{item.cutVideoPath})
        </foreach>
        ON DUPLICATE KEY UPDATE
        presell_type = VALUES(presell_type),
        cut_video_path = VALUES(cut_video_path),
        update_time=now()
    </insert>

    <insert id="insertCxCutClip" parameterType="com.fastclip.common.model.dataobject.ChanxuanCutClipDO" useGeneratedKeys="true" keyProperty="id">
        insert into cx_cut_clip(cx_account, star_id, star_name, product_id, material_product_id, product_name,
        product_url, cut_video_path, task_id, clip_size,duration, title, description)
        values
        <foreach collection ="chanxuanCutClipDOS" item="item" separator =",">
            (#{item.cxAccount}, #{item.starId}, #{item.starName}, #{item.productId}, #{item.materialProductId},
            #{item.productName},#{item.productUrl},#{item.cutVideoPath},#{item.taskId},#{item.clipSize},#{item.duration},
            #{item.title}, #{item.description})
        </foreach>
    </insert>

    <select id="getItemsNoCut" resultType="com.fastclip.common.model.dataobject.ChanxuanItemDO">
        select id, cx_account as cxAccount, star_id as starId, star_name as starName,
               product_id as productId, material_product_id as materialProductId,
               price, product_name as productName, product_url as productUrl, cj_material_link as cjMaterialLink,
               presell_type as presellType, cut_video_path as cutVideoPath
        from cx_item
        where (cut_video_path is null || cut_video_path='') and cx_account=#{cxAccount}
        limit 10
    </select>

    <select id="getItemByMaterialId" parameterType="java.lang.String" resultType="com.fastclip.common.model.dataobject.ChanxuanItemDO">
        select id, cx_account as cxAccount, star_id as starId, star_name as starName,
               product_id as productId, material_product_id as materialProductId,
               price, product_name as productName, product_url as productUrl, cj_material_link as cjMaterialLink,
               presell_type as presellType, cut_video_path as cutVideoPath
        from cx_item
        where material_product_id = #{materialId}
    </select>

    <select id="getItem" parameterType="java.lang.String" resultType="com.fastclip.common.model.dataobject.ChanxuanItemDO">
        select id, cx_account as cxAccount, star_id as starId, star_name as starName,
               product_id as productId, material_product_id as materialProductId,
               price, product_name as productName, product_url as productUrl, cj_material_link as cjMaterialLink,
               presell_type as presellType, cut_video_path as cutVideoPath
        from cx_item
        where product_id = #{productId}
    </select>

    <select id="getClipByTaskId" parameterType="java.lang.String" resultType="com.fastclip.common.model.dataobject.VideoClipDO">
        select id, cx_account as cxAccount, star_id as startId, star_name as starName, clip_size as clipSize, duration,
               clip_tag as clipTag, cover, room_id as roomId, start_ts as startTs,
               task_id as taskId, title, url, product_id as productId, product_name as productName,
               local_audio_path as localAudioPath, local_video_path as localVideoPath, local_srt_path as localSrtPath
        from cx_clip
        where task_id = #{taskId}
    </select>

    <select id="getCutClipByTaskId" resultType="com.fastclip.common.model.dataobject.ChanxuanCutClipDO">
        select cx_account as cxAccount, star_id as startId, star_name as starName, product_id as productId,
               material_product_id as materialProductId, product_name as productName, product_url as productUrl,
               cut_video_path as  cutVideoPath, task_id as taskId, is_publish as isPublish
        from cx_cut_clip
        where task_id = #{taskId}
    </select>


    <select id="getClipWithoutSrt" parameterType="java.lang.Integer" resultType="com.fastclip.common.model.dataobject.VideoClipDO">
        select id, cx_account as cxAccount, star_id as starId, star_name as starName, clip_size as clipSize, duration,
               clip_tag as clipTag, cover, room_id as roomId, start_ts as startTs,
               task_id as taskId, title, url, product_id as productId, product_name as productName,
               local_audio_path as localAudioPath, local_video_path as localVideoPath, local_srt_path as localSrtPath
        from cx_clip
        where local_srt_path is null or local_srt_path='' order by create_time desc
        limit #{limit}
    </select>

    <select id="getClipWithoutSrtByProductId" parameterType="java.lang.String" resultType="com.fastclip.common.model.dataobject.VideoClipDO">
        select id, cx_account as cxAccount, star_id as starId, star_name as starName, clip_size as clipSize, duration,
               clip_tag as clipTag, cover, room_id as roomId, start_ts as startTs,
               task_id as taskId, title, url, product_id as productId, product_name as productName,
               local_audio_path as localAudioPath, local_video_path as localVideoPath, local_srt_path as localSrtPath
        from cx_clip
        where local_srt_path is null and product_id=#{productId} order by create_time desc
    </select>

    <select id="getClipsByProductId" parameterType="java.lang.String" resultType="com.fastclip.common.model.dataobject.VideoClipDO">
        select id, cx_account as cxAccount, star_id as startId, star_name as starName, clip_size as clipSize, duration,
               clip_tag as clipTag, cover, room_id as roomId, start_ts as startTs,
               task_id as taskId, title, url, product_id as productId, product_name as productName,
               local_audio_path as localAudioPath, local_video_path as localVideoPath, local_srt_path as localSrtPath
        from cx_clip
        where product_id = #{productId} and local_srt_path is not null
    </select>

    <select id="getCountClipsByProductId" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from cx_clip
        where product_id = #{productId}
    </select>

    <select id="getAllClips" resultType="com.fastclip.common.model.dataobject.VideoClipDO">
        select id, cx_account as cxAccount, star_id as startId, star_name as starName, clip_size as clipSize, duration,
               clip_tag as clipTag, cover, room_id as roomId, start_ts as startTs,
               task_id as taskId, title, url, product_id as productId, product_name as productName,
               local_audio_path as localAudioPath, local_video_path as localVideoPath, local_srt_path as localSrtPath
        from cx_clip
    </select>

    <select id="getClipsByPage" parameterType="com.fastclip.common.model.request.VideoClipsReq" resultType="com.fastclip.common.model.dataobject.VideoClipDO">
        select id, cx_account as cxAccount, star_id as startId, star_name as starName, clip_size as clipSize, duration,
               clip_tag as clipTag, cover, room_id as roomId, start_ts as startTs,
               task_id as taskId, title, url, product_id as productId, product_name as productName,
               local_audio_path as localAudioPath, local_video_path as localVideoPath, local_srt_path as localSrtPath
        from cx_clip order by create_time desc limit #{req.startIndex}, #{req.endIndex}
    </select>

    <select id="getAllClipsNoCut" resultType="com.fastclip.common.model.dataobject.VideoClipDO">
        select clip.id, clip.cx_account as cxAccount, clip.star_id as startId, clip.star_name as starName,
               clip.clip_size as clipSize, clip.duration, clip.clip_tag as clipTag, clip.cover, clip.room_id as roomId,
               clip.start_ts as startTs,clip.task_id as taskId, clip.title, clip.url, clip.product_id as productId,
               clip.product_name as productName,clip.local_audio_path as localAudioPath, clip.local_video_path as localVideoPath,
               clip.local_srt_path as localSrtPath, cut_clip.cut_video_path as cutVideoPath

        from cx_clip clip left join cx_cut_clip cut_clip on clip.task_id=cut_clip.task_id where local_srt_path
            is not null and cut_video_path is null and clip.cx_account=#{cxAccount} order by clip.create_time desc
    </select>

    <delete id="deleteClip" parameterType="java.lang.Integer">
        delete from cx_clip
        where id = #{id}
    </delete>

    <delete id="deleteItem" parameterType="java.lang.Integer">
        delete from cx_item
        where id = #{id}
    </delete>

    <select id="getAllItems" resultType="com.fastclip.common.model.dataobject.ChanxuanItemDO">
        select id, cx_account as cxAccount, star_id as starId, star_name as starName,
               product_id as productId, material_product_id as materialProductId,
               price, product_name as productName, product_url as productUrl, cj_material_link as cjMaterialLink,
               presell_type as presellType, cut_video_path as cutVideoPath
        from cx_item
    </select>

</mapper>