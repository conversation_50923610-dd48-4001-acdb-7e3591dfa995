<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.VideoClipMapperExt">
  <update id="updateSort" parameterType="com.fastclip.dao.model.dataobject.UpdateSortReq">
    update video_clip
    set
      sort = sort + #{req.sortAdded},
      update_time = NOW()
    where sort > #{req.curSort}
  </update>

  <update id="batchUpdateByPrimaryKey" parameterType="java.util.List">
      <foreach collection="list" separator=";" item="item">
        update video_clip
        set
        start_subtitles_cut_id = #{item.startSubtitlesCutId},
        end_subtitles_cut_id = #{item.endSubtitlesCutId},
        start_subtitles_id = #{item.startSubtitlesId},
        end_subtitles_id = #{item.endSubtitlesId},
        subtitles = #{item.subtitles},
        duration = #{item.duration},
        subtitles_cut_count = #{item.subtitlesCutCount},
        sort = #{item.sort},
        update_time = NOW()
        where id =
        #{item.id}
      </foreach>
  </update>
</mapper>