<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.OrdersMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.Orders">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_img" jdbcType="VARCHAR" property="productImg" />
    <result column="author_account" jdbcType="VARCHAR" property="authorAccount" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="total_pay_amount" jdbcType="BIGINT" property="totalPayAmount" />
    <result column="commission_rate" jdbcType="BIGINT" property="commissionRate" />
    <result column="flow_point" jdbcType="VARCHAR" property="flowPoint" />
    <result column="app" jdbcType="VARCHAR" property="app" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_success_time" jdbcType="TIMESTAMP" property="paySuccessTime" />
    <result column="settle_time" jdbcType="TIMESTAMP" property="settleTime" />
    <result column="pay_goods_amount" jdbcType="BIGINT" property="payGoodsAmount" />
    <result column="settled_goods_amount" jdbcType="BIGINT" property="settledGoodsAmount" />
    <result column="real_commission" jdbcType="BIGINT" property="realCommission" />
    <result column="estimated_commission" jdbcType="BIGINT" property="estimatedCommission" />
    <result column="item_num" jdbcType="BIGINT" property="itemNum" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="estimated_total_commission" jdbcType="BIGINT" property="estimatedTotalCommission" />
    <result column="estimated_tech_service_fee" jdbcType="BIGINT" property="estimatedTechServiceFee" />
    <result column="pick_source_client_key" jdbcType="VARCHAR" property="pickSourceClientKey" />
    <result column="pick_extra" jdbcType="VARCHAR" property="pickExtra" />
    <result column="author_short_id" jdbcType="VARCHAR" property="authorShortId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, product_id, product_name, product_img, author_account, shop_name, total_pay_amount, 
    commission_rate, flow_point, app, update_time, pay_success_time, settle_time, pay_goods_amount, 
    settled_goods_amount, real_commission, estimated_commission, item_num, shop_id, refund_time, 
    estimated_total_commission, estimated_tech_service_fee, pick_source_client_key, pick_extra, 
    author_short_id
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.OrdersExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from orders
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from orders
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from orders
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.OrdersExample">
    delete from orders
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.Orders">
    insert into orders (id, order_id, product_id, 
      product_name, product_img, author_account, 
      shop_name, total_pay_amount, commission_rate, 
      flow_point, app, update_time, 
      pay_success_time, settle_time, pay_goods_amount, 
      settled_goods_amount, real_commission, estimated_commission, 
      item_num, shop_id, refund_time, 
      estimated_total_commission, estimated_tech_service_fee, 
      pick_source_client_key, pick_extra, author_short_id
      )
    values (#{id,jdbcType=BIGINT}, #{orderId,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{productImg,jdbcType=VARCHAR}, #{authorAccount,jdbcType=VARCHAR}, 
      #{shopName,jdbcType=VARCHAR}, #{totalPayAmount,jdbcType=BIGINT}, #{commissionRate,jdbcType=BIGINT}, 
      #{flowPoint,jdbcType=VARCHAR}, #{app,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{paySuccessTime,jdbcType=TIMESTAMP}, #{settleTime,jdbcType=TIMESTAMP}, #{payGoodsAmount,jdbcType=BIGINT}, 
      #{settledGoodsAmount,jdbcType=BIGINT}, #{realCommission,jdbcType=BIGINT}, #{estimatedCommission,jdbcType=BIGINT}, 
      #{itemNum,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{refundTime,jdbcType=TIMESTAMP}, 
      #{estimatedTotalCommission,jdbcType=BIGINT}, #{estimatedTechServiceFee,jdbcType=BIGINT}, 
      #{pickSourceClientKey,jdbcType=VARCHAR}, #{pickExtra,jdbcType=VARCHAR}, #{authorShortId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.Orders">
    insert into orders
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="productImg != null">
        product_img,
      </if>
      <if test="authorAccount != null">
        author_account,
      </if>
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="totalPayAmount != null">
        total_pay_amount,
      </if>
      <if test="commissionRate != null">
        commission_rate,
      </if>
      <if test="flowPoint != null">
        flow_point,
      </if>
      <if test="app != null">
        app,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="paySuccessTime != null">
        pay_success_time,
      </if>
      <if test="settleTime != null">
        settle_time,
      </if>
      <if test="payGoodsAmount != null">
        pay_goods_amount,
      </if>
      <if test="settledGoodsAmount != null">
        settled_goods_amount,
      </if>
      <if test="realCommission != null">
        real_commission,
      </if>
      <if test="estimatedCommission != null">
        estimated_commission,
      </if>
      <if test="itemNum != null">
        item_num,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="estimatedTotalCommission != null">
        estimated_total_commission,
      </if>
      <if test="estimatedTechServiceFee != null">
        estimated_tech_service_fee,
      </if>
      <if test="pickSourceClientKey != null">
        pick_source_client_key,
      </if>
      <if test="pickExtra != null">
        pick_extra,
      </if>
      <if test="authorShortId != null">
        author_short_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productImg != null">
        #{productImg,jdbcType=VARCHAR},
      </if>
      <if test="authorAccount != null">
        #{authorAccount,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="totalPayAmount != null">
        #{totalPayAmount,jdbcType=BIGINT},
      </if>
      <if test="commissionRate != null">
        #{commissionRate,jdbcType=BIGINT},
      </if>
      <if test="flowPoint != null">
        #{flowPoint,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        #{app,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paySuccessTime != null">
        #{paySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleTime != null">
        #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payGoodsAmount != null">
        #{payGoodsAmount,jdbcType=BIGINT},
      </if>
      <if test="settledGoodsAmount != null">
        #{settledGoodsAmount,jdbcType=BIGINT},
      </if>
      <if test="realCommission != null">
        #{realCommission,jdbcType=BIGINT},
      </if>
      <if test="estimatedCommission != null">
        #{estimatedCommission,jdbcType=BIGINT},
      </if>
      <if test="itemNum != null">
        #{itemNum,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedTotalCommission != null">
        #{estimatedTotalCommission,jdbcType=BIGINT},
      </if>
      <if test="estimatedTechServiceFee != null">
        #{estimatedTechServiceFee,jdbcType=BIGINT},
      </if>
      <if test="pickSourceClientKey != null">
        #{pickSourceClientKey,jdbcType=VARCHAR},
      </if>
      <if test="pickExtra != null">
        #{pickExtra,jdbcType=VARCHAR},
      </if>
      <if test="authorShortId != null">
        #{authorShortId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.OrdersExample" resultType="java.lang.Long">
    select count(*) from orders
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update orders
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.productImg != null">
        product_img = #{record.productImg,jdbcType=VARCHAR},
      </if>
      <if test="record.authorAccount != null">
        author_account = #{record.authorAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.shopName != null">
        shop_name = #{record.shopName,jdbcType=VARCHAR},
      </if>
      <if test="record.totalPayAmount != null">
        total_pay_amount = #{record.totalPayAmount,jdbcType=BIGINT},
      </if>
      <if test="record.commissionRate != null">
        commission_rate = #{record.commissionRate,jdbcType=BIGINT},
      </if>
      <if test="record.flowPoint != null">
        flow_point = #{record.flowPoint,jdbcType=VARCHAR},
      </if>
      <if test="record.app != null">
        app = #{record.app,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paySuccessTime != null">
        pay_success_time = #{record.paySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settleTime != null">
        settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payGoodsAmount != null">
        pay_goods_amount = #{record.payGoodsAmount,jdbcType=BIGINT},
      </if>
      <if test="record.settledGoodsAmount != null">
        settled_goods_amount = #{record.settledGoodsAmount,jdbcType=BIGINT},
      </if>
      <if test="record.realCommission != null">
        real_commission = #{record.realCommission,jdbcType=BIGINT},
      </if>
      <if test="record.estimatedCommission != null">
        estimated_commission = #{record.estimatedCommission,jdbcType=BIGINT},
      </if>
      <if test="record.itemNum != null">
        item_num = #{record.itemNum,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.estimatedTotalCommission != null">
        estimated_total_commission = #{record.estimatedTotalCommission,jdbcType=BIGINT},
      </if>
      <if test="record.estimatedTechServiceFee != null">
        estimated_tech_service_fee = #{record.estimatedTechServiceFee,jdbcType=BIGINT},
      </if>
      <if test="record.pickSourceClientKey != null">
        pick_source_client_key = #{record.pickSourceClientKey,jdbcType=VARCHAR},
      </if>
      <if test="record.pickExtra != null">
        pick_extra = #{record.pickExtra,jdbcType=VARCHAR},
      </if>
      <if test="record.authorShortId != null">
        author_short_id = #{record.authorShortId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update orders
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      product_img = #{record.productImg,jdbcType=VARCHAR},
      author_account = #{record.authorAccount,jdbcType=VARCHAR},
      shop_name = #{record.shopName,jdbcType=VARCHAR},
      total_pay_amount = #{record.totalPayAmount,jdbcType=BIGINT},
      commission_rate = #{record.commissionRate,jdbcType=BIGINT},
      flow_point = #{record.flowPoint,jdbcType=VARCHAR},
      app = #{record.app,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      pay_success_time = #{record.paySuccessTime,jdbcType=TIMESTAMP},
      settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      pay_goods_amount = #{record.payGoodsAmount,jdbcType=BIGINT},
      settled_goods_amount = #{record.settledGoodsAmount,jdbcType=BIGINT},
      real_commission = #{record.realCommission,jdbcType=BIGINT},
      estimated_commission = #{record.estimatedCommission,jdbcType=BIGINT},
      item_num = #{record.itemNum,jdbcType=BIGINT},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      estimated_total_commission = #{record.estimatedTotalCommission,jdbcType=BIGINT},
      estimated_tech_service_fee = #{record.estimatedTechServiceFee,jdbcType=BIGINT},
      pick_source_client_key = #{record.pickSourceClientKey,jdbcType=VARCHAR},
      pick_extra = #{record.pickExtra,jdbcType=VARCHAR},
      author_short_id = #{record.authorShortId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.Orders">
    update orders
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productImg != null">
        product_img = #{productImg,jdbcType=VARCHAR},
      </if>
      <if test="authorAccount != null">
        author_account = #{authorAccount,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="totalPayAmount != null">
        total_pay_amount = #{totalPayAmount,jdbcType=BIGINT},
      </if>
      <if test="commissionRate != null">
        commission_rate = #{commissionRate,jdbcType=BIGINT},
      </if>
      <if test="flowPoint != null">
        flow_point = #{flowPoint,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        app = #{app,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paySuccessTime != null">
        pay_success_time = #{paySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleTime != null">
        settle_time = #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payGoodsAmount != null">
        pay_goods_amount = #{payGoodsAmount,jdbcType=BIGINT},
      </if>
      <if test="settledGoodsAmount != null">
        settled_goods_amount = #{settledGoodsAmount,jdbcType=BIGINT},
      </if>
      <if test="realCommission != null">
        real_commission = #{realCommission,jdbcType=BIGINT},
      </if>
      <if test="estimatedCommission != null">
        estimated_commission = #{estimatedCommission,jdbcType=BIGINT},
      </if>
      <if test="itemNum != null">
        item_num = #{itemNum,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedTotalCommission != null">
        estimated_total_commission = #{estimatedTotalCommission,jdbcType=BIGINT},
      </if>
      <if test="estimatedTechServiceFee != null">
        estimated_tech_service_fee = #{estimatedTechServiceFee,jdbcType=BIGINT},
      </if>
      <if test="pickSourceClientKey != null">
        pick_source_client_key = #{pickSourceClientKey,jdbcType=VARCHAR},
      </if>
      <if test="pickExtra != null">
        pick_extra = #{pickExtra,jdbcType=VARCHAR},
      </if>
      <if test="authorShortId != null">
        author_short_id = #{authorShortId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.Orders">
    update orders
    set order_id = #{orderId,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      product_img = #{productImg,jdbcType=VARCHAR},
      author_account = #{authorAccount,jdbcType=VARCHAR},
      shop_name = #{shopName,jdbcType=VARCHAR},
      total_pay_amount = #{totalPayAmount,jdbcType=BIGINT},
      commission_rate = #{commissionRate,jdbcType=BIGINT},
      flow_point = #{flowPoint,jdbcType=VARCHAR},
      app = #{app,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pay_success_time = #{paySuccessTime,jdbcType=TIMESTAMP},
      settle_time = #{settleTime,jdbcType=TIMESTAMP},
      pay_goods_amount = #{payGoodsAmount,jdbcType=BIGINT},
      settled_goods_amount = #{settledGoodsAmount,jdbcType=BIGINT},
      real_commission = #{realCommission,jdbcType=BIGINT},
      estimated_commission = #{estimatedCommission,jdbcType=BIGINT},
      item_num = #{itemNum,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      estimated_total_commission = #{estimatedTotalCommission,jdbcType=BIGINT},
      estimated_tech_service_fee = #{estimatedTechServiceFee,jdbcType=BIGINT},
      pick_source_client_key = #{pickSourceClientKey,jdbcType=VARCHAR},
      pick_extra = #{pickExtra,jdbcType=VARCHAR},
      author_short_id = #{authorShortId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.OrdersExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from orders
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>