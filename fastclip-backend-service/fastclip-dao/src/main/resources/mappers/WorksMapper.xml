<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.WorksMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.Works">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="special_effects_template_code" jdbcType="VARCHAR" property="specialEffectsTemplateCode" />
    <result column="is_composed" jdbcType="BIT" property="isComposed" />
    <result column="is_published" jdbcType="BIT" property="isPublished" />
    <result column="video_path" jdbcType="VARCHAR" property="videoPath" />
    <result column="music_id" jdbcType="INTEGER" property="musicId" />
    <result column="filter_id" jdbcType="INTEGER" property="filterId" />
    <result column="sticker_id" jdbcType="INTEGER" property="stickerId" />
    <result column="speed_up" jdbcType="INTEGER" property="speedUp" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="works_name" jdbcType="VARCHAR" property="worksName" />
    <result column="works_desc" jdbcType="VARCHAR" property="worksDesc" />
    <result column="works_cover" jdbcType="VARCHAR" property="worksCover" />
    <result column="simple_item_name" jdbcType="VARCHAR" property="simpleItemName" />
    <result column="works_tag" jdbcType="VARCHAR" property="worksTag" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="published_time" jdbcType="TIMESTAMP" property="publishedTime" />
    <result column="item_features" jdbcType="VARCHAR" property="itemFeatures" />
    <result column="item_tags" jdbcType="VARCHAR" property="itemTags" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="font_id" jdbcType="INTEGER" property="fontId" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="is_desc_completed" jdbcType="BIT" property="isDescCompleted" />
    <result column="left_top_sticker_id" jdbcType="INTEGER" property="leftTopStickerId" />
    <result column="right_top_sticker_id" jdbcType="INTEGER" property="rightTopStickerId" />
    <result column="left_bottom_sticker_id" jdbcType="INTEGER" property="leftBottomStickerId" />
    <result column="right_bottom_sticker_id" jdbcType="INTEGER" property="rightBottomStickerId" />
    <result column="video_path_without_ass" jdbcType="VARCHAR" property="videoPathWithoutAss" />
    <result column="compose_status" jdbcType="INTEGER" property="composeStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, item_id, seller_id, duration, special_effects_template_code, is_composed, 
    is_published, video_path, music_id, filter_id, sticker_id, speed_up, create_time, 
    update_time, works_name, works_desc, works_cover, simple_item_name, works_tag, account_id, 
    phone, published_time, item_features, item_tags, creator_id, font_id, keywords, is_desc_completed, 
    left_top_sticker_id, right_top_sticker_id, left_bottom_sticker_id, right_bottom_sticker_id, 
    video_path_without_ass, compose_status
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.WorksExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from works
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from works
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from works
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.WorksExample">
    delete from works
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.Works">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into works (project_id, item_id, seller_id, 
      duration, special_effects_template_code, is_composed, 
      is_published, video_path, music_id, 
      filter_id, sticker_id, speed_up, 
      create_time, update_time, works_name, 
      works_desc, works_cover, simple_item_name, 
      works_tag, account_id, phone, 
      published_time, item_features, item_tags, 
      creator_id, font_id, keywords, 
      is_desc_completed, left_top_sticker_id, right_top_sticker_id, 
      left_bottom_sticker_id, right_bottom_sticker_id, 
      video_path_without_ass, compose_status)
    values (#{projectId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}, #{sellerId,jdbcType=BIGINT}, 
      #{duration,jdbcType=INTEGER}, #{specialEffectsTemplateCode,jdbcType=VARCHAR}, #{isComposed,jdbcType=BIT}, 
      #{isPublished,jdbcType=BIT}, #{videoPath,jdbcType=VARCHAR}, #{musicId,jdbcType=INTEGER}, 
      #{filterId,jdbcType=INTEGER}, #{stickerId,jdbcType=INTEGER}, #{speedUp,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{worksName,jdbcType=VARCHAR}, 
      #{worksDesc,jdbcType=VARCHAR}, #{worksCover,jdbcType=VARCHAR}, #{simpleItemName,jdbcType=VARCHAR}, 
      #{worksTag,jdbcType=VARCHAR}, #{accountId,jdbcType=BIGINT}, #{phone,jdbcType=VARCHAR}, 
      #{publishedTime,jdbcType=TIMESTAMP}, #{itemFeatures,jdbcType=VARCHAR}, #{itemTags,jdbcType=VARCHAR}, 
      #{creatorId,jdbcType=BIGINT}, #{fontId,jdbcType=INTEGER}, #{keywords,jdbcType=VARCHAR}, 
      #{isDescCompleted,jdbcType=BIT}, #{leftTopStickerId,jdbcType=INTEGER}, #{rightTopStickerId,jdbcType=INTEGER}, 
      #{leftBottomStickerId,jdbcType=INTEGER}, #{rightBottomStickerId,jdbcType=INTEGER}, 
      #{videoPathWithoutAss,jdbcType=VARCHAR}, #{composeStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.Works">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into works
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="specialEffectsTemplateCode != null">
        special_effects_template_code,
      </if>
      <if test="isComposed != null">
        is_composed,
      </if>
      <if test="isPublished != null">
        is_published,
      </if>
      <if test="videoPath != null">
        video_path,
      </if>
      <if test="musicId != null">
        music_id,
      </if>
      <if test="filterId != null">
        filter_id,
      </if>
      <if test="stickerId != null">
        sticker_id,
      </if>
      <if test="speedUp != null">
        speed_up,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="worksName != null">
        works_name,
      </if>
      <if test="worksDesc != null">
        works_desc,
      </if>
      <if test="worksCover != null">
        works_cover,
      </if>
      <if test="simpleItemName != null">
        simple_item_name,
      </if>
      <if test="worksTag != null">
        works_tag,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="publishedTime != null">
        published_time,
      </if>
      <if test="itemFeatures != null">
        item_features,
      </if>
      <if test="itemTags != null">
        item_tags,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="fontId != null">
        font_id,
      </if>
      <if test="keywords != null">
        keywords,
      </if>
      <if test="isDescCompleted != null">
        is_desc_completed,
      </if>
      <if test="leftTopStickerId != null">
        left_top_sticker_id,
      </if>
      <if test="rightTopStickerId != null">
        right_top_sticker_id,
      </if>
      <if test="leftBottomStickerId != null">
        left_bottom_sticker_id,
      </if>
      <if test="rightBottomStickerId != null">
        right_bottom_sticker_id,
      </if>
      <if test="videoPathWithoutAss != null">
        video_path_without_ass,
      </if>
      <if test="composeStatus != null">
        compose_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="specialEffectsTemplateCode != null">
        #{specialEffectsTemplateCode,jdbcType=VARCHAR},
      </if>
      <if test="isComposed != null">
        #{isComposed,jdbcType=BIT},
      </if>
      <if test="isPublished != null">
        #{isPublished,jdbcType=BIT},
      </if>
      <if test="videoPath != null">
        #{videoPath,jdbcType=VARCHAR},
      </if>
      <if test="musicId != null">
        #{musicId,jdbcType=INTEGER},
      </if>
      <if test="filterId != null">
        #{filterId,jdbcType=INTEGER},
      </if>
      <if test="stickerId != null">
        #{stickerId,jdbcType=INTEGER},
      </if>
      <if test="speedUp != null">
        #{speedUp,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksName != null">
        #{worksName,jdbcType=VARCHAR},
      </if>
      <if test="worksDesc != null">
        #{worksDesc,jdbcType=VARCHAR},
      </if>
      <if test="worksCover != null">
        #{worksCover,jdbcType=VARCHAR},
      </if>
      <if test="simpleItemName != null">
        #{simpleItemName,jdbcType=VARCHAR},
      </if>
      <if test="worksTag != null">
        #{worksTag,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="publishedTime != null">
        #{publishedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="itemFeatures != null">
        #{itemFeatures,jdbcType=VARCHAR},
      </if>
      <if test="itemTags != null">
        #{itemTags,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="fontId != null">
        #{fontId,jdbcType=INTEGER},
      </if>
      <if test="keywords != null">
        #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="isDescCompleted != null">
        #{isDescCompleted,jdbcType=BIT},
      </if>
      <if test="leftTopStickerId != null">
        #{leftTopStickerId,jdbcType=INTEGER},
      </if>
      <if test="rightTopStickerId != null">
        #{rightTopStickerId,jdbcType=INTEGER},
      </if>
      <if test="leftBottomStickerId != null">
        #{leftBottomStickerId,jdbcType=INTEGER},
      </if>
      <if test="rightBottomStickerId != null">
        #{rightBottomStickerId,jdbcType=INTEGER},
      </if>
      <if test="videoPathWithoutAss != null">
        #{videoPathWithoutAss,jdbcType=VARCHAR},
      </if>
      <if test="composeStatus != null">
        #{composeStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.WorksExample" resultType="java.lang.Long">
    select count(*) from works
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update works
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=BIGINT},
      </if>
      <if test="record.sellerId != null">
        seller_id = #{record.sellerId,jdbcType=BIGINT},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.specialEffectsTemplateCode != null">
        special_effects_template_code = #{record.specialEffectsTemplateCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isComposed != null">
        is_composed = #{record.isComposed,jdbcType=BIT},
      </if>
      <if test="record.isPublished != null">
        is_published = #{record.isPublished,jdbcType=BIT},
      </if>
      <if test="record.videoPath != null">
        video_path = #{record.videoPath,jdbcType=VARCHAR},
      </if>
      <if test="record.musicId != null">
        music_id = #{record.musicId,jdbcType=INTEGER},
      </if>
      <if test="record.filterId != null">
        filter_id = #{record.filterId,jdbcType=INTEGER},
      </if>
      <if test="record.stickerId != null">
        sticker_id = #{record.stickerId,jdbcType=INTEGER},
      </if>
      <if test="record.speedUp != null">
        speed_up = #{record.speedUp,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.worksName != null">
        works_name = #{record.worksName,jdbcType=VARCHAR},
      </if>
      <if test="record.worksDesc != null">
        works_desc = #{record.worksDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.worksCover != null">
        works_cover = #{record.worksCover,jdbcType=VARCHAR},
      </if>
      <if test="record.simpleItemName != null">
        simple_item_name = #{record.simpleItemName,jdbcType=VARCHAR},
      </if>
      <if test="record.worksTag != null">
        works_tag = #{record.worksTag,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.publishedTime != null">
        published_time = #{record.publishedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.itemFeatures != null">
        item_features = #{record.itemFeatures,jdbcType=VARCHAR},
      </if>
      <if test="record.itemTags != null">
        item_tags = #{record.itemTags,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.fontId != null">
        font_id = #{record.fontId,jdbcType=INTEGER},
      </if>
      <if test="record.keywords != null">
        keywords = #{record.keywords,jdbcType=VARCHAR},
      </if>
      <if test="record.isDescCompleted != null">
        is_desc_completed = #{record.isDescCompleted,jdbcType=BIT},
      </if>
      <if test="record.leftTopStickerId != null">
        left_top_sticker_id = #{record.leftTopStickerId,jdbcType=INTEGER},
      </if>
      <if test="record.rightTopStickerId != null">
        right_top_sticker_id = #{record.rightTopStickerId,jdbcType=INTEGER},
      </if>
      <if test="record.leftBottomStickerId != null">
        left_bottom_sticker_id = #{record.leftBottomStickerId,jdbcType=INTEGER},
      </if>
      <if test="record.rightBottomStickerId != null">
        right_bottom_sticker_id = #{record.rightBottomStickerId,jdbcType=INTEGER},
      </if>
      <if test="record.videoPathWithoutAss != null">
        video_path_without_ass = #{record.videoPathWithoutAss,jdbcType=VARCHAR},
      </if>
      <if test="record.composeStatus != null">
        compose_status = #{record.composeStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update works
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      item_id = #{record.itemId,jdbcType=BIGINT},
      seller_id = #{record.sellerId,jdbcType=BIGINT},
      duration = #{record.duration,jdbcType=INTEGER},
      special_effects_template_code = #{record.specialEffectsTemplateCode,jdbcType=VARCHAR},
      is_composed = #{record.isComposed,jdbcType=BIT},
      is_published = #{record.isPublished,jdbcType=BIT},
      video_path = #{record.videoPath,jdbcType=VARCHAR},
      music_id = #{record.musicId,jdbcType=INTEGER},
      filter_id = #{record.filterId,jdbcType=INTEGER},
      sticker_id = #{record.stickerId,jdbcType=INTEGER},
      speed_up = #{record.speedUp,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      works_name = #{record.worksName,jdbcType=VARCHAR},
      works_desc = #{record.worksDesc,jdbcType=VARCHAR},
      works_cover = #{record.worksCover,jdbcType=VARCHAR},
      simple_item_name = #{record.simpleItemName,jdbcType=VARCHAR},
      works_tag = #{record.worksTag,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=BIGINT},
      phone = #{record.phone,jdbcType=VARCHAR},
      published_time = #{record.publishedTime,jdbcType=TIMESTAMP},
      item_features = #{record.itemFeatures,jdbcType=VARCHAR},
      item_tags = #{record.itemTags,jdbcType=VARCHAR},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      font_id = #{record.fontId,jdbcType=INTEGER},
      keywords = #{record.keywords,jdbcType=VARCHAR},
      is_desc_completed = #{record.isDescCompleted,jdbcType=BIT},
      left_top_sticker_id = #{record.leftTopStickerId,jdbcType=INTEGER},
      right_top_sticker_id = #{record.rightTopStickerId,jdbcType=INTEGER},
      left_bottom_sticker_id = #{record.leftBottomStickerId,jdbcType=INTEGER},
      right_bottom_sticker_id = #{record.rightBottomStickerId,jdbcType=INTEGER},
      video_path_without_ass = #{record.videoPathWithoutAss,jdbcType=VARCHAR},
      compose_status = #{record.composeStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.Works">
    update works
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="specialEffectsTemplateCode != null">
        special_effects_template_code = #{specialEffectsTemplateCode,jdbcType=VARCHAR},
      </if>
      <if test="isComposed != null">
        is_composed = #{isComposed,jdbcType=BIT},
      </if>
      <if test="isPublished != null">
        is_published = #{isPublished,jdbcType=BIT},
      </if>
      <if test="videoPath != null">
        video_path = #{videoPath,jdbcType=VARCHAR},
      </if>
      <if test="musicId != null">
        music_id = #{musicId,jdbcType=INTEGER},
      </if>
      <if test="filterId != null">
        filter_id = #{filterId,jdbcType=INTEGER},
      </if>
      <if test="stickerId != null">
        sticker_id = #{stickerId,jdbcType=INTEGER},
      </if>
      <if test="speedUp != null">
        speed_up = #{speedUp,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="worksName != null">
        works_name = #{worksName,jdbcType=VARCHAR},
      </if>
      <if test="worksDesc != null">
        works_desc = #{worksDesc,jdbcType=VARCHAR},
      </if>
      <if test="worksCover != null">
        works_cover = #{worksCover,jdbcType=VARCHAR},
      </if>
      <if test="simpleItemName != null">
        simple_item_name = #{simpleItemName,jdbcType=VARCHAR},
      </if>
      <if test="worksTag != null">
        works_tag = #{worksTag,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="publishedTime != null">
        published_time = #{publishedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="itemFeatures != null">
        item_features = #{itemFeatures,jdbcType=VARCHAR},
      </if>
      <if test="itemTags != null">
        item_tags = #{itemTags,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="fontId != null">
        font_id = #{fontId,jdbcType=INTEGER},
      </if>
      <if test="keywords != null">
        keywords = #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="isDescCompleted != null">
        is_desc_completed = #{isDescCompleted,jdbcType=BIT},
      </if>
      <if test="leftTopStickerId != null">
        left_top_sticker_id = #{leftTopStickerId,jdbcType=INTEGER},
      </if>
      <if test="rightTopStickerId != null">
        right_top_sticker_id = #{rightTopStickerId,jdbcType=INTEGER},
      </if>
      <if test="leftBottomStickerId != null">
        left_bottom_sticker_id = #{leftBottomStickerId,jdbcType=INTEGER},
      </if>
      <if test="rightBottomStickerId != null">
        right_bottom_sticker_id = #{rightBottomStickerId,jdbcType=INTEGER},
      </if>
      <if test="videoPathWithoutAss != null">
        video_path_without_ass = #{videoPathWithoutAss,jdbcType=VARCHAR},
      </if>
      <if test="composeStatus != null">
        compose_status = #{composeStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.Works">
    update works
    set project_id = #{projectId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      seller_id = #{sellerId,jdbcType=BIGINT},
      duration = #{duration,jdbcType=INTEGER},
      special_effects_template_code = #{specialEffectsTemplateCode,jdbcType=VARCHAR},
      is_composed = #{isComposed,jdbcType=BIT},
      is_published = #{isPublished,jdbcType=BIT},
      video_path = #{videoPath,jdbcType=VARCHAR},
      music_id = #{musicId,jdbcType=INTEGER},
      filter_id = #{filterId,jdbcType=INTEGER},
      sticker_id = #{stickerId,jdbcType=INTEGER},
      speed_up = #{speedUp,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      works_name = #{worksName,jdbcType=VARCHAR},
      works_desc = #{worksDesc,jdbcType=VARCHAR},
      works_cover = #{worksCover,jdbcType=VARCHAR},
      simple_item_name = #{simpleItemName,jdbcType=VARCHAR},
      works_tag = #{worksTag,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=BIGINT},
      phone = #{phone,jdbcType=VARCHAR},
      published_time = #{publishedTime,jdbcType=TIMESTAMP},
      item_features = #{itemFeatures,jdbcType=VARCHAR},
      item_tags = #{itemTags,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=BIGINT},
      font_id = #{fontId,jdbcType=INTEGER},
      keywords = #{keywords,jdbcType=VARCHAR},
      is_desc_completed = #{isDescCompleted,jdbcType=BIT},
      left_top_sticker_id = #{leftTopStickerId,jdbcType=INTEGER},
      right_top_sticker_id = #{rightTopStickerId,jdbcType=INTEGER},
      left_bottom_sticker_id = #{leftBottomStickerId,jdbcType=INTEGER},
      right_bottom_sticker_id = #{rightBottomStickerId,jdbcType=INTEGER},
      video_path_without_ass = #{videoPathWithoutAss,jdbcType=VARCHAR},
      compose_status = #{composeStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.WorksExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from works
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>