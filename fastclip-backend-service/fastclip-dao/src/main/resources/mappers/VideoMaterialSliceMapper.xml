<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.VideoMaterialSliceMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.VideoMaterialSlice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="video_id" jdbcType="BIGINT" property="videoId" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="size" jdbcType="INTEGER" property="size" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="start_ts" jdbcType="INTEGER" property="startTs" />
    <result column="is_subtitles_done" jdbcType="BIT" property="isSubtitlesDone" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, video_id, duration, path, size, number, start_ts, is_subtitles_done, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSliceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_material_slice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from video_material_slice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from video_material_slice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSliceExample">
    delete from video_material_slice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSlice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_material_slice (video_id, duration, path, 
      size, number, start_ts, 
      is_subtitles_done, create_time, update_time
      )
    values (#{videoId,jdbcType=BIGINT}, #{duration,jdbcType=INTEGER}, #{path,jdbcType=VARCHAR}, 
      #{size,jdbcType=INTEGER}, #{number,jdbcType=INTEGER}, #{startTs,jdbcType=INTEGER}, 
      #{isSubtitlesDone,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSlice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_material_slice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="videoId != null">
        video_id,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="size != null">
        size,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="startTs != null">
        start_ts,
      </if>
      <if test="isSubtitlesDone != null">
        is_subtitles_done,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="videoId != null">
        #{videoId,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="startTs != null">
        #{startTs,jdbcType=INTEGER},
      </if>
      <if test="isSubtitlesDone != null">
        #{isSubtitlesDone,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSliceExample" resultType="java.lang.Long">
    select count(*) from video_material_slice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update video_material_slice
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.videoId != null">
        video_id = #{record.videoId,jdbcType=BIGINT},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.path != null">
        path = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.size != null">
        size = #{record.size,jdbcType=INTEGER},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=INTEGER},
      </if>
      <if test="record.startTs != null">
        start_ts = #{record.startTs,jdbcType=INTEGER},
      </if>
      <if test="record.isSubtitlesDone != null">
        is_subtitles_done = #{record.isSubtitlesDone,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update video_material_slice
    set id = #{record.id,jdbcType=BIGINT},
      video_id = #{record.videoId,jdbcType=BIGINT},
      duration = #{record.duration,jdbcType=INTEGER},
      path = #{record.path,jdbcType=VARCHAR},
      size = #{record.size,jdbcType=INTEGER},
      number = #{record.number,jdbcType=INTEGER},
      start_ts = #{record.startTs,jdbcType=INTEGER},
      is_subtitles_done = #{record.isSubtitlesDone,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSlice">
    update video_material_slice
    <set>
      <if test="videoId != null">
        video_id = #{videoId,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        size = #{size,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=INTEGER},
      </if>
      <if test="startTs != null">
        start_ts = #{startTs,jdbcType=INTEGER},
      </if>
      <if test="isSubtitlesDone != null">
        is_subtitles_done = #{isSubtitlesDone,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSlice">
    update video_material_slice
    set video_id = #{videoId,jdbcType=BIGINT},
      duration = #{duration,jdbcType=INTEGER},
      path = #{path,jdbcType=VARCHAR},
      size = #{size,jdbcType=INTEGER},
      number = #{number,jdbcType=INTEGER},
      start_ts = #{startTs,jdbcType=INTEGER},
      is_subtitles_done = #{isSubtitlesDone,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialSliceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_material_slice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>