<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.FilterEffectMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.FilterEffect">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="saturate" jdbcType="INTEGER" property="saturate" />
    <result column="lightness" jdbcType="INTEGER" property="lightness" />
    <result column="contrast" jdbcType="INTEGER" property="contrast" />
    <result column="shadow" jdbcType="INTEGER" property="shadow" />
    <result column="highLight" jdbcType="INTEGER" property="highlight" />
    <result column="temperature" jdbcType="INTEGER" property="temperature" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, saturate, lightness, contrast, shadow, highLight, temperature, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.FilterEffectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from filter_effect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from filter_effect
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from filter_effect
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.FilterEffectExample">
    delete from filter_effect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.FilterEffect">
    insert into filter_effect (id, name, saturate, 
      lightness, contrast, shadow, 
      highLight, temperature, create_time, 
      update_time)
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{saturate,jdbcType=INTEGER}, 
      #{lightness,jdbcType=INTEGER}, #{contrast,jdbcType=INTEGER}, #{shadow,jdbcType=INTEGER}, 
      #{highlight,jdbcType=INTEGER}, #{temperature,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.FilterEffect">
    insert into filter_effect
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="saturate != null">
        saturate,
      </if>
      <if test="lightness != null">
        lightness,
      </if>
      <if test="contrast != null">
        contrast,
      </if>
      <if test="shadow != null">
        shadow,
      </if>
      <if test="highlight != null">
        highLight,
      </if>
      <if test="temperature != null">
        temperature,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="saturate != null">
        #{saturate,jdbcType=INTEGER},
      </if>
      <if test="lightness != null">
        #{lightness,jdbcType=INTEGER},
      </if>
      <if test="contrast != null">
        #{contrast,jdbcType=INTEGER},
      </if>
      <if test="shadow != null">
        #{shadow,jdbcType=INTEGER},
      </if>
      <if test="highlight != null">
        #{highlight,jdbcType=INTEGER},
      </if>
      <if test="temperature != null">
        #{temperature,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.FilterEffectExample" resultType="java.lang.Long">
    select count(*) from filter_effect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update filter_effect
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.saturate != null">
        saturate = #{record.saturate,jdbcType=INTEGER},
      </if>
      <if test="record.lightness != null">
        lightness = #{record.lightness,jdbcType=INTEGER},
      </if>
      <if test="record.contrast != null">
        contrast = #{record.contrast,jdbcType=INTEGER},
      </if>
      <if test="record.shadow != null">
        shadow = #{record.shadow,jdbcType=INTEGER},
      </if>
      <if test="record.highlight != null">
        highLight = #{record.highlight,jdbcType=INTEGER},
      </if>
      <if test="record.temperature != null">
        temperature = #{record.temperature,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update filter_effect
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      saturate = #{record.saturate,jdbcType=INTEGER},
      lightness = #{record.lightness,jdbcType=INTEGER},
      contrast = #{record.contrast,jdbcType=INTEGER},
      shadow = #{record.shadow,jdbcType=INTEGER},
      highLight = #{record.highlight,jdbcType=INTEGER},
      temperature = #{record.temperature,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.FilterEffect">
    update filter_effect
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="saturate != null">
        saturate = #{saturate,jdbcType=INTEGER},
      </if>
      <if test="lightness != null">
        lightness = #{lightness,jdbcType=INTEGER},
      </if>
      <if test="contrast != null">
        contrast = #{contrast,jdbcType=INTEGER},
      </if>
      <if test="shadow != null">
        shadow = #{shadow,jdbcType=INTEGER},
      </if>
      <if test="highlight != null">
        highLight = #{highlight,jdbcType=INTEGER},
      </if>
      <if test="temperature != null">
        temperature = #{temperature,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.FilterEffect">
    update filter_effect
    set name = #{name,jdbcType=VARCHAR},
      saturate = #{saturate,jdbcType=INTEGER},
      lightness = #{lightness,jdbcType=INTEGER},
      contrast = #{contrast,jdbcType=INTEGER},
      shadow = #{shadow,jdbcType=INTEGER},
      highLight = #{highlight,jdbcType=INTEGER},
      temperature = #{temperature,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.FilterEffectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from filter_effect
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>