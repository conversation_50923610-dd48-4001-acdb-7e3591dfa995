<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.SubtitlesCutMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.SubtitlesCut">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="video_id" jdbcType="BIGINT" property="videoId" />
    <result column="subtitles_id" jdbcType="BIGINT" property="subtitlesId" />
    <result column="cut_start_content" jdbcType="VARCHAR" property="cutStartContent" />
    <result column="cut_end_content" jdbcType="VARCHAR" property="cutEndContent" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="cut_start_ts" jdbcType="INTEGER" property="cutStartTs" />
    <result column="cut_end_ts" jdbcType="INTEGER" property="cutEndTs" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="length" jdbcType="INTEGER" property="length" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, video_id, subtitles_id, cut_start_content, cut_end_content, content, 
    cut_start_ts, cut_end_ts, duration, length, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCutExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from subtitles_cut
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from subtitles_cut
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from subtitles_cut
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCutExample">
    delete from subtitles_cut
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCut">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into subtitles_cut (project_id, video_id, subtitles_id, 
      cut_start_content, cut_end_content, content, 
      cut_start_ts, cut_end_ts, duration, 
      length, create_time, update_time
      )
    values (#{projectId,jdbcType=BIGINT}, #{videoId,jdbcType=BIGINT}, #{subtitlesId,jdbcType=BIGINT}, 
      #{cutStartContent,jdbcType=VARCHAR}, #{cutEndContent,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{cutStartTs,jdbcType=INTEGER}, #{cutEndTs,jdbcType=INTEGER}, #{duration,jdbcType=INTEGER}, 
      #{length,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCut">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into subtitles_cut
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="subtitlesId != null">
        subtitles_id,
      </if>
      <if test="cutStartContent != null">
        cut_start_content,
      </if>
      <if test="cutEndContent != null">
        cut_end_content,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="cutStartTs != null">
        cut_start_ts,
      </if>
      <if test="cutEndTs != null">
        cut_end_ts,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="length != null">
        length,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=BIGINT},
      </if>
      <if test="subtitlesId != null">
        #{subtitlesId,jdbcType=BIGINT},
      </if>
      <if test="cutStartContent != null">
        #{cutStartContent,jdbcType=VARCHAR},
      </if>
      <if test="cutEndContent != null">
        #{cutEndContent,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="cutStartTs != null">
        #{cutStartTs,jdbcType=INTEGER},
      </if>
      <if test="cutEndTs != null">
        #{cutEndTs,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="length != null">
        #{length,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCutExample" resultType="java.lang.Long">
    select count(*) from subtitles_cut
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update subtitles_cut
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.videoId != null">
        video_id = #{record.videoId,jdbcType=BIGINT},
      </if>
      <if test="record.subtitlesId != null">
        subtitles_id = #{record.subtitlesId,jdbcType=BIGINT},
      </if>
      <if test="record.cutStartContent != null">
        cut_start_content = #{record.cutStartContent,jdbcType=VARCHAR},
      </if>
      <if test="record.cutEndContent != null">
        cut_end_content = #{record.cutEndContent,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.cutStartTs != null">
        cut_start_ts = #{record.cutStartTs,jdbcType=INTEGER},
      </if>
      <if test="record.cutEndTs != null">
        cut_end_ts = #{record.cutEndTs,jdbcType=INTEGER},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.length != null">
        length = #{record.length,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update subtitles_cut
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      video_id = #{record.videoId,jdbcType=BIGINT},
      subtitles_id = #{record.subtitlesId,jdbcType=BIGINT},
      cut_start_content = #{record.cutStartContent,jdbcType=VARCHAR},
      cut_end_content = #{record.cutEndContent,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      cut_start_ts = #{record.cutStartTs,jdbcType=INTEGER},
      cut_end_ts = #{record.cutEndTs,jdbcType=INTEGER},
      duration = #{record.duration,jdbcType=INTEGER},
      length = #{record.length,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCut">
    update subtitles_cut
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="videoId != null">
        video_id = #{videoId,jdbcType=BIGINT},
      </if>
      <if test="subtitlesId != null">
        subtitles_id = #{subtitlesId,jdbcType=BIGINT},
      </if>
      <if test="cutStartContent != null">
        cut_start_content = #{cutStartContent,jdbcType=VARCHAR},
      </if>
      <if test="cutEndContent != null">
        cut_end_content = #{cutEndContent,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="cutStartTs != null">
        cut_start_ts = #{cutStartTs,jdbcType=INTEGER},
      </if>
      <if test="cutEndTs != null">
        cut_end_ts = #{cutEndTs,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="length != null">
        length = #{length,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCut">
    update subtitles_cut
    set project_id = #{projectId,jdbcType=BIGINT},
      video_id = #{videoId,jdbcType=BIGINT},
      subtitles_id = #{subtitlesId,jdbcType=BIGINT},
      cut_start_content = #{cutStartContent,jdbcType=VARCHAR},
      cut_end_content = #{cutEndContent,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      cut_start_ts = #{cutStartTs,jdbcType=INTEGER},
      cut_end_ts = #{cutEndTs,jdbcType=INTEGER},
      duration = #{duration,jdbcType=INTEGER},
      length = #{length,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.SubtitlesCutExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from subtitles_cut
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>