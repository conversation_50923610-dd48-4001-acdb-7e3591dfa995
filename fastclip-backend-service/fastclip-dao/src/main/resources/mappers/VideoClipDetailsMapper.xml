<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.VideoClipDetailsMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.VideoClipDetails">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="video_clip_id" jdbcType="BIGINT" property="videoClipId" />
    <result column="subtitles_cut_id" jdbcType="BIGINT" property="subtitlesCutId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, video_clip_id, subtitles_cut_id, sort, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetailsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_clip_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from video_clip_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from video_clip_details
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetailsExample">
    delete from video_clip_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetails">
    insert into video_clip_details (id, project_id, video_clip_id, 
      subtitles_cut_id, sort, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{videoClipId,jdbcType=BIGINT}, 
      #{subtitlesCutId,jdbcType=BIGINT}, #{sort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetails">
    insert into video_clip_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="videoClipId != null">
        video_clip_id,
      </if>
      <if test="subtitlesCutId != null">
        subtitles_cut_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="videoClipId != null">
        #{videoClipId,jdbcType=BIGINT},
      </if>
      <if test="subtitlesCutId != null">
        #{subtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetailsExample" resultType="java.lang.Long">
    select count(*) from video_clip_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update video_clip_details
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.videoClipId != null">
        video_clip_id = #{record.videoClipId,jdbcType=BIGINT},
      </if>
      <if test="record.subtitlesCutId != null">
        subtitles_cut_id = #{record.subtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update video_clip_details
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      video_clip_id = #{record.videoClipId,jdbcType=BIGINT},
      subtitles_cut_id = #{record.subtitlesCutId,jdbcType=BIGINT},
      sort = #{record.sort,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetails">
    update video_clip_details
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="videoClipId != null">
        video_clip_id = #{videoClipId,jdbcType=BIGINT},
      </if>
      <if test="subtitlesCutId != null">
        subtitles_cut_id = #{subtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetails">
    update video_clip_details
    set project_id = #{projectId,jdbcType=BIGINT},
      video_clip_id = #{videoClipId,jdbcType=BIGINT},
      subtitles_cut_id = #{subtitlesCutId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.VideoClipDetailsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_clip_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>