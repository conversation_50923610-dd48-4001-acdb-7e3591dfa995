<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.OrderMapperExt">
  <insert id="batchInsertOrUpdateOrders">
    INSERT INTO `orders` (
    `order_id`,
    `product_id`,
    `product_name`,
    `product_img`,
    `author_account`,
    `shop_name`,
    `total_pay_amount`,
    `commission_rate`,
    `flow_point`,
    `app`,
    `update_time`,
    `pay_success_time`,
    `settle_time`,
    `pay_goods_amount`,
    `settled_goods_amount`,
    `real_commission`,
    `estimated_commission`,
    `item_num`,
    `shop_id`,
    `refund_time`,
    `estimated_total_commission`,
    `estimated_tech_service_fee`,
    `pick_source_client_key`,
    `pick_extra`,
    `author_short_id`
    ) VALUES
    <foreach collection="orders" item="order" separator=",">
      (
      #{order.orderId},
      #{order.productId},
      #{order.productName},
      #{order.productImg},
      #{order.authorAccount},
      #{order.shopName},
      #{order.totalPayAmount},
      #{order.commissionRate},
      #{order.flowPoint},
      #{order.app},
      #{order.updateTime},
      #{order.paySuccessTime},
      #{order.settleTime},
      #{order.payGoodsAmount},
      #{order.settledGoodsAmount},
      #{order.realCommission},
      #{order.estimatedCommission},
      #{order.itemNum},
      #{order.shopId},
      #{order.refundTime},
      #{order.estimatedTotalCommission},
      #{order.estimatedTechServiceFee},
      #{order.pickSourceClientKey},
      #{order.pickExtra},
      #{order.authorShortId}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    update_time = VALUES(update_time),
    settle_time = VALUES(settle_time),
    refund_time = VALUES(refund_time),
    flow_point = VALUES(flow_point)
  </insert>

  <select id="getOrdersCount" parameterType="com.fastclip.common.model.request.GetOrderListReq" resultType="com.fastclip.common.model.dataobject.OrderDO">
    select sum(total_pay_amount) as totalPayAmount, count(*) as orderCount
    from orders o left join douyin_account d on o.author_short_id=d.code
    where 1=1
    <if test="req.startDate != null">
      and  pay_success_time >= #{req.startDate}
    </if>
    <if test="req.endDate != null">
      and  pay_success_time &lt;= #{req.endDate}
    </if>
    <if test="req.flowPoint != null">
      and  flow_point = #{req.flowPoint}
    </if>
    <if test="req.accountType != null">
      and  d.type = #{req.accountType}
    </if>
    <if test="req.douyinAccountCode != null">
      and  d.code = #{req.douyinAccountCode}
    </if>
    <if test="req.orderId != null">
      and  o.order_id = #{req.orderId}
    </if>
    <if test="req.teamId != null">
      and  d.team_id = #{req.teamId} and d.type=2
    </if>
    <if test="req.selfCutterId != null">
      and  d.self_id = #{req.selfCutterId} and d.type=3
    </if>
  </select>

  <select id="getOrdersList" parameterType="com.fastclip.common.model.request.GetOrderListReq" resultType="com.fastclip.dao.model.dataobject.Orders">
    select o.id, o.order_id as orderId, o.product_id as productId, o.product_name as productName, o.product_img as productImg,
           o.author_account as author_account, o.shop_name shopName, o.total_pay_amount as totalPayAmount,
           o.commission_rate as commissionRate, flow_point as flowPoint, app, o.update_time as updateTime,
           pay_success_time as paySuccessTime, settle_time as settleTime, pay_goods_amount as payGoodsAmount,
           settled_goods_amount as settledGoodsAmount, real_commission as realCommission,
           estimated_commission as estimatedCommission, item_num as itemNum, shop_id as shopId, refund_time as refundTime,
           estimated_total_commission as estimatedTotalCommission, estimated_tech_service_fee as estimatedTechServiceFee,
           pick_source_client_key as pickSourceClientKey, pick_extra as pickExtra, author_short_id as authorShortId
    from orders o left join douyin_account d on o.author_short_id=d.code
    where 1=1
    <if test="req.startDate != null">
      and  pay_success_time >= #{req.startDate}
    </if>
    <if test="req.endDate != null">
      and  pay_success_time &lt;= #{req.endDate}
    </if>
    <if test="req.flowPoint != null">
      and  flow_point = #{req.flowPoint}
    </if>
    <if test="req.accountType != null">
      and  d.type = #{req.accountType}
    </if>
    <if test="req.douyinAccountCode != null">
      and  d.code = #{req.douyinAccountCode}
    </if>
    <if test="req.orderId != null">
      and  o.order_id = #{req.orderId}
    </if>
    <if test="req.teamId != null">
      and  d.team_id = #{req.teamId} and d.type=2
    </if>
    <if test="req.selfCutterId != null">
      and  d.self_id = #{req.selfCutterId} and d.type=3
    </if>
    order by pay_success_time desc
    limit #{req.offset},#{req.pageSize}
  </select>

</mapper>