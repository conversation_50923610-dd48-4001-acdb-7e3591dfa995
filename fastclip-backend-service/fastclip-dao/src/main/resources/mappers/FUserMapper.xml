<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.FUserMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.FUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="is_admin" jdbcType="BIT" property="isAdmin" />
    <result column="passwd" jdbcType="VARCHAR" property="passwd" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_account" jdbcType="VARCHAR" property="userAccount" />
    <result column="has_upload_previlege" jdbcType="BIT" property="hasUploadPrevilege" />
    <result column="is_self_cutter" jdbcType="BIT" property="isSelfCutter" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_name, is_admin, passwd, create_time, update_time, user_account, has_upload_previlege, 
    is_self_cutter
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.FUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from f_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from f_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from f_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.FUserExample">
    delete from f_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.FUser">
    insert into f_user (id, user_name, is_admin, 
      passwd, create_time, update_time, 
      user_account, has_upload_previlege, is_self_cutter
      )
    values (#{id,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{isAdmin,jdbcType=BIT}, 
      #{passwd,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{userAccount,jdbcType=VARCHAR}, #{hasUploadPrevilege,jdbcType=BIT}, #{isSelfCutter,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.FUser">
    insert into f_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="passwd != null">
        passwd,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="userAccount != null">
        user_account,
      </if>
      <if test="hasUploadPrevilege != null">
        has_upload_previlege,
      </if>
      <if test="isSelfCutter != null">
        is_self_cutter,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=BIT},
      </if>
      <if test="passwd != null">
        #{passwd,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userAccount != null">
        #{userAccount,jdbcType=VARCHAR},
      </if>
      <if test="hasUploadPrevilege != null">
        #{hasUploadPrevilege,jdbcType=BIT},
      </if>
      <if test="isSelfCutter != null">
        #{isSelfCutter,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.FUserExample" resultType="java.lang.Long">
    select count(*) from f_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update f_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.isAdmin != null">
        is_admin = #{record.isAdmin,jdbcType=BIT},
      </if>
      <if test="record.passwd != null">
        passwd = #{record.passwd,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userAccount != null">
        user_account = #{record.userAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.hasUploadPrevilege != null">
        has_upload_previlege = #{record.hasUploadPrevilege,jdbcType=BIT},
      </if>
      <if test="record.isSelfCutter != null">
        is_self_cutter = #{record.isSelfCutter,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update f_user
    set id = #{record.id,jdbcType=BIGINT},
      user_name = #{record.userName,jdbcType=VARCHAR},
      is_admin = #{record.isAdmin,jdbcType=BIT},
      passwd = #{record.passwd,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      user_account = #{record.userAccount,jdbcType=VARCHAR},
      has_upload_previlege = #{record.hasUploadPrevilege,jdbcType=BIT},
      is_self_cutter = #{record.isSelfCutter,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.FUser">
    update f_user
    <set>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=BIT},
      </if>
      <if test="passwd != null">
        passwd = #{passwd,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userAccount != null">
        user_account = #{userAccount,jdbcType=VARCHAR},
      </if>
      <if test="hasUploadPrevilege != null">
        has_upload_previlege = #{hasUploadPrevilege,jdbcType=BIT},
      </if>
      <if test="isSelfCutter != null">
        is_self_cutter = #{isSelfCutter,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.FUser">
    update f_user
    set user_name = #{userName,jdbcType=VARCHAR},
      is_admin = #{isAdmin,jdbcType=BIT},
      passwd = #{passwd,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      user_account = #{userAccount,jdbcType=VARCHAR},
      has_upload_previlege = #{hasUploadPrevilege,jdbcType=BIT},
      is_self_cutter = #{isSelfCutter,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.FUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from f_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>