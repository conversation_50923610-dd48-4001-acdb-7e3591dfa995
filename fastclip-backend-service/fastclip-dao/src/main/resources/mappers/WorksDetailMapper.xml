<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.WorksDetailMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.WorksDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="works_id" jdbcType="BIGINT" property="worksId" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="video_clip_id" jdbcType="BIGINT" property="videoClipId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="tran_code" jdbcType="VARCHAR" property="tranCode" />
    <result column="tag_code" jdbcType="VARCHAR" property="tagCode" />
    <result column="cut_percent" jdbcType="INTEGER" property="cutPercent" />
    <result column="trans_id" jdbcType="INTEGER" property="transId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="flip_flag" jdbcType="BIT" property="flipFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, works_id, seller_id, video_clip_id, sort, duration, tran_code, tag_code, 
    cut_percent, trans_id, create_time, update_time, flip_flag
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.WorksDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from works_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from works_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from works_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.WorksDetailExample">
    delete from works_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.WorksDetail">
    insert into works_detail (id, project_id, works_id, 
      seller_id, video_clip_id, sort, 
      duration, tran_code, tag_code, 
      cut_percent, trans_id, create_time, 
      update_time, flip_flag)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{worksId,jdbcType=BIGINT}, 
      #{sellerId,jdbcType=BIGINT}, #{videoClipId,jdbcType=BIGINT}, #{sort,jdbcType=INTEGER}, 
      #{duration,jdbcType=INTEGER}, #{tranCode,jdbcType=VARCHAR}, #{tagCode,jdbcType=VARCHAR}, 
      #{cutPercent,jdbcType=INTEGER}, #{transId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{flipFlag,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.WorksDetail">
    insert into works_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="worksId != null">
        works_id,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="videoClipId != null">
        video_clip_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="tranCode != null">
        tran_code,
      </if>
      <if test="tagCode != null">
        tag_code,
      </if>
      <if test="cutPercent != null">
        cut_percent,
      </if>
      <if test="transId != null">
        trans_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="flipFlag != null">
        flip_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="worksId != null">
        #{worksId,jdbcType=BIGINT},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="videoClipId != null">
        #{videoClipId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="tranCode != null">
        #{tranCode,jdbcType=VARCHAR},
      </if>
      <if test="tagCode != null">
        #{tagCode,jdbcType=VARCHAR},
      </if>
      <if test="cutPercent != null">
        #{cutPercent,jdbcType=INTEGER},
      </if>
      <if test="transId != null">
        #{transId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flipFlag != null">
        #{flipFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.WorksDetailExample" resultType="java.lang.Long">
    select count(*) from works_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update works_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.worksId != null">
        works_id = #{record.worksId,jdbcType=BIGINT},
      </if>
      <if test="record.sellerId != null">
        seller_id = #{record.sellerId,jdbcType=BIGINT},
      </if>
      <if test="record.videoClipId != null">
        video_clip_id = #{record.videoClipId,jdbcType=BIGINT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.tranCode != null">
        tran_code = #{record.tranCode,jdbcType=VARCHAR},
      </if>
      <if test="record.tagCode != null">
        tag_code = #{record.tagCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cutPercent != null">
        cut_percent = #{record.cutPercent,jdbcType=INTEGER},
      </if>
      <if test="record.transId != null">
        trans_id = #{record.transId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.flipFlag != null">
        flip_flag = #{record.flipFlag,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update works_detail
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      works_id = #{record.worksId,jdbcType=BIGINT},
      seller_id = #{record.sellerId,jdbcType=BIGINT},
      video_clip_id = #{record.videoClipId,jdbcType=BIGINT},
      sort = #{record.sort,jdbcType=INTEGER},
      duration = #{record.duration,jdbcType=INTEGER},
      tran_code = #{record.tranCode,jdbcType=VARCHAR},
      tag_code = #{record.tagCode,jdbcType=VARCHAR},
      cut_percent = #{record.cutPercent,jdbcType=INTEGER},
      trans_id = #{record.transId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      flip_flag = #{record.flipFlag,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.WorksDetail">
    update works_detail
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="worksId != null">
        works_id = #{worksId,jdbcType=BIGINT},
      </if>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="videoClipId != null">
        video_clip_id = #{videoClipId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="tranCode != null">
        tran_code = #{tranCode,jdbcType=VARCHAR},
      </if>
      <if test="tagCode != null">
        tag_code = #{tagCode,jdbcType=VARCHAR},
      </if>
      <if test="cutPercent != null">
        cut_percent = #{cutPercent,jdbcType=INTEGER},
      </if>
      <if test="transId != null">
        trans_id = #{transId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flipFlag != null">
        flip_flag = #{flipFlag,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.WorksDetail">
    update works_detail
    set project_id = #{projectId,jdbcType=BIGINT},
      works_id = #{worksId,jdbcType=BIGINT},
      seller_id = #{sellerId,jdbcType=BIGINT},
      video_clip_id = #{videoClipId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=INTEGER},
      duration = #{duration,jdbcType=INTEGER},
      tran_code = #{tranCode,jdbcType=VARCHAR},
      tag_code = #{tagCode,jdbcType=VARCHAR},
      cut_percent = #{cutPercent,jdbcType=INTEGER},
      trans_id = #{transId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      flip_flag = #{flipFlag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.WorksDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from works_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>