<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.ProjectMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.Project">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="des" jdbcType="VARCHAR" property="des" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="cover" jdbcType="VARCHAR" property="cover" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_name, item_id, seller_id, des, status, create_time, update_time, cover, 
    item_type, creator_id
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.ProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.ProjectExample">
    delete from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.Project">
    insert into project (id, project_name, item_id, 
      seller_id, des, status, 
      create_time, update_time, cover, 
      item_type, creator_id)
    values (#{id,jdbcType=BIGINT}, #{projectName,jdbcType=VARCHAR}, #{itemId,jdbcType=BIGINT}, 
      #{sellerId,jdbcType=BIGINT}, #{des,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{cover,jdbcType=VARCHAR}, 
      #{itemType,jdbcType=INTEGER}, #{creatorId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.Project">
    insert into project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="des != null">
        des,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="cover != null">
        cover,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="des != null">
        #{des,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cover != null">
        #{cover,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.ProjectExample" resultType="java.lang.Long">
    select count(*) from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=BIGINT},
      </if>
      <if test="record.sellerId != null">
        seller_id = #{record.sellerId,jdbcType=BIGINT},
      </if>
      <if test="record.des != null">
        des = #{record.des,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cover != null">
        cover = #{record.cover,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project
    set id = #{record.id,jdbcType=BIGINT},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      item_id = #{record.itemId,jdbcType=BIGINT},
      seller_id = #{record.sellerId,jdbcType=BIGINT},
      des = #{record.des,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      cover = #{record.cover,jdbcType=VARCHAR},
      item_type = #{record.itemType,jdbcType=INTEGER},
      creator_id = #{record.creatorId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.Project">
    update project
    <set>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="des != null">
        des = #{des,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cover != null">
        cover = #{cover,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.Project">
    update project
    set project_name = #{projectName,jdbcType=VARCHAR},
      item_id = #{itemId,jdbcType=BIGINT},
      seller_id = #{sellerId,jdbcType=BIGINT},
      des = #{des,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      cover = #{cover,jdbcType=VARCHAR},
      item_type = #{itemType,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.ProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>