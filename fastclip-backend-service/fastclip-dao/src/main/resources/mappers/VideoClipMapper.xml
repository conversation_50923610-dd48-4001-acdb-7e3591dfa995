<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.VideoClipMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.VideoClip">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="start_subtitles_cut_id" jdbcType="BIGINT" property="startSubtitlesCutId" />
    <result column="end_subtitles_cut_id" jdbcType="BIGINT" property="endSubtitlesCutId" />
    <result column="start_subtitles_id" jdbcType="BIGINT" property="startSubtitlesId" />
    <result column="end_subtitles_id" jdbcType="BIGINT" property="endSubtitlesId" />
    <result column="subtitles" jdbcType="VARCHAR" property="subtitles" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="subtitles_cut_count" jdbcType="INTEGER" property="subtitlesCutCount" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, start_subtitles_cut_id, end_subtitles_cut_id, start_subtitles_id, 
    end_subtitles_id, subtitles, duration, subtitles_cut_count, sort, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.VideoClipExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_clip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from video_clip
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from video_clip
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.VideoClipExample">
    delete from video_clip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.VideoClip">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_clip (project_id, start_subtitles_cut_id, end_subtitles_cut_id, 
      start_subtitles_id, end_subtitles_id, subtitles, 
      duration, subtitles_cut_count, sort, 
      create_time, update_time)
    values (#{projectId,jdbcType=BIGINT}, #{startSubtitlesCutId,jdbcType=BIGINT}, #{endSubtitlesCutId,jdbcType=BIGINT}, 
      #{startSubtitlesId,jdbcType=BIGINT}, #{endSubtitlesId,jdbcType=BIGINT}, #{subtitles,jdbcType=VARCHAR}, 
      #{duration,jdbcType=INTEGER}, #{subtitlesCutCount,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.VideoClip">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_clip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="startSubtitlesCutId != null">
        start_subtitles_cut_id,
      </if>
      <if test="endSubtitlesCutId != null">
        end_subtitles_cut_id,
      </if>
      <if test="startSubtitlesId != null">
        start_subtitles_id,
      </if>
      <if test="endSubtitlesId != null">
        end_subtitles_id,
      </if>
      <if test="subtitles != null">
        subtitles,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="subtitlesCutCount != null">
        subtitles_cut_count,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="startSubtitlesCutId != null">
        #{startSubtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="endSubtitlesCutId != null">
        #{endSubtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="startSubtitlesId != null">
        #{startSubtitlesId,jdbcType=BIGINT},
      </if>
      <if test="endSubtitlesId != null">
        #{endSubtitlesId,jdbcType=BIGINT},
      </if>
      <if test="subtitles != null">
        #{subtitles,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="subtitlesCutCount != null">
        #{subtitlesCutCount,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.VideoClipExample" resultType="java.lang.Long">
    select count(*) from video_clip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update video_clip
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.startSubtitlesCutId != null">
        start_subtitles_cut_id = #{record.startSubtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="record.endSubtitlesCutId != null">
        end_subtitles_cut_id = #{record.endSubtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="record.startSubtitlesId != null">
        start_subtitles_id = #{record.startSubtitlesId,jdbcType=BIGINT},
      </if>
      <if test="record.endSubtitlesId != null">
        end_subtitles_id = #{record.endSubtitlesId,jdbcType=BIGINT},
      </if>
      <if test="record.subtitles != null">
        subtitles = #{record.subtitles,jdbcType=VARCHAR},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.subtitlesCutCount != null">
        subtitles_cut_count = #{record.subtitlesCutCount,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update video_clip
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      start_subtitles_cut_id = #{record.startSubtitlesCutId,jdbcType=BIGINT},
      end_subtitles_cut_id = #{record.endSubtitlesCutId,jdbcType=BIGINT},
      start_subtitles_id = #{record.startSubtitlesId,jdbcType=BIGINT},
      end_subtitles_id = #{record.endSubtitlesId,jdbcType=BIGINT},
      subtitles = #{record.subtitles,jdbcType=VARCHAR},
      duration = #{record.duration,jdbcType=INTEGER},
      subtitles_cut_count = #{record.subtitlesCutCount,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.VideoClip">
    update video_clip
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="startSubtitlesCutId != null">
        start_subtitles_cut_id = #{startSubtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="endSubtitlesCutId != null">
        end_subtitles_cut_id = #{endSubtitlesCutId,jdbcType=BIGINT},
      </if>
      <if test="startSubtitlesId != null">
        start_subtitles_id = #{startSubtitlesId,jdbcType=BIGINT},
      </if>
      <if test="endSubtitlesId != null">
        end_subtitles_id = #{endSubtitlesId,jdbcType=BIGINT},
      </if>
      <if test="subtitles != null">
        subtitles = #{subtitles,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="subtitlesCutCount != null">
        subtitles_cut_count = #{subtitlesCutCount,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.VideoClip">
    update video_clip
    set project_id = #{projectId,jdbcType=BIGINT},
      start_subtitles_cut_id = #{startSubtitlesCutId,jdbcType=BIGINT},
      end_subtitles_cut_id = #{endSubtitlesCutId,jdbcType=BIGINT},
      start_subtitles_id = #{startSubtitlesId,jdbcType=BIGINT},
      end_subtitles_id = #{endSubtitlesId,jdbcType=BIGINT},
      subtitles = #{subtitles,jdbcType=VARCHAR},
      duration = #{duration,jdbcType=INTEGER},
      subtitles_cut_count = #{subtitlesCutCount,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.VideoClipExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_clip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>