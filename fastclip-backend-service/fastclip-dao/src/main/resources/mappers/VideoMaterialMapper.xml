<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.VideoMaterialMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.VideoMaterial">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="size" jdbcType="INTEGER" property="size" />
    <result column="is_subtitles_done" jdbcType="BIT" property="isSubtitlesDone" />
    <result column="subtitles_bp_ts" jdbcType="INTEGER" property="subtitlesBpTs" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="video_name" jdbcType="VARCHAR" property="videoName" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="video_type" jdbcType="INTEGER" property="videoType" />
    <result column="live_room_id" jdbcType="VARCHAR" property="liveRoomId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="latest_slice_merged_id" jdbcType="INTEGER" property="latestSliceMergedId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="combine_status" jdbcType="INTEGER" property="combineStatus" />
    <result column="start_scene" jdbcType="INTEGER" property="startScene" />
    <result column="start_scene_flag" jdbcType="BIT" property="startSceneFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, seller_id, duration, path, size, is_subtitles_done, subtitles_bp_ts, create_time, 
    update_time, video_name, start_date, start_time, video_type, live_room_id, status, 
    latest_slice_merged_id, sort, combine_status, start_scene, start_scene_flag
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from video_material
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from video_material
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialExample">
    delete from video_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.VideoMaterial">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_material (seller_id, duration, path, 
      size, is_subtitles_done, subtitles_bp_ts, 
      create_time, update_time, video_name, 
      start_date, start_time, video_type, 
      live_room_id, status, latest_slice_merged_id, 
      sort, combine_status, start_scene, 
      start_scene_flag)
    values (#{sellerId,jdbcType=BIGINT}, #{duration,jdbcType=INTEGER}, #{path,jdbcType=VARCHAR}, 
      #{size,jdbcType=INTEGER}, #{isSubtitlesDone,jdbcType=BIT}, #{subtitlesBpTs,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{videoName,jdbcType=VARCHAR}, 
      #{startDate,jdbcType=TIMESTAMP}, #{startTime,jdbcType=INTEGER}, #{videoType,jdbcType=INTEGER}, 
      #{liveRoomId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{latestSliceMergedId,jdbcType=INTEGER}, 
      #{sort,jdbcType=INTEGER}, #{combineStatus,jdbcType=INTEGER}, #{startScene,jdbcType=INTEGER}, 
      #{startSceneFlag,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.VideoMaterial">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into video_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="size != null">
        size,
      </if>
      <if test="isSubtitlesDone != null">
        is_subtitles_done,
      </if>
      <if test="subtitlesBpTs != null">
        subtitles_bp_ts,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="videoName != null">
        video_name,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="videoType != null">
        video_type,
      </if>
      <if test="liveRoomId != null">
        live_room_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="latestSliceMergedId != null">
        latest_slice_merged_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="combineStatus != null">
        combine_status,
      </if>
      <if test="startScene != null">
        start_scene,
      </if>
      <if test="startSceneFlag != null">
        start_scene_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sellerId != null">
        #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=INTEGER},
      </if>
      <if test="isSubtitlesDone != null">
        #{isSubtitlesDone,jdbcType=BIT},
      </if>
      <if test="subtitlesBpTs != null">
        #{subtitlesBpTs,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="videoName != null">
        #{videoName,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="videoType != null">
        #{videoType,jdbcType=INTEGER},
      </if>
      <if test="liveRoomId != null">
        #{liveRoomId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="latestSliceMergedId != null">
        #{latestSliceMergedId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="combineStatus != null">
        #{combineStatus,jdbcType=INTEGER},
      </if>
      <if test="startScene != null">
        #{startScene,jdbcType=INTEGER},
      </if>
      <if test="startSceneFlag != null">
        #{startSceneFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialExample" resultType="java.lang.Long">
    select count(*) from video_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update video_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sellerId != null">
        seller_id = #{record.sellerId,jdbcType=BIGINT},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.path != null">
        path = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.size != null">
        size = #{record.size,jdbcType=INTEGER},
      </if>
      <if test="record.isSubtitlesDone != null">
        is_subtitles_done = #{record.isSubtitlesDone,jdbcType=BIT},
      </if>
      <if test="record.subtitlesBpTs != null">
        subtitles_bp_ts = #{record.subtitlesBpTs,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.videoName != null">
        video_name = #{record.videoName,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=INTEGER},
      </if>
      <if test="record.videoType != null">
        video_type = #{record.videoType,jdbcType=INTEGER},
      </if>
      <if test="record.liveRoomId != null">
        live_room_id = #{record.liveRoomId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.latestSliceMergedId != null">
        latest_slice_merged_id = #{record.latestSliceMergedId,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.combineStatus != null">
        combine_status = #{record.combineStatus,jdbcType=INTEGER},
      </if>
      <if test="record.startScene != null">
        start_scene = #{record.startScene,jdbcType=INTEGER},
      </if>
      <if test="record.startSceneFlag != null">
        start_scene_flag = #{record.startSceneFlag,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update video_material
    set id = #{record.id,jdbcType=BIGINT},
      seller_id = #{record.sellerId,jdbcType=BIGINT},
      duration = #{record.duration,jdbcType=INTEGER},
      path = #{record.path,jdbcType=VARCHAR},
      size = #{record.size,jdbcType=INTEGER},
      is_subtitles_done = #{record.isSubtitlesDone,jdbcType=BIT},
      subtitles_bp_ts = #{record.subtitlesBpTs,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      video_name = #{record.videoName,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      start_time = #{record.startTime,jdbcType=INTEGER},
      video_type = #{record.videoType,jdbcType=INTEGER},
      live_room_id = #{record.liveRoomId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      latest_slice_merged_id = #{record.latestSliceMergedId,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      combine_status = #{record.combineStatus,jdbcType=INTEGER},
      start_scene = #{record.startScene,jdbcType=INTEGER},
      start_scene_flag = #{record.startSceneFlag,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.VideoMaterial">
    update video_material
    <set>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        size = #{size,jdbcType=INTEGER},
      </if>
      <if test="isSubtitlesDone != null">
        is_subtitles_done = #{isSubtitlesDone,jdbcType=BIT},
      </if>
      <if test="subtitlesBpTs != null">
        subtitles_bp_ts = #{subtitlesBpTs,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="videoName != null">
        video_name = #{videoName,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="videoType != null">
        video_type = #{videoType,jdbcType=INTEGER},
      </if>
      <if test="liveRoomId != null">
        live_room_id = #{liveRoomId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="latestSliceMergedId != null">
        latest_slice_merged_id = #{latestSliceMergedId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="combineStatus != null">
        combine_status = #{combineStatus,jdbcType=INTEGER},
      </if>
      <if test="startScene != null">
        start_scene = #{startScene,jdbcType=INTEGER},
      </if>
      <if test="startSceneFlag != null">
        start_scene_flag = #{startSceneFlag,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.VideoMaterial">
    update video_material
    set seller_id = #{sellerId,jdbcType=BIGINT},
      duration = #{duration,jdbcType=INTEGER},
      path = #{path,jdbcType=VARCHAR},
      size = #{size,jdbcType=INTEGER},
      is_subtitles_done = #{isSubtitlesDone,jdbcType=BIT},
      subtitles_bp_ts = #{subtitlesBpTs,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      video_name = #{videoName,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=INTEGER},
      video_type = #{videoType,jdbcType=INTEGER},
      live_room_id = #{liveRoomId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      latest_slice_merged_id = #{latestSliceMergedId,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      combine_status = #{combineStatus,jdbcType=INTEGER},
      start_scene = #{startScene,jdbcType=INTEGER},
      start_scene_flag = #{startSceneFlag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.VideoMaterialExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from video_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>