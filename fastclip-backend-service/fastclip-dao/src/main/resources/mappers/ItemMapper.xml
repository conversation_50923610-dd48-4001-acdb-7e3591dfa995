<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.ItemMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.Item">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="out_item_id" jdbcType="VARCHAR" property="outItemId" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="des" jdbcType="VARCHAR" property="des" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="share_url" jdbcType="VARCHAR" property="shareUrl" />
    <result column="is_presell" jdbcType="BIT" property="isPresell" />
    <result column="is_available" jdbcType="BIT" property="isAvailable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_publish" jdbcType="BIT" property="isPublish" />
    <result column="material_info" jdbcType="VARCHAR" property="materialInfo" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="pic_num" jdbcType="INTEGER" property="picNum" />
    <result column="is_pic_downloaded" jdbcType="BIT" property="isPicDownloaded" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, item_name, out_item_id, seller_id, des, creator, share_url, is_presell, is_available, 
    create_time, update_time, is_publish, material_info, item_type, creator_id, pic_num, 
    is_pic_downloaded
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.ItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.ItemExample">
    delete from item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.Item">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into item (item_name, out_item_id, seller_id, 
      des, creator, share_url, 
      is_presell, is_available, create_time, 
      update_time, is_publish, material_info, 
      item_type, creator_id, pic_num, 
      is_pic_downloaded)
    values (#{itemName,jdbcType=VARCHAR}, #{outItemId,jdbcType=VARCHAR}, #{sellerId,jdbcType=BIGINT}, 
      #{des,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{shareUrl,jdbcType=VARCHAR}, 
      #{isPresell,jdbcType=BIT}, #{isAvailable,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isPublish,jdbcType=BIT}, #{materialInfo,jdbcType=VARCHAR}, 
      #{itemType,jdbcType=INTEGER}, #{creatorId,jdbcType=BIGINT}, #{picNum,jdbcType=INTEGER}, 
      #{isPicDownloaded,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.Item">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="itemName != null">
        item_name,
      </if>
      <if test="outItemId != null">
        out_item_id,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="des != null">
        des,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="shareUrl != null">
        share_url,
      </if>
      <if test="isPresell != null">
        is_presell,
      </if>
      <if test="isAvailable != null">
        is_available,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isPublish != null">
        is_publish,
      </if>
      <if test="materialInfo != null">
        material_info,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="picNum != null">
        pic_num,
      </if>
      <if test="isPicDownloaded != null">
        is_pic_downloaded,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="outItemId != null">
        #{outItemId,jdbcType=VARCHAR},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="des != null">
        #{des,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="shareUrl != null">
        #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="isPresell != null">
        #{isPresell,jdbcType=BIT},
      </if>
      <if test="isAvailable != null">
        #{isAvailable,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPublish != null">
        #{isPublish,jdbcType=BIT},
      </if>
      <if test="materialInfo != null">
        #{materialInfo,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="picNum != null">
        #{picNum,jdbcType=INTEGER},
      </if>
      <if test="isPicDownloaded != null">
        #{isPicDownloaded,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.ItemExample" resultType="java.lang.Long">
    select count(*) from item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.itemName != null">
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.outItemId != null">
        out_item_id = #{record.outItemId,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerId != null">
        seller_id = #{record.sellerId,jdbcType=BIGINT},
      </if>
      <if test="record.des != null">
        des = #{record.des,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.shareUrl != null">
        share_url = #{record.shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isPresell != null">
        is_presell = #{record.isPresell,jdbcType=BIT},
      </if>
      <if test="record.isAvailable != null">
        is_available = #{record.isAvailable,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isPublish != null">
        is_publish = #{record.isPublish,jdbcType=BIT},
      </if>
      <if test="record.materialInfo != null">
        material_info = #{record.materialInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.picNum != null">
        pic_num = #{record.picNum,jdbcType=INTEGER},
      </if>
      <if test="record.isPicDownloaded != null">
        is_pic_downloaded = #{record.isPicDownloaded,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update item
    set id = #{record.id,jdbcType=BIGINT},
      item_name = #{record.itemName,jdbcType=VARCHAR},
      out_item_id = #{record.outItemId,jdbcType=VARCHAR},
      seller_id = #{record.sellerId,jdbcType=BIGINT},
      des = #{record.des,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      share_url = #{record.shareUrl,jdbcType=VARCHAR},
      is_presell = #{record.isPresell,jdbcType=BIT},
      is_available = #{record.isAvailable,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_publish = #{record.isPublish,jdbcType=BIT},
      material_info = #{record.materialInfo,jdbcType=VARCHAR},
      item_type = #{record.itemType,jdbcType=INTEGER},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      pic_num = #{record.picNum,jdbcType=INTEGER},
      is_pic_downloaded = #{record.isPicDownloaded,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.Item">
    update item
    <set>
      <if test="itemName != null">
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="outItemId != null">
        out_item_id = #{outItemId,jdbcType=VARCHAR},
      </if>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="des != null">
        des = #{des,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="shareUrl != null">
        share_url = #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="isPresell != null">
        is_presell = #{isPresell,jdbcType=BIT},
      </if>
      <if test="isAvailable != null">
        is_available = #{isAvailable,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPublish != null">
        is_publish = #{isPublish,jdbcType=BIT},
      </if>
      <if test="materialInfo != null">
        material_info = #{materialInfo,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="picNum != null">
        pic_num = #{picNum,jdbcType=INTEGER},
      </if>
      <if test="isPicDownloaded != null">
        is_pic_downloaded = #{isPicDownloaded,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.Item">
    update item
    set item_name = #{itemName,jdbcType=VARCHAR},
      out_item_id = #{outItemId,jdbcType=VARCHAR},
      seller_id = #{sellerId,jdbcType=BIGINT},
      des = #{des,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      share_url = #{shareUrl,jdbcType=VARCHAR},
      is_presell = #{isPresell,jdbcType=BIT},
      is_available = #{isAvailable,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_publish = #{isPublish,jdbcType=BIT},
      material_info = #{materialInfo,jdbcType=VARCHAR},
      item_type = #{itemType,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=BIGINT},
      pic_num = #{picNum,jdbcType=INTEGER},
      is_pic_downloaded = #{isPicDownloaded,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.ItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>