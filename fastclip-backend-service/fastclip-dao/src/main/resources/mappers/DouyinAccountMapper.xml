<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.DouyinAccountMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.DouyinAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="douyin_name" jdbcType="VARCHAR" property="douyinName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="cover_container_path" jdbcType="VARCHAR" property="coverContainerPath" />
    <result column="cover_background" jdbcType="VARCHAR" property="coverBackground" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="expires_in" jdbcType="TIMESTAMP" property="expiresIn" />
    <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken" />
    <result column="refresh_expires_in" jdbcType="TIMESTAMP" property="refreshExpiresIn" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="invite_token" jdbcType="VARCHAR" property="inviteToken" />
    <result column="invite_done" jdbcType="BIT" property="inviteDone" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="self_id" jdbcType="BIGINT" property="selfId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, phone, seller_id, douyin_name, create_time, update_time, cover_container_path, 
    cover_background, code, access_token, expire_time, expires_in, refresh_token, refresh_expires_in, 
    open_id, invite_token, invite_done, type, team_id, self_id
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.DouyinAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from douyin_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from douyin_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from douyin_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.DouyinAccountExample">
    delete from douyin_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.DouyinAccount">
    insert into douyin_account (id, phone, seller_id, 
      douyin_name, create_time, update_time, 
      cover_container_path, cover_background, code, 
      access_token, expire_time, expires_in, 
      refresh_token, refresh_expires_in, open_id, 
      invite_token, invite_done, type, 
      team_id, self_id)
    values (#{id,jdbcType=BIGINT}, #{phone,jdbcType=VARCHAR}, #{sellerId,jdbcType=BIGINT}, 
      #{douyinName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{coverContainerPath,jdbcType=VARCHAR}, #{coverBackground,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{accessToken,jdbcType=VARCHAR}, #{expireTime,jdbcType=TIMESTAMP}, #{expiresIn,jdbcType=TIMESTAMP}, 
      #{refreshToken,jdbcType=VARCHAR}, #{refreshExpiresIn,jdbcType=TIMESTAMP}, #{openId,jdbcType=VARCHAR}, 
      #{inviteToken,jdbcType=VARCHAR}, #{inviteDone,jdbcType=BIT}, #{type,jdbcType=INTEGER}, 
      #{teamId,jdbcType=BIGINT}, #{selfId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.DouyinAccount">
    insert into douyin_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="douyinName != null">
        douyin_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="coverContainerPath != null">
        cover_container_path,
      </if>
      <if test="coverBackground != null">
        cover_background,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="accessToken != null">
        access_token,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="expiresIn != null">
        expires_in,
      </if>
      <if test="refreshToken != null">
        refresh_token,
      </if>
      <if test="refreshExpiresIn != null">
        refresh_expires_in,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="inviteToken != null">
        invite_token,
      </if>
      <if test="inviteDone != null">
        invite_done,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="selfId != null">
        self_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="douyinName != null">
        #{douyinName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coverContainerPath != null">
        #{coverContainerPath,jdbcType=VARCHAR},
      </if>
      <if test="coverBackground != null">
        #{coverBackground,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresIn != null">
        #{expiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="refreshToken != null">
        #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshExpiresIn != null">
        #{refreshExpiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="inviteToken != null">
        #{inviteToken,jdbcType=VARCHAR},
      </if>
      <if test="inviteDone != null">
        #{inviteDone,jdbcType=BIT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="selfId != null">
        #{selfId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.DouyinAccountExample" resultType="java.lang.Long">
    select count(*) from douyin_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update douyin_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerId != null">
        seller_id = #{record.sellerId,jdbcType=BIGINT},
      </if>
      <if test="record.douyinName != null">
        douyin_name = #{record.douyinName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.coverContainerPath != null">
        cover_container_path = #{record.coverContainerPath,jdbcType=VARCHAR},
      </if>
      <if test="record.coverBackground != null">
        cover_background = #{record.coverBackground,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.accessToken != null">
        access_token = #{record.accessToken,jdbcType=VARCHAR},
      </if>
      <if test="record.expireTime != null">
        expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiresIn != null">
        expires_in = #{record.expiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refreshToken != null">
        refresh_token = #{record.refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="record.refreshExpiresIn != null">
        refresh_expires_in = #{record.refreshExpiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="record.openId != null">
        open_id = #{record.openId,jdbcType=VARCHAR},
      </if>
      <if test="record.inviteToken != null">
        invite_token = #{record.inviteToken,jdbcType=VARCHAR},
      </if>
      <if test="record.inviteDone != null">
        invite_done = #{record.inviteDone,jdbcType=BIT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.teamId != null">
        team_id = #{record.teamId,jdbcType=BIGINT},
      </if>
      <if test="record.selfId != null">
        self_id = #{record.selfId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update douyin_account
    set id = #{record.id,jdbcType=BIGINT},
      phone = #{record.phone,jdbcType=VARCHAR},
      seller_id = #{record.sellerId,jdbcType=BIGINT},
      douyin_name = #{record.douyinName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      cover_container_path = #{record.coverContainerPath,jdbcType=VARCHAR},
      cover_background = #{record.coverBackground,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      access_token = #{record.accessToken,jdbcType=VARCHAR},
      expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      expires_in = #{record.expiresIn,jdbcType=TIMESTAMP},
      refresh_token = #{record.refreshToken,jdbcType=VARCHAR},
      refresh_expires_in = #{record.refreshExpiresIn,jdbcType=TIMESTAMP},
      open_id = #{record.openId,jdbcType=VARCHAR},
      invite_token = #{record.inviteToken,jdbcType=VARCHAR},
      invite_done = #{record.inviteDone,jdbcType=BIT},
      type = #{record.type,jdbcType=INTEGER},
      team_id = #{record.teamId,jdbcType=BIGINT},
      self_id = #{record.selfId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.DouyinAccount">
    update douyin_account
    <set>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="douyinName != null">
        douyin_name = #{douyinName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coverContainerPath != null">
        cover_container_path = #{coverContainerPath,jdbcType=VARCHAR},
      </if>
      <if test="coverBackground != null">
        cover_background = #{coverBackground,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresIn != null">
        expires_in = #{expiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="refreshToken != null">
        refresh_token = #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshExpiresIn != null">
        refresh_expires_in = #{refreshExpiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="inviteToken != null">
        invite_token = #{inviteToken,jdbcType=VARCHAR},
      </if>
      <if test="inviteDone != null">
        invite_done = #{inviteDone,jdbcType=BIT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="selfId != null">
        self_id = #{selfId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.DouyinAccount">
    update douyin_account
    set phone = #{phone,jdbcType=VARCHAR},
      seller_id = #{sellerId,jdbcType=BIGINT},
      douyin_name = #{douyinName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      cover_container_path = #{coverContainerPath,jdbcType=VARCHAR},
      cover_background = #{coverBackground,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      access_token = #{accessToken,jdbcType=VARCHAR},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      expires_in = #{expiresIn,jdbcType=TIMESTAMP},
      refresh_token = #{refreshToken,jdbcType=VARCHAR},
      refresh_expires_in = #{refreshExpiresIn,jdbcType=TIMESTAMP},
      open_id = #{openId,jdbcType=VARCHAR},
      invite_token = #{inviteToken,jdbcType=VARCHAR},
      invite_done = #{inviteDone,jdbcType=BIT},
      type = #{type,jdbcType=INTEGER},
      team_id = #{teamId,jdbcType=BIGINT},
      self_id = #{selfId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.DouyinAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from douyin_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>