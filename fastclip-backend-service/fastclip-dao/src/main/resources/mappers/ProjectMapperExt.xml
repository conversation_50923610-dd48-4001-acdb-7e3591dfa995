<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.ProjectMapperExt">
  <select id="getProjects" parameterType="com.fastclip.common.model.request.ProjectReq" resultType="com.fastclip.dao.model.dataobject.ProjectExt">

    select * from (
    select t.id,t.itemName,t.outItemId,t.sellerId,t.shareUrl,t.projectName,t.sellerName,t.status,t.des, t.createTime, t.itemId,
    t.updateTime,t.maxMaterialDate, t.itemType, count(w.id) as worksCount,t.creatorId
    from (
    select p.id, p.project_name as projectName,p.item_type as itemType, i.item_name as itemName, i.id as itemId, i.out_item_id as outItemId, p.seller_id as
    sellerId, p.status, p.des, p.create_time as createTime, p.update_time as updateTime,p.creator_id as creatorId,
    i.share_url as shareUrl, i.is_presell as isPresell, s.seller_name as sellerName,
    max(v.start_date) as maxMaterialDate
    from project p left join item i on i.id=p.item_id left join seller s on p.seller_id=s.id left join
    video_material_clip c on i.id=c.item_id left join video_material v on c.video_id=v.id
    where 1=1
    <if test="req.id != null">and p.id =#{req.id}
    </if>
    <if test="req.projectName != null">and p.project_name like concat('%', #{req.projectName} ,'%')
    </if>
    <if test="req.itemName != null">and i.item_name like concat('%', #{req.itemName} ,'%')
    </if>
    <if test="req.sellerName != null">and s.seller_name like concat('%', #{req.sellerName} ,'%')
    </if>
    <if test="req.isAdmin != true">and p.creator_id = #{req.creatorId}
    </if>
    <if test="req.sellerIds != null">
      and
      <foreach collection="req.sellerIds" open="i.seller_id in (" item="item" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    group by p.id,
    i.item_name,
    i.id,
    i.out_item_id,
    i.seller_id,
    i.share_url,
    p.project_name,
    s.seller_name,
    p.status,
    p.des,
    p.item_type
    ) t left join works w on t.id=w.project_id
    where  1 = 1
    <if test="req.maxMaterialDate != null">
      and t.maxMaterialDate = #{req.maxMaterialDate}
    </if>
    group by t.id,
    t.itemId,
    t.itemName,
    t.outItemId,
    t.sellerId,
    t.shareUrl,
    t.projectName,
    t.sellerName,
    t.status,
    t.des,
    t.maxMaterialDate
    ) tmp
    where 1=1
    <if test="req.worksCount != null">
      and  tmp.worksCount = #{req.worksCount}
    </if>
    <if test="req.orderByClause != null">
      order by #{req.orderByClause}  #{req.ascOrDesc}
    </if>
    <if test="req.orderByClause == null">
      order by tmp.createTime desc
    </if>
    limit #{req.offset},#{req.pageSize}
  </select>
  <select id="countProjects" parameterType="com.fastclip.common.model.request.ProjectReq" resultType="java.lang.Integer">

    select count(*) from (
    select t.id,t.itemName,t.outItemId,t.sellerId,t.shareUrl,t.projectName,t.sellerName,t.status,t.des, t.createTime, t.itemId,
    t.updateTime,t.maxMaterialDate, count(w.id) as
    worksCount from (
    select p.id, p.project_name as projectName, i.item_name as itemName, i.id as itemId, i.out_item_id as outItemId, i.seller_id as
    sellerId, p.status, p.des, p.create_time as createTime, p.update_time as updateTime,
    i.share_url as shareUrl, i.is_presell as isPresell, s.seller_name as sellerName,
    max(v.start_date) as maxMaterialDate
    from project p left join item i on i.id=p.item_id left join seller s on p.seller_id=s.id left join
    video_material_clip c on i.id=c.item_id left join video_material v on c.video_id=v.id
    where 1=1
    <if test="req.projectName != null">and p.project_name like concat('%', #{req.projectName} ,'%')
    </if>
    <if test="req.itemName != null">and i.item_name like concat('%', #{req.itemName} ,'%')
    </if>
    <if test="req.sellerName != null">and s.seller_name like concat('%', #{req.sellerName} ,'%')
    </if>
    <if test="req.sellerIds != null">
      and
      <foreach collection="req.sellerIds" open="i.seller_id in (" item="item" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    group by p.id,
    i.item_name,
    i.id,
    i.out_item_id,
    i.seller_id,
    i.share_url,
    p.project_name,
    s.seller_name,
    p.status,
    p.des
    ) t left join works w on t.id=w.project_id
    where  1 = 1
    <if test="req.maxMaterialDate != null">
      and t.maxMaterialDate = #{req.maxMaterialDate}
    </if>
    group by t.id,
    t.itemId,
    t.itemName,
    t.outItemId,
    t.sellerId,
    t.shareUrl,
    t.projectName,
    t.sellerName,
    t.status,
    t.des,
    t.maxMaterialDate
    ) tmp
    where 1=1
    <if test="req.worksCount != null">
      and  tmp.worksCount = #{req.worksCount}
    </if>
  </select>
</mapper>