<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fastclip.dao.mapper.SubtitlesMapper" >
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.Subtitles" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="seller_id" property="sellerId" jdbcType="BIGINT" />
    <result column="video_id" property="videoId" jdbcType="BIGINT" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="start_ts" property="startTs" jdbcType="INTEGER" />
    <result column="end_ts" property="endTs" jdbcType="INTEGER" />
    <result column="duration" property="duration" jdbcType="INTEGER" />
    <result column="length" property="length" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, seller_id, video_id, content, start_ts, end_ts, duration, length, create_time, 
    update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.fastclip.dao.model.dataobject.SubtitlesExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from subtitles
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from subtitles
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from subtitles
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.SubtitlesExample" >
    delete from subtitles
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.Subtitles" >
    insert into subtitles (id, seller_id, video_id, 
      content, start_ts, end_ts, 
      duration, length, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{sellerId,jdbcType=BIGINT}, #{videoId,jdbcType=BIGINT}, 
      #{content,jdbcType=VARCHAR}, #{startTs,jdbcType=INTEGER}, #{endTs,jdbcType=INTEGER}, 
      #{duration,jdbcType=INTEGER}, #{length,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.Subtitles" >
    insert into subtitles
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sellerId != null" >
        seller_id,
      </if>
      <if test="videoId != null" >
        video_id,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="startTs != null" >
        start_ts,
      </if>
      <if test="endTs != null" >
        end_ts,
      </if>
      <if test="duration != null" >
        duration,
      </if>
      <if test="length != null" >
        length,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sellerId != null" >
        #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="videoId != null" >
        #{videoId,jdbcType=BIGINT},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="startTs != null" >
        #{startTs,jdbcType=INTEGER},
      </if>
      <if test="endTs != null" >
        #{endTs,jdbcType=INTEGER},
      </if>
      <if test="duration != null" >
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="length != null" >
        #{length,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.SubtitlesExample" resultType="java.lang.Integer" >
    select count(*) from subtitles
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update subtitles
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sellerId != null" >
        seller_id = #{record.sellerId,jdbcType=BIGINT},
      </if>
      <if test="record.videoId != null" >
        video_id = #{record.videoId,jdbcType=BIGINT},
      </if>
      <if test="record.content != null" >
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.startTs != null" >
        start_ts = #{record.startTs,jdbcType=INTEGER},
      </if>
      <if test="record.endTs != null" >
        end_ts = #{record.endTs,jdbcType=INTEGER},
      </if>
      <if test="record.duration != null" >
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.length != null" >
        length = #{record.length,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update subtitles
    set id = #{record.id,jdbcType=BIGINT},
      seller_id = #{record.sellerId,jdbcType=BIGINT},
      video_id = #{record.videoId,jdbcType=BIGINT},
      content = #{record.content,jdbcType=VARCHAR},
      start_ts = #{record.startTs,jdbcType=INTEGER},
      end_ts = #{record.endTs,jdbcType=INTEGER},
      duration = #{record.duration,jdbcType=INTEGER},
      length = #{record.length,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.Subtitles" >
    update subtitles
    <set >
      <if test="sellerId != null" >
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="videoId != null" >
        video_id = #{videoId,jdbcType=BIGINT},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="startTs != null" >
        start_ts = #{startTs,jdbcType=INTEGER},
      </if>
      <if test="endTs != null" >
        end_ts = #{endTs,jdbcType=INTEGER},
      </if>
      <if test="duration != null" >
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="length != null" >
        length = #{length,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.Subtitles" >
    update subtitles
    set seller_id = #{sellerId,jdbcType=BIGINT},
      video_id = #{videoId,jdbcType=BIGINT},
      content = #{content,jdbcType=VARCHAR},
      start_ts = #{startTs,jdbcType=INTEGER},
      end_ts = #{endTs,jdbcType=INTEGER},
      duration = #{duration,jdbcType=INTEGER},
      length = #{length,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>