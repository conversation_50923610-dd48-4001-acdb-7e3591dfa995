<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.ItemMapperExt">
  <select id="getItems" parameterType="com.fastclip.common.model.request.ItemReq" resultType="com.fastclip.dao.model.dataobject.ItemExt">
    select * from (
      select i.id, i.item_name as itemName, i.out_item_id as outItemId, i.seller_id as sellerId, i.des, i.creator,
             i.share_url  as shareUrl, i.is_presell as isPresell,
             i.is_available as isAvaiable, i.create_time as createTime, i.update_time as updateTime, i.is_publish as isPublish,
             max(v.start_date) as maxMaterialDate, p.id as projectId
      from item i left join project p  on i.id=p.item_id and p.creator_id = #{req.creatorId} left join video_material_clip c on i.id=c.item_id left join
          video_material v on c.video_id=v.id
      where 1=1
      <if test="req.hasProject == false">
        and p.id is null
      </if>
      <if test="req.hasProject == true">
        and p.id is not null
      </if>

      <if test="req.hasMaterial == false">
        and c.id is null
      </if>
      <if test="req.hasMaterial == true">
        and c.id is not null
      </if>
      <if test="req.itemName != null">
        and i.item_name like CONCAT('%', #{req.itemName}, '%')
      </if>
    <if test="req.itemType != null">
      and i.item_type=#{req.itemType}
    </if>
      <if test="req.sellerIds != null">
        and
        <foreach collection="req.sellerIds" open="i.seller_id in (" item="item" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      group by i.id,
      i.item_name,
      i.out_item_id,
      i.seller_id,
      i.des,
      i.creator,
      i.share_url,
      i.is_presell,
      i.is_available,
      i.create_time,
      i.update_time,
      i.is_publish,
      p.id) t
    order by t.maxMaterialDate desc
    limit #{req.offset}, #{req.pageSize}
  </select>

  <select id="count" parameterType="com.fastclip.common.model.request.ItemReq" resultType="java.lang.Integer">
    select count(*) from (
    select i.id, i.item_name as itemName, i.out_item_id as outItemId, i.seller_id as sellerId, i.des, i.creator,
    i.share_url  as shareUrl, i.is_presell as isPresell,
    i.is_available as isAvaiable, i.create_time as createTime, i.update_time as updateTime, i.is_publish as isPublish,
    max(v.start_date) as maxMaterialDate, p.id as projectId
    from item i left join project p  on i.id=p.item_id left join video_material_clip c on i.id=c.item_id left join
    video_material v on c.video_id=v.id
    where 1=1
    <if test="req.hasProject == false">
      and p.id is null
    </if>
    <if test="req.hasProject == true">
      and p.id is not null
    </if>
    <if test="req.hasMaterial == false">
      and c.id is null
    </if>
    <if test="req.hasMaterial == true">
      and c.id is not null
    </if>
    <if test="req.itemName != null">
      and i.item_name like CONCAT('%', #{req.itemName}, '%')
    </if>
    <if test="req.sellerIds != null">
      and
      <foreach collection="req.sellerIds" open="i.seller_id in (" item="item" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    group by i.id,
    i.item_name,
    i.out_item_id,
    i.seller_id,
    i.des,
    i.creator,
    i.share_url,
    i.is_presell,
    i.is_available,
    i.create_time,
    i.update_time,
    i.is_publish,
    p.id) t
  </select>

  <select id="getLiveItems" parameterType="com.fastclip.common.model.request.ItemReq" resultType="com.fastclip.dao.model.dataobject.ItemExt">
    select * from (
    select i.id, i.item_name as itemName, i.out_item_id as outItemId, i.seller_id as sellerId, i.des, i.creator,
    i.share_url  as shareUrl, i.is_presell as isPresell,
    i.is_available as isAvaiable, i.create_time as createTime, i.update_time as updateTime, i.is_publish as isPublish,
    max(v.start_date) as maxMaterialDate, p.id as projectId
    from item i left join project p  on i.id=p.item_id left join video_material_clip c on i.id=c.item_id left join
    video_material v on c.video_id=v.id
    where 1=1
    <if test="req.hasProject == false">
      and p.id is null
    </if>
    <if test="req.hasProject == true">
      and p.id is not null
    </if>
    <if test="req.itemName != null">
      and i.item_name like CONCAT('%', #{req.itemName}, '%')
    </if>
    <if test="req.itemType != null">
      and i.item_type=#{req.itemType}
    </if>
    <if test="req.sellerIds != null">
      and
      <foreach collection="req.sellerIds" open="i.seller_id in (" item="item" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    group by i.id,
    i.item_name,
    i.out_item_id,
    i.seller_id,
    i.des,
    i.creator,
    i.share_url,
    i.is_presell,
    i.is_available,
    i.create_time,
    i.update_time,
    i.is_publish,
    p.id) t
    order by t.createTime desc
    limit #{req.offset}, #{req.pageSize}
  </select>
</mapper>