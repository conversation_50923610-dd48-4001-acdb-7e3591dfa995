<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.QRCodeMapper">
  <resultMap id="BaseResultMap" type="com.fastclip.dao.model.dataobject.QRCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="douyin_account_id" jdbcType="BIGINT" property="douyinAccountId" />
    <result column="captcha" jdbcType="VARCHAR" property="captcha" />
    <result column="desc_url" jdbcType="VARCHAR" property="descUrl" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="error_code" jdbcType="INTEGER" property="errorCode" />
    <result column="is_frontier" jdbcType="BIT" property="isFrontier" />
    <result column="qrcode_index_url" jdbcType="VARCHAR" property="qrcodeIndexUrl" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, status, douyin_account_id, captcha, desc_url, description, error_code, is_frontier, 
    qrcode_index_url, token, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fastclip.dao.model.dataobject.QRCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from qr_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qr_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from qr_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fastclip.dao.model.dataobject.QRCodeExample">
    delete from qr_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fastclip.dao.model.dataobject.QRCode">
    insert into qr_code (id, status, douyin_account_id, 
      captcha, desc_url, description, 
      error_code, is_frontier, qrcode_index_url, 
      token, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{douyinAccountId,jdbcType=BIGINT}, 
      #{captcha,jdbcType=VARCHAR}, #{descUrl,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{errorCode,jdbcType=INTEGER}, #{isFrontier,jdbcType=BIT}, #{qrcodeIndexUrl,jdbcType=VARCHAR}, 
      #{token,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fastclip.dao.model.dataobject.QRCode">
    insert into qr_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="douyinAccountId != null">
        douyin_account_id,
      </if>
      <if test="captcha != null">
        captcha,
      </if>
      <if test="descUrl != null">
        desc_url,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="isFrontier != null">
        is_frontier,
      </if>
      <if test="qrcodeIndexUrl != null">
        qrcode_index_url,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="douyinAccountId != null">
        #{douyinAccountId,jdbcType=BIGINT},
      </if>
      <if test="captcha != null">
        #{captcha,jdbcType=VARCHAR},
      </if>
      <if test="descUrl != null">
        #{descUrl,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=INTEGER},
      </if>
      <if test="isFrontier != null">
        #{isFrontier,jdbcType=BIT},
      </if>
      <if test="qrcodeIndexUrl != null">
        #{qrcodeIndexUrl,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fastclip.dao.model.dataobject.QRCodeExample" resultType="java.lang.Long">
    select count(*) from qr_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qr_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.douyinAccountId != null">
        douyin_account_id = #{record.douyinAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.captcha != null">
        captcha = #{record.captcha,jdbcType=VARCHAR},
      </if>
      <if test="record.descUrl != null">
        desc_url = #{record.descUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.errorCode != null">
        error_code = #{record.errorCode,jdbcType=INTEGER},
      </if>
      <if test="record.isFrontier != null">
        is_frontier = #{record.isFrontier,jdbcType=BIT},
      </if>
      <if test="record.qrcodeIndexUrl != null">
        qrcode_index_url = #{record.qrcodeIndexUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.token != null">
        token = #{record.token,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qr_code
    set id = #{record.id,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      douyin_account_id = #{record.douyinAccountId,jdbcType=BIGINT},
      captcha = #{record.captcha,jdbcType=VARCHAR},
      desc_url = #{record.descUrl,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      error_code = #{record.errorCode,jdbcType=INTEGER},
      is_frontier = #{record.isFrontier,jdbcType=BIT},
      qrcode_index_url = #{record.qrcodeIndexUrl,jdbcType=VARCHAR},
      token = #{record.token,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fastclip.dao.model.dataobject.QRCode">
    update qr_code
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="douyinAccountId != null">
        douyin_account_id = #{douyinAccountId,jdbcType=BIGINT},
      </if>
      <if test="captcha != null">
        captcha = #{captcha,jdbcType=VARCHAR},
      </if>
      <if test="descUrl != null">
        desc_url = #{descUrl,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=INTEGER},
      </if>
      <if test="isFrontier != null">
        is_frontier = #{isFrontier,jdbcType=BIT},
      </if>
      <if test="qrcodeIndexUrl != null">
        qrcode_index_url = #{qrcodeIndexUrl,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fastclip.dao.model.dataobject.QRCode">
    update qr_code
    set status = #{status,jdbcType=INTEGER},
      douyin_account_id = #{douyinAccountId,jdbcType=BIGINT},
      captcha = #{captcha,jdbcType=VARCHAR},
      desc_url = #{descUrl,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      error_code = #{errorCode,jdbcType=INTEGER},
      is_frontier = #{isFrontier,jdbcType=BIT},
      qrcode_index_url = #{qrcodeIndexUrl,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.fastclip.dao.model.dataobject.QRCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from qr_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>