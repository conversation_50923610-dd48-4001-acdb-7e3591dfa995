<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fastclip.dao.mapper.DouyinMapper">

    <select id="getDouyinDO" resultType="com.fastclip.common.model.dataobject.DouyinDO">
        SELECT
            id,
            videoId,
            videoUrl,
            shareUrl,
            des,
            localPath,
            duration,
            author,
            status,
            create_time
        FROM
            douyin
        where videoId=#{videoId}
    </select>

    <insert id="insert" parameterType="com.fastclip.common.model.dataobject.DouyinDO" useGeneratedKeys="true" keyProperty="id">
        insert into douyin(videoId, videoUrl, shareUrl, des, duration, localPath, author,status)
        values
            (#{videoId}, #{videoUrl}, #{shareUrl},#{des},#{duration},#{localPath},#{author},#{status})
    </insert>
</mapper>