<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.SubtitlesMapperExt">
  <select id="batchInsert" parameterType="java.util.List">
    insert into subtitles (seller_id, video_id,
                           content, start_ts, end_ts,
                           duration, length, create_time,
                           update_time)
    values
       <foreach collection="list" item="item" index="index" separator=",">
           (#{item.sellerId,jdbcType=BIGINT}, #{item.videoId,jdbcType=BIGINT},
            #{item.content,jdbcType=VARCHAR}, #{item.startTs,jdbcType=INTEGER}, #{item.endTs,jdbcType=INTEGER},
            #{item.duration,jdbcType=INTEGER}, #{item.length,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP})
       </foreach>
  </select>
</mapper>