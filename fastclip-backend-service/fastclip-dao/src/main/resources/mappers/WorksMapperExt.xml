<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.WorksMapperExt">
  <select id="getWorkCountsToday" parameterType="com.fastclip.common.model.request.WorksCountReq" resultType="com.fastclip.common.model.dataobject.AccountWorksDO">
    select d.phone, count(*) as worksCountToday from douyin_account d left join works w  on d.phone=w.phone
    where w.create_time >= #{req.today} and
    <foreach collection="req.phones" open="d.phone in (" item="item" close=")" separator=",">
      #{item}
    </foreach>
    <if test="req.isAdmin != true">and w.creator_id =#{req.userId}
    </if>
    group by d.phone
  </select>

  <select id="getPublishedCountsToday" parameterType="com.fastclip.common.model.request.WorksCountReq" resultType="com.fastclip.common.model.dataobject.AccountWorksDO">
    select d.phone, count(*) as publishedWorksCountToday from douyin_account d left join works w  on d.phone=w.phone
    where w.published_time >= #{req.today} and
    <foreach collection="req.phones" open="d.phone in (" item="item" close=")" separator=",">
      #{item}
    </foreach>
    <if test="req.isAdmin != true">and w.creator_id =#{req.userId}
    </if>
    group by d.phone
  </select>


  <select id="getUnPublishedCounts" parameterType="com.fastclip.common.model.request.WorksCountReq" resultType="com.fastclip.common.model.dataobject.AccountWorksDO">
    select d.phone, count(*) as unPublishedWorks from douyin_account d left join works w  on d.phone=w.phone
    where w.is_published=0 and
    <foreach collection="req.phones" open="d.phone in (" item="item" close=")" separator=",">
      #{item}
    </foreach>
    <if test="req.isAdmin != true">and w.creator_id =#{req.userId}
    </if>
    group by d.phone
  </select>

</mapper>