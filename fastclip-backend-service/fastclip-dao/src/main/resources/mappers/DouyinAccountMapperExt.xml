<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastclip.dao.mapper.DouyinAccountMapperExt">
  <select id="countByTeamId" parameterType="com.fastclip.common.model.request.DouyinAccountByTeamReq" resultType="com.fastclip.common.model.response.DouyinAccountByTeamRes">
    select team_id, count(*) from douyin_account
    where
      <foreach collection="req.teamIds" open="team_id in (" item="item" close=")" separator=",">
        #{item}
      </foreach>
    group by team_id
  </select>
</mapper>