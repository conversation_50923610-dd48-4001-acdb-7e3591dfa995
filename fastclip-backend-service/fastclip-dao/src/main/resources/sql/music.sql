CREATE TABLE `music`  (
                            `id` int(10)  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `name` varchar(100) DEFAULT NULL  COMMENT '音乐名称',
                            `path` varchar(200)  DEFAULT NULL COMMENT '地址',
                            `duration` int(10) DEFAULT NULL COMMENT '视频片段时长，单位ms',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            index(`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='音乐';