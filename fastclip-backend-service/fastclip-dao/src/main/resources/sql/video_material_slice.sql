CREATE TABLE `video_material_slice`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '视频id',
                            `video_id` bigint unsigned  NOT NULL COMMENT '视频素材id',
                            `duration` int(10) NOT NULL COMMENT '视频时长',
                            `path` varchar(128)  DEFAULT NULL COMMENT '视频地址',
                            `size` int(10) NOT NULL COMMENT '视频大小',
                            `number` int(10) NOT NULL COMMENT '素材片段序号',
                            `start_ts` int(10) NOT NULL COMMENT '相对开始时间（毫秒）',
                            `is_subtitles_done` TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否完成字幕解析',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`video_id`, `start_ts`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='直播视频素材分片表';

