CREATE TABLE `works_tracks`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '轨道id',
                            `project_id` bigint unsigned  NOT NULL  COMMENT '项目id',
                            `item_id` bigint unsigned  NOT NULL  COMMENT '商品id',
                            `works_id` bigint unsigned  NOT NULL  COMMENT '作品id',
                            `track_type` varchar(32)  NOT NULL COMMENT '轨道类型',
                            `order` int(10) DEFAULT NULL COMMENT '轨道序号',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`works_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='作品轨道信息';