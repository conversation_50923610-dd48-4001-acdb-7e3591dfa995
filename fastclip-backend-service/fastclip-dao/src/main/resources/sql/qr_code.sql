CREATE TABLE IF NOT EXISTS `qr_code` (
                                           `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '自增id',
                                           `status` int(10)  NOT NULL DEFAULT 1 COMMENT '邀请码状态，0：未绑定，1：已绑定',
                                            `douyin_account_id` bigint unsigned NOT NULL ,
                                            `captcha` VARCHAR(128) NOT NULL ,
                                            `desc_url` VARCHAR(128) NOT NULL,
                                            `description` VARCHAR(128) NOT NULL,
                                            `error_code` int(10) NOT NULL,
                                            `is_frontier` TINYINT(1) NOT NULL,
                                            `qrcode_index_url` VARCHAR(500) NOT NULL,
                                            `token` VARCHAR(128) NOT NULL,
                                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                            PRIMARY KEY (`id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='邀请码表';
