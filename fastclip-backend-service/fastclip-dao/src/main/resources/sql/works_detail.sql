CREATE TABLE `works_detail`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '视频作品明细id',
                            `project_id` bigint unsigned  NOT NULL  COMMENT '项目id',
                            `works_id` bigint unsigned  NOT NULL  COMMENT '作品id',
                            `seller_id` bigint unsigned  NOT NULL  COMMENT '直播达人id',
                            `video_clip_id` bigint unsigned  NOT NULL COMMENT '视频片段id',
                            `sort` int(10) NOT NULL COMMENT '序号',
                            `duration` int(10) DEFAULT NULL COMMENT '视频片段时长，单位ms',
                            `tran_code` varchar(20)  DEFAULT NULL COMMENT '转场代码',
                            `tag_code` varchar(20)  DEFAULT NULL COMMENT '标签代码',
                            `cut_percent` int(10) NOT NULL COMMENT '逐步裁剪比例，10-20之间',
                            `trans_id` int(10) NOT NULL COMMENT '转场效果id',
                            `flip_flag` TINYINT(1)  DEFAULT 0 COMMENT '是否进行镜像翻转,0:不翻转，1:翻转',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`works_id`),
                            INDEX (`project_id`,`works_id`,`video_clip_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='视频作品明细表';