CREATE TABLE `team`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `name` varchar(20)  NOT NULL  COMMENT '团队名称',
                            `phone` varchar(20)  NOT NULL COMMENT '团队联系方式',
                            `province` varchar(20)  NOT NULL COMMENT '省',
                            `city` varchar(20)  NOT NULL COMMENT '市',
                            `share_ratio` int(10)  NOT NULL COMMENT '分润比例',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='团队';






