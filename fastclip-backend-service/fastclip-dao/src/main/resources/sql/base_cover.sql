CREATE TABLE `base_cover`  (
                            `id` int(10)  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `name` varchar(100) DEFAULT NULL  COMMENT '封面背景名称',
                            `path` varchar(200)  DEFAULT NULL COMMENT '地址',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            index(`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='封面背景';

insert into base_cover(name, path) values('封面1', '/data/app/douyin/baseCover/base_cover1.jpg');
insert into base_cover(name, path) values('封面2', '/data/app/douyin/baseCover/base_cover2.jpg');
insert into base_cover(name, path) values('封面3', '/data/app/douyin/baseCover/base_cover3.jpg');
insert into base_cover(name, path) values('封面4', '/data/app/douyin/baseCover/base_cover4.jpg');
insert into base_cover(name, path) values('封面5', '/data/app/douyin/baseCover/base_cover5.jpg');
insert into base_cover(name, path) values('封面6', '/data/app/douyin/baseCover/base_cover6.jpg');
insert into base_cover(name, path) values('封面7', '/data/app/douyin/baseCover/base_cover7.jpg');
insert into base_cover(name, path) values('封面8', '/data/app/douyin/baseCover/base_cover8.jpg');
insert into base_cover(name, path) values('封面9', '/data/app/douyin/baseCover/base_cover9.jpg');
insert into base_cover(name, path) values('封面10', '/data/app/douyin/baseCover/base_cover10.jpg');
insert into base_cover(name, path) values('封面11', '/data/app/douyin/baseCover/base_cover11.jpg');
insert into base_cover(name, path) values('封面12', '/data/app/douyin/baseCover/base_cover12.jpg');