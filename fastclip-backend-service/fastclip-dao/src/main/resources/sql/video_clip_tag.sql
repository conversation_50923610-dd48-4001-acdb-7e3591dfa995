CREATE TABLE `video_clip_tag`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `clip_id` bigint unsigned  NOT NULL COMMENT '视频id',
                            `tag_code` varchar(128)  DEFAULT NULL COMMENT '切片标签，如开头、穿搭展示、材质介绍、尺码介绍等',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`clip_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='视频片段标签';