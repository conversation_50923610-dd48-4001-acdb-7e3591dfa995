CREATE TABLE `video_material_clip`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '视频素材片段id',
                            `item_id` bigint unsigned  NOT NULL  COMMENT '关联商品id',
                            `video_id` bigint unsigned  NOT NULL  COMMENT '视频id',
                            `seller_id` bigint unsigned  NOT NULL COMMENT '直播达人id',
                            `duration` int(10) DEFAULT NULL COMMENT '视频片段时长，单位ms',
                            `start_ts` int(10) NOT NULL COMMENT '开始时间，单位ms',
                            `end_ts` int(10) DEFAULT NULL COMMENT '结束时间，单位ms',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`seller_id`),
                            INDEX (`item_id`),
                            unique index(`video_id`, `item_id`, `start_ts`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='商品所在视频素材表';
