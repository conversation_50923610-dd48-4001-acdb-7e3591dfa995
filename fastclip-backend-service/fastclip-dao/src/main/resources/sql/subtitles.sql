CREATE TABLE `subtitles`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `seller_id` bigint unsigned  NOT NULL COMMENT '直播达人id',
                            `video_id`bigint unsigned  NOT NULL COMMENT '视频id',
                            `content` varchar(256)  DEFAULT NULL COMMENT '字幕内容',
                            `start_ts` int(10) NOT NULL COMMENT '开始时间，精确到毫秒',
                            `end_ts` int(10) NOT NULL COMMENT '结束时间，精确到毫秒',
                            `duration` int(10) NOT NULL COMMENT '时长',
                            `length` int(10) NOT NULL COMMENT '字数',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE INDEX (`video_id`,`start_ts`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='视频素材字幕表';
