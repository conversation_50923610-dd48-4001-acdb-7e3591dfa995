CREATE TABLE `video_material`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '视频id',
                            `seller_id` bigint unsigned  NOT NULL COMMENT '直播达人id',
                            `duration` int(10) NOT NULL COMMENT '视频时长',
                            `path` varchar(128)  DEFAULT NULL COMMENT '视频地址',
                            `size` int(10) NOT NULL COMMENT '视频大小',
                            `is_subtitles_done` TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否完成字幕解析',
                            `subtitles_bp_ts` int(10) NOT NULL DEFAULT 0 COMMENT '字幕解析断点',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='视频素材表';
alter table `video_material` add `video_name` varchar(128);
alter table `video_material` add `start_date` datetime;
alter table `video_material` add `start_time` int(10);
alter table `video_material` add `video_type` int(10) DEFAULT 1 COMMENT '视频素材类型:1，下载素材；2，直播素材；3，分镜素材';
alter table `video_material` add `live_room_id` varchar(30) DEFAULT NULL COMMENT '直播间id';
alter table `video_material` add `status` int(10) DEFAULT 1 COMMENT '视频素材状态：1，完成录制；2，录制中';
alter table `video_material` add `combine_status` int(10) DEFAULT 1 COMMENT '视频素材合并状态：1，完成合并；2，合并中';
alter table `video_material` add `latest_slice_merged_id` int(10) DEFAULT 0 COMMENT '最新合并的视频碎片id';
alter table `video_material` add `sort` int(10) DEFAULT 1 COMMENT '素材所在日期的顺序';
alter table `video_material` add index  (`path`);
alter table `video_material` add `start_scene_flag` TINYINT(1) DEFAULT false COMMENT '是否为某场次起始位置';
alter table `video_material` add `start_scene` int(10) DEFAULT NULL COMMENT '素材所在直播场次，且该素材为起始位置';
alter table `video_material` add index  (`seller_id`, `start_date`);

insert into video_material(`id`,`live_room_id`,`path`, `seller_id`,`start_date`,`start_time`,`status`,`video_type`,`duration`,`size`,`video_name`)
values (121, '607264452495','/Volumes/共享文件夹/douyin/tmp/hls/11_2025011162670069.mp4',11, '2025-01-11',62670069,2,2,0,0,'2025-01-11')

