CREATE TABLE `subtitles_cut` (
                                 `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                 `project_id` bigint unsigned NOT NULL COMMENT '项目id',
                                 `video_id` bigint unsigned NOT NULL COMMENT '视频id',
                                 `subtitles_id` bigint unsigned NOT NULL COMMENT '字幕id',
                                 `cut_start_content` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裁剪的前段字幕',
                                 `cut_end_content` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裁剪的后段字幕',
                                 `content` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裁剪后的字幕',
                                 `cut_start_ts` int NOT NULL COMMENT '裁剪后的开始时间，精确到毫秒',
                                 `cut_end_ts` int NOT NULL COMMENT '裁剪后的结束时间，精确到毫秒',
                                 `duration` int NOT NULL COMMENT '裁剪后的时长，精确到毫秒',
                                 `length` int NOT NULL COMMENT '裁剪后的字数',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 UNIQUE KEY `project_id` (`project_id`,`subtitles_id`),
                                 KEY `project_id_2` (`project_id`,`video_id`,`cut_start_ts`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=COMPACT COMMENT='经过裁剪后的字幕片段';
