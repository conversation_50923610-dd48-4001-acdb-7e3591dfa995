CREATE TABLE `works_publish_info`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '发布id',
                            `works_id` bigint unsigned  NOT NULL  COMMENT '作品id',
                            `project_id` bigint unsigned  NOT NULL  COMMENT '项目id',
                            `item_id` bigint unsigned  NOT NULL  COMMENT '商品id',
                            `seller_id` bigint unsigned  NOT NULL  COMMENT '直播达人id',
                            `special_effects_template_code` varchar(32) DEFAULT NULL COMMENT '去重特效模板code',
                            `status` TINYINT(1)  DEFAULT 1 COMMENT '发布状态：0，已撤销；1，已发布',
                            `publish_douyin_id` TINYINT(1)  DEFAULT 1 COMMENT '发布到的抖音账号id',
                            `publish_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间' ,
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`works_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='作品发布信息表';