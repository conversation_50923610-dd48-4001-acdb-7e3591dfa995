CREATE TABLE `ending`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '商品id',
                            `item_name` varchar(200)  NOT NULL COMMENT '商品名称',
                            `out_item_id`varchar(100)  NOT NULL COMMENT '外部商品id',
                            `seller_id` bigint unsigned  NOT NULL COMMENT '关联直播达人',
                            `des` varchar(1280)  DEFAULT NULL COMMENT '项目描述',
                            `creator` varchar(64) NOT NULL COMMENT '创建人',
                            `share_url` varchar(200) NOT NULL COMMENT '分享链接',
                            `is_presell` TINYINT(1)  DEFAULT 1 COMMENT '是否预售',
                            `is_available` TINYINT(1)  DEFAULT 1 COMMENT '是否有货',
                            `is_publish` TINYINT(1)  DEFAULT 1 COMMENT '是否已完成',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`seller_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='商品表';