CREATE TABLE `seller`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '达人id',
                            `seller_name` varchar(32)  NOT NULL COMMENT '达人名称',
                            `des` varchar(128)  DEFAULT NULL COMMENT '达人描述',
                            `material_base_path` varchar(256)  DEFAULT NULL COMMENT '素材基础路径',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='直播达人表';