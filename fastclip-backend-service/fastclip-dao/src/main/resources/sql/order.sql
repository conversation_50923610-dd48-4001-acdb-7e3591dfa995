CREATE TABLE `orders`  (
                          `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '订单id',
                          `order_id` VARCHAR(64) NOT NULL  COMMENT '订单号',
                          `product_id` VARCHAR(64) NOT NULL COMMENT '商品ID',
                          `product_name` VARCHAR(500) NOT NULL COMMENT '商品名称',
                          `product_img` VARCHAR(500) COMMENT '商品图片URL',
                          `author_account` VARCHAR(255) COMMENT '作者账号昵称(抖音/火山作者)',
                          `shop_name` VARCHAR(255) COMMENT '商家名称',
                          `total_pay_amount` BIGINT COMMENT '订单支付金额，单位分',
                          `commission_rate` BIGINT COMMENT '达人佣金率（真实数据x1万后的值）',
                          `flow_point` VARCHAR(50) COMMENT '订单状态（PAY_SUCC/REFUND/SETTLE/CONFIRM）',
                          `app` VARCHAR(50) COMMENT 'App名称（抖音/火山）',
                          `update_time` DATETIME COMMENT '联盟侧订单更新时间',
                          `pay_success_time` DATETIME COMMENT '付款时间',
                          `settle_time` DATETIME COMMENT '结算时间（结算前为空）',
                          `pay_goods_amount` BIGINT COMMENT '预估参与结算金额',
                          `settled_goods_amount` BIGINT COMMENT '实际参与结算金额',
                          `real_commission` BIGINT COMMENT '达人实际佣金',
                          `estimated_commission` BIGINT COMMENT '达人预估佣金',
                          `item_num` BIGINT COMMENT '商品数目',
                          `shop_id` BIGINT COMMENT '店铺ID',
                          `refund_time` DATETIME COMMENT '退款订单退款时间',
                          `estimated_total_commission` BIGINT COMMENT '总佣金（预估）',
                          `estimated_tech_service_fee` BIGINT COMMENT '预估平台技术服务费',
                          `pick_source_client_key` VARCHAR(64) COMMENT '选品App client_key',
                          `pick_extra` VARCHAR(500) COMMENT '选品来源自定义参数',
                          `author_short_id` VARCHAR(64) COMMENT '达人抖音号/火山号',
                           PRIMARY KEY (`id`) USING BTREE,
                           UNIQUE INDEX(`order_id`),
                           INDEX (`author_short_id`,`pay_success_time`),
                           INDEX (`product_id`,`pay_success_time`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='订单表';