CREATE TABLE `filter_effect`  (
                            `id` int(10)  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `name` varchar(100) DEFAULT NULL  COMMENT '滤镜名称',
                            `saturate` int(10) DEFAULT NULL COMMENT '饱和度参数',
                            `lightness` int(10) DEFAULT NULL COMMENT '明亮度参数',
                            `contrast` int(10) DEFAULT NULL COMMENT '对比度参数',
                            `shadow` int(10) DEFAULT NULL COMMENT '阴影度参数',
                            `highLight` int(10) DEFAULT NULL COMMENT '高亮度参数',
                            `temperature` int(10) DEFAULT NULL COMMENT '视频片段时长，单位ms',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            index(`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='滤镜效果表';

insert into filter_effect(name, saturate, lightness, contrast, shadow, highLight, temperature) values('滤镜1', 25, 10, 5, 10, -10, -10);
insert into filter_effect(name, saturate, lightness, contrast, shadow, highLight, temperature) values('滤镜2', 20, 8, 2, 4, -8, -11);
insert into filter_effect(name, saturate, lightness, contrast, shadow, highLight, temperature) values('滤镜3', 15, 9, 10, 12, -9, -12);