CREATE TABLE `cx_clip`  (
                           `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                           `cx_account`varchar(100)  NOT NULL COMMENT '蝉选账号',
                           `star_id`varchar(100)  NOT NULL COMMENT '达人id',
                           `star_name`varchar(100)  NOT NULL COMMENT '达人名称',
                           `clip_size` int(10) NOT NULL COMMENT '视频大小KB',
                           `duration` int(10) NOT NULL COMMENT '视频时长',
                           `clip_tag`varchar(100)  NOT NULL COMMENT '标签',
                           `cover`varchar(200)  NOT NULL COMMENT '封面',
                           `is_download` TINYINT(1)  DEFAULT 0 COMMENT '是否已经下载',
                           `is_published` TINYINT(1)  DEFAULT 0 COMMENT '是否已经发布',
                           `need_cut` TINYINT(1)  DEFAULT 0 COMMENT '是否需要重新剪切',
                           `room_id` varchar(20) DEFAULT NULL COMMENT '房间id',
                           `start_ts` datetime NOT NULL COMMENT '开始时间',
                           `task_id` varchar(50) NOT NULL COMMENT '任务id',
                           `title` varchar(50) DEFAULT NULL COMMENT '标题',
                           `url` varchar(200) DEFAULT NULL COMMENT '视频url',
                           `product_id` varchar(50) DEFAULT NULL COMMENT '关联的商品id',
                           `product_name` varchar(50) DEFAULT NULL COMMENT '关联的商品名称',
                           `local_audio_path` varchar(100) DEFAULT NULL COMMENT '本地路基',
                           `local_video_path` varchar(100) DEFAULT NULL COMMENT '本地路基',
                           `local_cut_video_path` varchar(100) DEFAULT NULL COMMENT '本地路基',
                           `local_srt_path` varchar(100) DEFAULT NULL COMMENT '本地路基',
                           `local_cut_srt_path` varchar(100) DEFAULT NULL COMMENT '本地路基',
                           `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                           `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                           PRIMARY KEY (`id`) USING BTREE,
                           unique INDEX (`task_id`),
                           INDEX (`start_ts`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='蝉选切片信息';

CREATE TABLE `cx_item`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                             `cx_account`varchar(100)  NOT NULL COMMENT '蝉选账号',
                             `star_id`varchar(100)  NOT NULL COMMENT '达人id',
                             `star_name`varchar(100)  NOT NULL COMMENT '达人名称',
                             `product_id` varchar(50) DEFAULT NULL COMMENT '关联的商品id',
                             `material_product_id` varchar(50) DEFAULT NULL COMMENT '关联的素材中的商品id',
                             `product_name` varchar(50) DEFAULT NULL COMMENT '关联的商品名称',
                             `price` int(10) NOT NULL COMMENT '价格',
                            `is_publish` int(10) DEFAULT 0 COMMENT '是否已发布',
                            `presell_type` int(10) NOT NULL COMMENT '预售类型：1，现货；2，预售。',
                            `product_url` varchar(500) DEFAULT NULL COMMENT '商品链接',
                            `cj_material_link` varchar(500) DEFAULT NULL COMMENT '素材链接',
                            `cut_video_path` varchar(500) DEFAULT NULL COMMENT '剪切后的视频路径',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                             `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            unique INDEX (`product_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='蝉选商品信息';

CREATE TABLE `cx_cut_clip`  (
                             `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                             `cx_account`varchar(100)  NOT NULL COMMENT '蝉选账号',
                             `star_id`varchar(100)  NOT NULL COMMENT '达人id',
                             `star_name`varchar(100)  NOT NULL COMMENT '达人名称',
                             `clip_size` int(10) NOT NULL COMMENT '视频大小KB',
                             `duration` int(10) NOT NULL COMMENT '视频时长',
                             `title` varchar(200) DEFAULT NULL COMMENT '标题',
                             `task_id` varchar(50) NOT NULL COMMENT '任务id',
                             `product_id` varchar(50) DEFAULT NULL COMMENT '关联的商品id',
                             `material_product_id` varchar(50) DEFAULT NULL COMMENT '关联的素材中的商品id',
                             `product_name` varchar(50) DEFAULT NULL COMMENT '关联的商品名称',
                             `product_url` varchar(500) DEFAULT NULL COMMENT '关联的商品名称',
                             `cut_video_path` varchar(100) DEFAULT NULL COMMENT '本地视频路径',
                             `is_publish` int(10) DEFAULT 0 COMMENT '是否已发布',
                             `desc` varchar(500) DEFAULT NULL COMMENT '软文描述',
                             `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                             `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                             PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`task_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='蝉选切片剪切后信息';