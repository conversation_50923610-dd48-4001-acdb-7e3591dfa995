CREATE TABLE `sticker`  (
                            `id` int(10)  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `name` varchar(100) DEFAULT NULL  COMMENT '贴纸名称',
                            `path` varchar(200)  DEFAULT NULL COMMENT '地址',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            index(`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='贴纸';
alter table `sticker` add column `type` int(10) DEFAULT 1 COMMENT '贴纸类型：1，中间；2，上面或者下面';
alter table `sticker` add index (`type`);

INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('1', '/Volumes/共享文件夹/douyin/sticker/1.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('2', '/Volumes/共享文件夹/douyin/sticker/2.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('3', '/Volumes/共享文件夹/douyin/sticker/3.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('4', '/Volumes/共享文件夹/douyin/sticker/4.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('5', '/Volumes/共享文件夹/douyin/sticker/5.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('6', '/Volumes/共享文件夹/douyin/sticker/6.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('7', '/Volumes/共享文件夹/douyin/sticker/7.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('8', '/Volumes/共享文件夹/douyin/sticker/8.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('9', '/Volumes/共享文件夹/douyin/sticker/9.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('10', '/Volumes/共享文件夹/douyin/sticker/10.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('11', '/Volumes/共享文件夹/douyin/sticker/11.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('12', '/Volumes/共享文件夹/douyin/sticker/12.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('13', '/Volumes/共享文件夹/douyin/sticker/13.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('14', '/Volumes/共享文件夹/douyin/sticker/14.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('15', '/Volumes/共享文件夹/douyin/sticker/15.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('16', '/Volumes/共享文件夹/douyin/sticker/16.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('17', '/Volumes/共享文件夹/douyin/sticker/17.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('18', '/Volumes/共享文件夹/douyin/sticker/18.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('19', '/Volumes/共享文件夹/douyin/sticker/19.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('20', '/Volumes/共享文件夹/douyin/sticker/20.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('21', '/Volumes/共享文件夹/douyin/sticker/21.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('22', '/Volumes/共享文件夹/douyin/sticker/22.gif ', 2);
INSERT INTO `douyin`.`sticker` ( `name`, `path`,`type`) VALUES ('23', '/Volumes/共享文件夹/douyin/sticker/23.gif ', 2);