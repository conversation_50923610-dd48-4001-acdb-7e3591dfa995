CREATE TABLE `project`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '项目id',
                            `project_name` varchar(32)  NOT NULL COMMENT '项目名称',
                            `item_id` bigint unsigned  NOT NULL COMMENT '关联商品id',
                            `seller_id` bigint unsigned  NOT NULL COMMENT '关联直播达人id',
                            `des` varchar(128)  DEFAULT NULL COMMENT '项目描述',
                            `status` int(10) NOT NULL COMMENT '项目状态：1，已发布；0，草稿',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`item_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='项目表';
alter table `project` add column `cover` varchar(256)  DEFAULT NULL COMMENT '统一封面';
alter table `project` add column `item_type` int(10) DEFAULT 1 COMMENT '商品类型：1，导入商品；2，直播商品';
alter table `project` add column `creator_id` bigint unsigned  DEFAULT 1 COMMENT '创建者id';