CREATE TABLE `douyin_account`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `phone` varchar(20)  NOT NULL  COMMENT '手机号',
                            `seller_id` bigint unsigned  NOT NULL  COMMENT '绑定的直播达人id',
                            `douyin_name` varchar(128)  NOT NULL COMMENT '抖音账号名称',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`seller_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='抖音账号';
alter table `douyin_account` add column `cover_background` varchar(256)  NOT NULL COMMENT '封面背景';
alter table `douyin_account` add column `cover_container_path` varchar(256)  NOT NULL COMMENT '封面背景';
alter table `douyin_account` add column `code` varchar(64)  NOT NULL COMMENT 'code';
alter table `douyin_account` add column `access_token` varchar(500)  DEFAULT NULL COMMENT 'access_token';
alter table `douyin_account` add column `expires_in` datetime DEFAULT NULL COMMENT 'acccess_token超时时间';
alter table `douyin_account` add column `refresh_token` varchar(500)  DEFAULT NULL COMMENT 'refresh_token';
alter table `douyin_account` add column `refresh_expires_in` datetime DEFAULT NULL COMMENT 'refresh_acccess_token超时时间';
alter table `douyin_account` add column `open_id` varchar(64)  DEFAULT NULL COMMENT 'open_id';
alter table `douyin_account` add column `invite_token` varchar(128)  DEFAULT NULL COMMENT '邀请码';
alter table `douyin_account` add column `invite_done` TINYINT(1) DEFAULT NULL COMMENT '是否完成扫码';
alter table `douyin_account` add UNIQUE INDEX (`open_id`);
alter table `douyin_account` add column `type` int(10) DEFAULT 1 COMMENT '类型：1，个人号；2，团队号；3，自营号。';
alter table `douyin_account` add column `team_id` bigint unsigned   DEFAULT NULL COMMENT '团队id，type为2时有值';
alter table `douyin_account` add column `self_id` bigint unsigned   DEFAULT NULL COMMENT '自营id，type为3时有值';
alter table `douyin_account` add UNIQUE INDEX (`phone`);
alter table `douyin_account` add UNIQUE INDEX (`code`);
alter table `douyin_account` modify column `douyin_name`  varchar(128)  DEFAULT NULL COMMENT '抖音账号名称',






