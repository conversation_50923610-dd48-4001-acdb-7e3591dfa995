CREATE TABLE `works_track_elements`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '轨道元素id',
                            `track_id` bigint unsigned  NOT NULL COMMENT '轨道id',
                            `project_id` bigint unsigned  NOT NULL  COMMENT '项目id',
                            `item_id` bigint unsigned  NOT NULL  COMMENT '商品id',
                            `works_id` bigint unsigned  NOT NULL  COMMENT '作品id',
                            `track_type` varchar(32)  NOT NULL COMMENT '轨道类型',
                            `content` varchar(100)  NOT NULL COMMENT '轨道类型',
                            `duration` int(10) DEFAULT NULL COMMENT '元素持续时长，单位ms',
                            `start_ts` int(10) DEFAULT NULL COMMENT '开始时间，单位ms',
                            `end_ts` int(10) DEFAULT NULL COMMENT '结束时间，单位ms',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`track_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='作品轨道元素信息';