CREATE TABLE `works`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '视频作品id',
                            `project_id` bigint unsigned  NOT NULL  COMMENT '项目id',
                            `item_id` bigint unsigned  NOT NULL  COMMENT '商品id',
                            `seller_id` bigint unsigned  NOT NULL  COMMENT '直播达人id',
                            `duration` int(10) DEFAULT NULL COMMENT '视频片段时长，单位ms',
                            `special_effects_template_code` varchar(32) DEFAULT NULL COMMENT '去重特效模板code',
                            `is_composed` TINYINT(1)  DEFAULT 1 COMMENT '是否已经合成视频（弃用）',
                            `is_published` TINYINT(1)  DEFAULT 1 COMMENT '是否已经发布到抖音',
                            `video_path` varchar(128)  DEFAULT NULL COMMENT '合成的视频地址',
                            `music_id` int(10)  NOT NULL  COMMENT '背景音乐id',
                            `filter_id` int(10)  NOT NULL  COMMENT '滤镜id',
                            `sticker_id` int(10)  NOT NULL  COMMENT '贴纸id',
                            `speed_up` int(10)  NOT NULL  COMMENT '变速效果，10-20之间',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`project_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='视频作品表';
alter table works add column `works_name` varchar(60)  NOT NULL COMMENT '作品名称';
alter table works add column `works_desc` varchar(2000)  NOT NULL COMMENT '作品描述';
alter table works add column `works_cover` varchar(60)  NOT NULL COMMENT '作品封面';
alter table works add column `simple_item_name` varchar(50)  NOT NULL COMMENT '商品简称';
alter table works add column `works_tag` varchar(150)  NOT NULL COMMENT '作品标签';
alter table works add column `account_id` bigint unsigned  NOT NULL COMMENT '抖音账号id';
alter table works add column `phone` varchar(20)  NOT NULL COMMENT '手机号';
alter table works add column `published_time` datetime  DEFAULT NULL COMMENT '发布时间';
alter table works add column `item_features` varchar(150)  NOT NULL COMMENT '商品特性';
alter table works add column `item_tags` varchar(150)  NOT NULL COMMENT '商品标签';
alter table works add column `creator_id` bigint unsigned  DEFAULT 1 COMMENT '创建者id';
alter table works add column `font_id` int(10)  DEFAULT 1 COMMENT '字体id';
alter table works add column `keywords` varchar(255)  DEFAULT NULL COMMENT '关键词';
alter table works add column `is_desc_completed` tinyint(1)  DEFAULT 18 COMMENT '描述文案是否已生成';
alter table works add column `left_top_sticker_id` int(10)  DEFAULT 18 COMMENT '左上贴纸id';
alter table works add column `right_top_sticker_id` int(10)  DEFAULT 18 COMMENT '右上贴纸id';
alter table works add column `left_bottom_sticker_id` int(10)  DEFAULT 18 COMMENT '左下贴纸id';
alter table works add column `right_bottom_sticker_id` int(10)  DEFAULT 18 COMMENT '右下贴纸id';
alter table works add column `video_path_without_ass` varchar(255)  DEFAULT NULL COMMENT '不带字幕的视频路径';
alter table works add column `compose_status` int(10)   DEFAULT 0 COMMENT '合成状态';