CREATE TABLE `download_log`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '日志id',
                            `out_id` bigint unsigned  NOT NULL COMMENT '下载的视频或者封面id',
                            `download_type` int(10)  DEFAULT NULL COMMENT '下载类型',
                            `user_id` bigint unsigned  NOT NULL COMMENT '用户id',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            index (`create_time`, `download_type`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='下载日志';