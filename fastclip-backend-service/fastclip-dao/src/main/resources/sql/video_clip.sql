CREATE TABLE `video_clip`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `project_id` bigint unsigned  NOT NULL  COMMENT '项目id',
                            `start_subtitles_cut_id` bigint unsigned  NOT NULL  COMMENT '开始字幕切片id',
                            `end_subtitles_cut_id` bigint unsigned  NOT NULL  COMMENT '结束字幕切片id',
                            `start_subtitles_id` bigint unsigned  NOT NULL  COMMENT '开始原始字幕片段id',
                            `end_subtitles_id` bigint unsigned  NOT NULL  COMMENT '结束原始字幕片段id',
                            `subtitles` varchar(500)  DEFAULT NULL COMMENT '字幕内容',
                            `duration` int(10) DEFAULT NULL COMMENT '视频片段时长，单位ms',
                            `subtitles_cut_count` int(10) NOT NULL COMMENT '字幕片段个数',
                            `sort` int(10) DEFAULT NULL COMMENT '排序字段',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            index(`project_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='视频片段';