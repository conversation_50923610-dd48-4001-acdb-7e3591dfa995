CREATE TABLE `f_user`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '用户id',
                            `user_account` varchar(20)  NOT NULL COMMENT '用户账号',
                            `is_admin` TINYINT(1)  DEFAULT 1 COMMENT '是否是管理员',
                            `passwd` varchar(20)  NOT NULL COMMENT '密码',
                            `user_name` varchar(20)  NOT NULL COMMENT '用户名',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`user_name`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='用户表';
alter table `f_user` add column `is_self_cutter` TINYINT(1) DEFAULT 0 COMMENT '是否自营剪辑手';
alter table `f_user` add column `has_upload_previlege` TINYINT(1) DEFAULT 1 COMMENT '是否有上传权限';



