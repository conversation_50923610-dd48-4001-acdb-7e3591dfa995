CREATE TABLE `token`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT 'id',
                            `user_id` bigint  NOT NULL COMMENT '用户id',
                            `token` varchar(32)  NOT NULL COMMENT 'token',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE INDEX (`user_id`),
                            INDEX (`token`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='token表';
