CREATE TABLE `item_on_live`  (
                            `id` bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '商品id',
                            `item_name` varchar(150)  NOT NULL COMMENT '商品名称',
                            `seller_id` bigint unsigned  NOT NULL COMMENT '关联直播达人',
                            `create_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                            `update_time` datetime  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX (`seller_id`),
                            INDEX (`item_name`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact comment='直播商品表';