package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class VideoMaterial {
    private Long id;

    private Long sellerId;

    private Integer duration;

    private String path;

    private Integer size;

    private Boolean isSubtitlesDone;

    private Integer subtitlesBpTs;

    private Date createTime;

    private Date updateTime;

    private String videoName;

    private Date startDate;

    private Integer startTime;

    private Integer videoType;

    private String liveRoomId;

    private Integer status;

    private Integer latestSliceMergedId;

    private Integer sort;

    private Integer combineStatus;

    private Integer startScene;

    private Boolean startSceneFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path == null ? null : path.trim();
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Boolean getIsSubtitlesDone() {
        return isSubtitlesDone;
    }

    public void setIsSubtitlesDone(Boolean isSubtitlesDone) {
        this.isSubtitlesDone = isSubtitlesDone;
    }

    public Integer getSubtitlesBpTs() {
        return subtitlesBpTs;
    }

    public void setSubtitlesBpTs(Integer subtitlesBpTs) {
        this.subtitlesBpTs = subtitlesBpTs;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getVideoName() {
        return videoName;
    }

    public void setVideoName(String videoName) {
        this.videoName = videoName == null ? null : videoName.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getVideoType() {
        return videoType;
    }

    public void setVideoType(Integer videoType) {
        this.videoType = videoType;
    }

    public String getLiveRoomId() {
        return liveRoomId;
    }

    public void setLiveRoomId(String liveRoomId) {
        this.liveRoomId = liveRoomId == null ? null : liveRoomId.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getLatestSliceMergedId() {
        return latestSliceMergedId;
    }

    public void setLatestSliceMergedId(Integer latestSliceMergedId) {
        this.latestSliceMergedId = latestSliceMergedId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getCombineStatus() {
        return combineStatus;
    }

    public void setCombineStatus(Integer combineStatus) {
        this.combineStatus = combineStatus;
    }

    public Integer getStartScene() {
        return startScene;
    }

    public void setStartScene(Integer startScene) {
        this.startScene = startScene;
    }

    public Boolean getStartSceneFlag() {
        return startSceneFlag;
    }

    public void setStartSceneFlag(Boolean startSceneFlag) {
        this.startSceneFlag = startSceneFlag;
    }
}