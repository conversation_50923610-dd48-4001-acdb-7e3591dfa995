package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.dao.model.dataobject.Item;
import com.fastclip.dao.model.dataobject.Subtitles;
import com.fastclip.dao.model.dataobject.SubtitlesCut;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class SubtitlesCutUtils {
    public static SubtitlesCutDTO do2DTO(SubtitlesCut subtitlesCut) {
        SubtitlesCutDTO subtitlesCutDTO = new SubtitlesCutDTO();
        BeanUtils.copyProperties(subtitlesCut, subtitlesCutDTO);
        return subtitlesCutDTO;
    }

    public static SubtitlesCut dto2DO(SubtitlesCutDTO subtitlesCutDTO) {
        SubtitlesCut subtitlesCut = new SubtitlesCut();
        BeanUtils.copyProperties(subtitlesCutDTO, subtitlesCut);
        return subtitlesCut;
    }

    public static List<SubtitlesCutDTO> do2DTO(List<SubtitlesCut> subtitles) {
        if (CollectionUtils.isEmpty(subtitles)) {
            return new ArrayList<>();
        }
        return subtitles.stream().map(SubtitlesCutUtils::do2DTO).collect(Collectors.toList());
    }
}
