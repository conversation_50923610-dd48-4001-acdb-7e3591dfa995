package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class VideoMaterialSlice {
    private Long id;

    private Long videoId;

    private Integer duration;

    private String path;

    private Integer size;

    private Integer number;

    private Integer startTs;

    private Boolean isSubtitlesDone;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path == null ? null : path.trim();
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getStartTs() {
        return startTs;
    }

    public void setStartTs(Integer startTs) {
        this.startTs = startTs;
    }

    public Boolean getIsSubtitlesDone() {
        return isSubtitlesDone;
    }

    public void setIsSubtitlesDone(Boolean isSubtitlesDone) {
        this.isSubtitlesDone = isSubtitlesDone;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}