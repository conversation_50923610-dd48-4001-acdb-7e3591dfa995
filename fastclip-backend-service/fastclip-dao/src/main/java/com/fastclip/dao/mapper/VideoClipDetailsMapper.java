package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.VideoClipDetails;
import com.fastclip.dao.model.dataobject.VideoClipDetailsExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface VideoClipDetailsMapper {
    long countByExample(VideoClipDetailsExample example);

    int deleteByExample(VideoClipDetailsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(VideoClipDetails record);

    int insertSelective(VideoClipDetails record);

    List<VideoClipDetails> selectByExampleWithRowbounds(VideoClipDetailsExample example, RowBounds rowBounds);

    List<VideoClipDetails> selectByExample(VideoClipDetailsExample example);

    VideoClipDetails selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") VideoClipDetails record, @Param("example") VideoClipDetailsExample example);

    int updateByExample(@Param("record") VideoClipDetails record, @Param("example") VideoClipDetailsExample example);

    int updateByPrimaryKeySelective(VideoClipDetails record);

    int updateByPrimaryKey(VideoClipDetails record);
}