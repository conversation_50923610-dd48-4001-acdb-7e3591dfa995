package com.fastclip.dao.model.dataobject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class QRCodeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public QRCodeExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdIsNull() {
            addCriterion("douyin_account_id is null");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdIsNotNull() {
            addCriterion("douyin_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdEqualTo(Long value) {
            addCriterion("douyin_account_id =", value, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdNotEqualTo(Long value) {
            addCriterion("douyin_account_id <>", value, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdGreaterThan(Long value) {
            addCriterion("douyin_account_id >", value, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("douyin_account_id >=", value, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdLessThan(Long value) {
            addCriterion("douyin_account_id <", value, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("douyin_account_id <=", value, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdIn(List<Long> values) {
            addCriterion("douyin_account_id in", values, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdNotIn(List<Long> values) {
            addCriterion("douyin_account_id not in", values, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdBetween(Long value1, Long value2) {
            addCriterion("douyin_account_id between", value1, value2, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andDouyinAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("douyin_account_id not between", value1, value2, "douyinAccountId");
            return (Criteria) this;
        }

        public Criteria andCaptchaIsNull() {
            addCriterion("captcha is null");
            return (Criteria) this;
        }

        public Criteria andCaptchaIsNotNull() {
            addCriterion("captcha is not null");
            return (Criteria) this;
        }

        public Criteria andCaptchaEqualTo(String value) {
            addCriterion("captcha =", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaNotEqualTo(String value) {
            addCriterion("captcha <>", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaGreaterThan(String value) {
            addCriterion("captcha >", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaGreaterThanOrEqualTo(String value) {
            addCriterion("captcha >=", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaLessThan(String value) {
            addCriterion("captcha <", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaLessThanOrEqualTo(String value) {
            addCriterion("captcha <=", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaLike(String value) {
            addCriterion("captcha like", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaNotLike(String value) {
            addCriterion("captcha not like", value, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaIn(List<String> values) {
            addCriterion("captcha in", values, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaNotIn(List<String> values) {
            addCriterion("captcha not in", values, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaBetween(String value1, String value2) {
            addCriterion("captcha between", value1, value2, "captcha");
            return (Criteria) this;
        }

        public Criteria andCaptchaNotBetween(String value1, String value2) {
            addCriterion("captcha not between", value1, value2, "captcha");
            return (Criteria) this;
        }

        public Criteria andDescUrlIsNull() {
            addCriterion("desc_url is null");
            return (Criteria) this;
        }

        public Criteria andDescUrlIsNotNull() {
            addCriterion("desc_url is not null");
            return (Criteria) this;
        }

        public Criteria andDescUrlEqualTo(String value) {
            addCriterion("desc_url =", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlNotEqualTo(String value) {
            addCriterion("desc_url <>", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlGreaterThan(String value) {
            addCriterion("desc_url >", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlGreaterThanOrEqualTo(String value) {
            addCriterion("desc_url >=", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlLessThan(String value) {
            addCriterion("desc_url <", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlLessThanOrEqualTo(String value) {
            addCriterion("desc_url <=", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlLike(String value) {
            addCriterion("desc_url like", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlNotLike(String value) {
            addCriterion("desc_url not like", value, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlIn(List<String> values) {
            addCriterion("desc_url in", values, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlNotIn(List<String> values) {
            addCriterion("desc_url not in", values, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlBetween(String value1, String value2) {
            addCriterion("desc_url between", value1, value2, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescUrlNotBetween(String value1, String value2) {
            addCriterion("desc_url not between", value1, value2, "descUrl");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andErrorCodeIsNull() {
            addCriterion("error_code is null");
            return (Criteria) this;
        }

        public Criteria andErrorCodeIsNotNull() {
            addCriterion("error_code is not null");
            return (Criteria) this;
        }

        public Criteria andErrorCodeEqualTo(Integer value) {
            addCriterion("error_code =", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeNotEqualTo(Integer value) {
            addCriterion("error_code <>", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeGreaterThan(Integer value) {
            addCriterion("error_code >", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("error_code >=", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeLessThan(Integer value) {
            addCriterion("error_code <", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeLessThanOrEqualTo(Integer value) {
            addCriterion("error_code <=", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeIn(List<Integer> values) {
            addCriterion("error_code in", values, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeNotIn(List<Integer> values) {
            addCriterion("error_code not in", values, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeBetween(Integer value1, Integer value2) {
            addCriterion("error_code between", value1, value2, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("error_code not between", value1, value2, "errorCode");
            return (Criteria) this;
        }

        public Criteria andIsFrontierIsNull() {
            addCriterion("is_frontier is null");
            return (Criteria) this;
        }

        public Criteria andIsFrontierIsNotNull() {
            addCriterion("is_frontier is not null");
            return (Criteria) this;
        }

        public Criteria andIsFrontierEqualTo(Boolean value) {
            addCriterion("is_frontier =", value, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierNotEqualTo(Boolean value) {
            addCriterion("is_frontier <>", value, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierGreaterThan(Boolean value) {
            addCriterion("is_frontier >", value, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_frontier >=", value, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierLessThan(Boolean value) {
            addCriterion("is_frontier <", value, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierLessThanOrEqualTo(Boolean value) {
            addCriterion("is_frontier <=", value, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierIn(List<Boolean> values) {
            addCriterion("is_frontier in", values, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierNotIn(List<Boolean> values) {
            addCriterion("is_frontier not in", values, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierBetween(Boolean value1, Boolean value2) {
            addCriterion("is_frontier between", value1, value2, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andIsFrontierNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_frontier not between", value1, value2, "isFrontier");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlIsNull() {
            addCriterion("qrcode_index_url is null");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlIsNotNull() {
            addCriterion("qrcode_index_url is not null");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlEqualTo(String value) {
            addCriterion("qrcode_index_url =", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlNotEqualTo(String value) {
            addCriterion("qrcode_index_url <>", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlGreaterThan(String value) {
            addCriterion("qrcode_index_url >", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlGreaterThanOrEqualTo(String value) {
            addCriterion("qrcode_index_url >=", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlLessThan(String value) {
            addCriterion("qrcode_index_url <", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlLessThanOrEqualTo(String value) {
            addCriterion("qrcode_index_url <=", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlLike(String value) {
            addCriterion("qrcode_index_url like", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlNotLike(String value) {
            addCriterion("qrcode_index_url not like", value, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlIn(List<String> values) {
            addCriterion("qrcode_index_url in", values, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlNotIn(List<String> values) {
            addCriterion("qrcode_index_url not in", values, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlBetween(String value1, String value2) {
            addCriterion("qrcode_index_url between", value1, value2, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andQrcodeIndexUrlNotBetween(String value1, String value2) {
            addCriterion("qrcode_index_url not between", value1, value2, "qrcodeIndexUrl");
            return (Criteria) this;
        }

        public Criteria andTokenIsNull() {
            addCriterion("token is null");
            return (Criteria) this;
        }

        public Criteria andTokenIsNotNull() {
            addCriterion("token is not null");
            return (Criteria) this;
        }

        public Criteria andTokenEqualTo(String value) {
            addCriterion("token =", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotEqualTo(String value) {
            addCriterion("token <>", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenGreaterThan(String value) {
            addCriterion("token >", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenGreaterThanOrEqualTo(String value) {
            addCriterion("token >=", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLessThan(String value) {
            addCriterion("token <", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLessThanOrEqualTo(String value) {
            addCriterion("token <=", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLike(String value) {
            addCriterion("token like", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotLike(String value) {
            addCriterion("token not like", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenIn(List<String> values) {
            addCriterion("token in", values, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotIn(List<String> values) {
            addCriterion("token not in", values, "token");
            return (Criteria) this;
        }

        public Criteria andTokenBetween(String value1, String value2) {
            addCriterion("token between", value1, value2, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotBetween(String value1, String value2) {
            addCriterion("token not between", value1, value2, "token");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}