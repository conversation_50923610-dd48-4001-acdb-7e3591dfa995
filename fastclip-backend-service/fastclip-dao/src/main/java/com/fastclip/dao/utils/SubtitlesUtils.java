package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.SubtitlesDTO;
import com.fastclip.dao.model.dataobject.Subtitles;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class SubtitlesUtils {
    public static SubtitlesDTO do2DTO(Subtitles subtitles) {
        if(subtitles == null) {
            return null;
        }
        SubtitlesDTO subtitlesDTO = new SubtitlesDTO();
        BeanUtils.copyProperties(subtitles, subtitlesDTO);
        return subtitlesDTO;
    }

    public static List<SubtitlesDTO> do2DTO(List<Subtitles> subtitles) {
        if (CollectionUtils.isEmpty(subtitles)) {
            return new ArrayList<>();
        }
        return subtitles.stream().map(SubtitlesUtils::do2DTO).collect(Collectors.toList());
    }

    public static Subtitles dto2DO(SubtitlesDTO subtitlesDTO) {
        Subtitles subtitles  = new Subtitles();
        BeanUtils.copyProperties(subtitlesDTO, subtitles);
        return subtitles;
    }
}
