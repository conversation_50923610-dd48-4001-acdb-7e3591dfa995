package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class VideoClipDetails {
    private Long id;

    private Long projectId;

    private Long videoClipId;

    private Long subtitlesCutId;

    private Integer sort;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getVideoClipId() {
        return videoClipId;
    }

    public void setVideoClipId(Long videoClipId) {
        this.videoClipId = videoClipId;
    }

    public Long getSubtitlesCutId() {
        return subtitlesCutId;
    }

    public void setSubtitlesCutId(Long subtitlesCutId) {
        this.subtitlesCutId = subtitlesCutId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}