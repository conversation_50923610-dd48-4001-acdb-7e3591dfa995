package com.fastclip.dao.utils;

import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.model.dto.Image;
import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.ProjectDTO;
import com.fastclip.dao.model.dataobject.Item;
import com.fastclip.dao.model.dataobject.ItemExt;
import com.fastclip.dao.model.dataobject.ItemOnLive;
import com.fastclip.dao.model.dataobject.Project;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ItemUtils {
    public static ItemDTO do2DTO(Item item) {
        if(item == null) {
            return null;
        }
        ItemDTO itemDTO = new ItemDTO();
        BeanUtils.copyProperties(item, itemDTO);
        itemDTO.setItemType(ItemTypeEnum.Import.getValue());
        return itemDTO;
    }

    public static ItemDTO extDo2DTO(ItemExt itemExt) {
        ItemDTO itemDTO = new ItemDTO();
        BeanUtils.copyProperties(itemExt, itemDTO);
        itemDTO.setItemType(ItemTypeEnum.Import.getValue());
        return itemDTO;
    }

    public static Item dto2DO(ItemDTO itemDTO) {
        Item item = new Item();
        BeanUtils.copyProperties(itemDTO, item);
        return item;
    }

    public static List<ItemDTO> do2DTOs(List<Item> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        return items.stream().map(ItemUtils::do2DTO).collect(Collectors.toList());
    }

    public static List<ItemDTO> extDo2DTOs(List<ItemExt> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        return items.stream().map(ItemUtils::extDo2DTO).collect(Collectors.toList());
    }

    public static ItemDTO liveItemDo2Dto(ItemOnLive item) {
        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setItemType(ItemTypeEnum.Live.getValue());
        itemDTO.setId(item.getId());
        itemDTO.setItemName(item.getItemName());
        itemDTO.setCreateTime(item.getCreateTime());
        itemDTO.setUpdateTime(item.getUpdateTime());
        itemDTO.setSellerId(item.getSellerId());
        return itemDTO;
    }

    public static List<ItemDTO> liveItemDo2Dtos(List<ItemOnLive> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        return items.stream().map(ItemUtils::liveItemDo2Dto).collect(Collectors.toList());
    }
}
