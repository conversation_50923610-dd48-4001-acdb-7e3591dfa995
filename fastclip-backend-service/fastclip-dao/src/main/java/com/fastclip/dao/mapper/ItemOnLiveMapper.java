package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.ItemOnLive;
import com.fastclip.dao.model.dataobject.ItemOnLiveExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface ItemOnLiveMapper {
    long countByExample(ItemOnLiveExample example);

    int deleteByExample(ItemOnLiveExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ItemOnLive record);

    int insertSelective(ItemOnLive record);

    List<ItemOnLive> selectByExampleWithRowbounds(ItemOnLiveExample example, RowBounds rowBounds);

    List<ItemOnLive> selectByExample(ItemOnLiveExample example);

    ItemOnLive selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ItemOnLive record, @Param("example") ItemOnLiveExample example);

    int updateByExample(@Param("record") ItemOnLive record, @Param("example") ItemOnLiveExample example);

    int updateByPrimaryKeySelective(ItemOnLive record);

    int updateByPrimaryKey(ItemOnLive record);
}