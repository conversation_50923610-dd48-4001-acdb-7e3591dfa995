package com.fastclip.dao.model.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class ProjectExt {
    private Long id;

    private String projectName;

    private Long itemId;

    private Long sellerId;

    private String des;

    private Integer status;

    private Date createTime;

    private Date updateTime;

    private String itemName;

    private String sellerName;

    private Integer worksCount;

    private Integer maxMarterialDate;

    private String shareUrl;

    private Date maxMaterialDate;

    private Integer itemType;

    private Long creatorId;
}