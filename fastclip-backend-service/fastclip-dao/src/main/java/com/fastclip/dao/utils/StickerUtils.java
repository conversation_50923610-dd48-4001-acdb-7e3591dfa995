package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.MusicDTO;
import com.fastclip.common.model.dto.StickerDTO;
import com.fastclip.dao.model.dataobject.Music;
import com.fastclip.dao.model.dataobject.Sticker;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class StickerUtils {

    public static StickerDTO do2DTO(Sticker sticker) {
        StickerDTO stickerDTO = new StickerDTO();
        BeanUtils.copyProperties(sticker, stickerDTO);
        return stickerDTO;
    }

    public static List<StickerDTO> do2DTOs(List<Sticker> stickers) {
        if(CollectionUtils.isEmpty(stickers)) {
            return new ArrayList<>();
        }
        return stickers.stream().map(StickerUtils::do2DTO).collect(Collectors.toList());
    }
}
