package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.ProjectDTO;
import com.fastclip.common.model.dto.SellerDTO;
import com.fastclip.dao.model.dataobject.Project;
import com.fastclip.dao.model.dataobject.Seller;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class SellerUtils {
    public static SellerDTO do2DTO(Seller seller) {
        if(seller == null) {
            return null;
        }
        SellerDTO sellerDTO = new SellerDTO();
        BeanUtils.copyProperties(seller, sellerDTO);
        sellerDTO.setSellerId(seller.getId());
        return sellerDTO;
    }

    public static Seller dto2DO(SellerDTO sellerDTO) {
        Seller seller = new Seller();
        BeanUtils.copyProperties(sellerDTO, seller);
        seller.setId(sellerDTO.getSellerId());
        return seller;
    }

    public static List<SellerDTO> do2DTO(List<Seller> sellers) {
        if (CollectionUtils.isEmpty(sellers)) {
            return new ArrayList<>();
        }
        return sellers.stream().map(SellerUtils::do2DTO).collect(Collectors.toList());
    }
}
