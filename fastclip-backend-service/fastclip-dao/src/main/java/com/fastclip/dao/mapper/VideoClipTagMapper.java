package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.VideoClipTag;
import com.fastclip.dao.model.dataobject.VideoClipTagExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface VideoClipTagMapper {
    long countByExample(VideoClipTagExample example);

    int deleteByExample(VideoClipTagExample example);

    int deleteByPrimaryKey(Long id);

    int insert(VideoClipTag record);

    int insertSelective(VideoClipTag record);

    List<VideoClipTag> selectByExampleWithRowbounds(VideoClipTagExample example, RowBounds rowBounds);

    List<VideoClipTag> selectByExample(VideoClipTagExample example);

    VideoClipTag selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") VideoClipTag record, @Param("example") VideoClipTagExample example);

    int updateByExample(@Param("record") VideoClipTag record, @Param("example") VideoClipTagExample example);

    int updateByPrimaryKeySelective(VideoClipTag record);

    int updateByPrimaryKey(VideoClipTag record);
}