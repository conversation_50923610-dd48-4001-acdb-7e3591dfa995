package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.Token;
import com.fastclip.dao.model.dataobject.TokenExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface TokenMapper {
    long countByExample(TokenExample example);

    int deleteByExample(TokenExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Token record);

    int insertSelective(Token record);

    List<Token> selectByExampleWithRowbounds(TokenExample example, RowBounds rowBounds);

    List<Token> selectByExample(TokenExample example);

    Token selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") Token record, @Param("example") TokenExample example);

    int updateByExample(@Param("record") Token record, @Param("example") TokenExample example);

    int updateByPrimaryKeySelective(Token record);

    int updateByPrimaryKey(Token record);
}