package com.fastclip.dao.model.dataobject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DouyinAccountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DouyinAccountExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNull() {
            addCriterion("seller_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNotNull() {
            addCriterion("seller_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerIdEqualTo(Long value) {
            addCriterion("seller_id =", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotEqualTo(Long value) {
            addCriterion("seller_id <>", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThan(Long value) {
            addCriterion("seller_id >", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("seller_id >=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThan(Long value) {
            addCriterion("seller_id <", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThanOrEqualTo(Long value) {
            addCriterion("seller_id <=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdIn(List<Long> values) {
            addCriterion("seller_id in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotIn(List<Long> values) {
            addCriterion("seller_id not in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdBetween(Long value1, Long value2) {
            addCriterion("seller_id between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotBetween(Long value1, Long value2) {
            addCriterion("seller_id not between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andDouyinNameIsNull() {
            addCriterion("douyin_name is null");
            return (Criteria) this;
        }

        public Criteria andDouyinNameIsNotNull() {
            addCriterion("douyin_name is not null");
            return (Criteria) this;
        }

        public Criteria andDouyinNameEqualTo(String value) {
            addCriterion("douyin_name =", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameNotEqualTo(String value) {
            addCriterion("douyin_name <>", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameGreaterThan(String value) {
            addCriterion("douyin_name >", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameGreaterThanOrEqualTo(String value) {
            addCriterion("douyin_name >=", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameLessThan(String value) {
            addCriterion("douyin_name <", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameLessThanOrEqualTo(String value) {
            addCriterion("douyin_name <=", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameLike(String value) {
            addCriterion("douyin_name like", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameNotLike(String value) {
            addCriterion("douyin_name not like", value, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameIn(List<String> values) {
            addCriterion("douyin_name in", values, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameNotIn(List<String> values) {
            addCriterion("douyin_name not in", values, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameBetween(String value1, String value2) {
            addCriterion("douyin_name between", value1, value2, "douyinName");
            return (Criteria) this;
        }

        public Criteria andDouyinNameNotBetween(String value1, String value2) {
            addCriterion("douyin_name not between", value1, value2, "douyinName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathIsNull() {
            addCriterion("cover_container_path is null");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathIsNotNull() {
            addCriterion("cover_container_path is not null");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathEqualTo(String value) {
            addCriterion("cover_container_path =", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathNotEqualTo(String value) {
            addCriterion("cover_container_path <>", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathGreaterThan(String value) {
            addCriterion("cover_container_path >", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathGreaterThanOrEqualTo(String value) {
            addCriterion("cover_container_path >=", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathLessThan(String value) {
            addCriterion("cover_container_path <", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathLessThanOrEqualTo(String value) {
            addCriterion("cover_container_path <=", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathLike(String value) {
            addCriterion("cover_container_path like", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathNotLike(String value) {
            addCriterion("cover_container_path not like", value, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathIn(List<String> values) {
            addCriterion("cover_container_path in", values, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathNotIn(List<String> values) {
            addCriterion("cover_container_path not in", values, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathBetween(String value1, String value2) {
            addCriterion("cover_container_path between", value1, value2, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverContainerPathNotBetween(String value1, String value2) {
            addCriterion("cover_container_path not between", value1, value2, "coverContainerPath");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundIsNull() {
            addCriterion("cover_background is null");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundIsNotNull() {
            addCriterion("cover_background is not null");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundEqualTo(String value) {
            addCriterion("cover_background =", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundNotEqualTo(String value) {
            addCriterion("cover_background <>", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundGreaterThan(String value) {
            addCriterion("cover_background >", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundGreaterThanOrEqualTo(String value) {
            addCriterion("cover_background >=", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundLessThan(String value) {
            addCriterion("cover_background <", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundLessThanOrEqualTo(String value) {
            addCriterion("cover_background <=", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundLike(String value) {
            addCriterion("cover_background like", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundNotLike(String value) {
            addCriterion("cover_background not like", value, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundIn(List<String> values) {
            addCriterion("cover_background in", values, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundNotIn(List<String> values) {
            addCriterion("cover_background not in", values, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundBetween(String value1, String value2) {
            addCriterion("cover_background between", value1, value2, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCoverBackgroundNotBetween(String value1, String value2) {
            addCriterion("cover_background not between", value1, value2, "coverBackground");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andAccessTokenIsNull() {
            addCriterion("access_token is null");
            return (Criteria) this;
        }

        public Criteria andAccessTokenIsNotNull() {
            addCriterion("access_token is not null");
            return (Criteria) this;
        }

        public Criteria andAccessTokenEqualTo(String value) {
            addCriterion("access_token =", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenNotEqualTo(String value) {
            addCriterion("access_token <>", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenGreaterThan(String value) {
            addCriterion("access_token >", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenGreaterThanOrEqualTo(String value) {
            addCriterion("access_token >=", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenLessThan(String value) {
            addCriterion("access_token <", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenLessThanOrEqualTo(String value) {
            addCriterion("access_token <=", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenLike(String value) {
            addCriterion("access_token like", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenNotLike(String value) {
            addCriterion("access_token not like", value, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenIn(List<String> values) {
            addCriterion("access_token in", values, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenNotIn(List<String> values) {
            addCriterion("access_token not in", values, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenBetween(String value1, String value2) {
            addCriterion("access_token between", value1, value2, "accessToken");
            return (Criteria) this;
        }

        public Criteria andAccessTokenNotBetween(String value1, String value2) {
            addCriterion("access_token not between", value1, value2, "accessToken");
            return (Criteria) this;
        }

        public Criteria andExpireTimeIsNull() {
            addCriterion("expire_time is null");
            return (Criteria) this;
        }

        public Criteria andExpireTimeIsNotNull() {
            addCriterion("expire_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpireTimeEqualTo(Date value) {
            addCriterion("expire_time =", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeNotEqualTo(Date value) {
            addCriterion("expire_time <>", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeGreaterThan(Date value) {
            addCriterion("expire_time >", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expire_time >=", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeLessThan(Date value) {
            addCriterion("expire_time <", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeLessThanOrEqualTo(Date value) {
            addCriterion("expire_time <=", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeIn(List<Date> values) {
            addCriterion("expire_time in", values, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeNotIn(List<Date> values) {
            addCriterion("expire_time not in", values, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeBetween(Date value1, Date value2) {
            addCriterion("expire_time between", value1, value2, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeNotBetween(Date value1, Date value2) {
            addCriterion("expire_time not between", value1, value2, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpiresInIsNull() {
            addCriterion("expires_in is null");
            return (Criteria) this;
        }

        public Criteria andExpiresInIsNotNull() {
            addCriterion("expires_in is not null");
            return (Criteria) this;
        }

        public Criteria andExpiresInEqualTo(Date value) {
            addCriterion("expires_in =", value, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInNotEqualTo(Date value) {
            addCriterion("expires_in <>", value, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInGreaterThan(Date value) {
            addCriterion("expires_in >", value, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInGreaterThanOrEqualTo(Date value) {
            addCriterion("expires_in >=", value, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInLessThan(Date value) {
            addCriterion("expires_in <", value, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInLessThanOrEqualTo(Date value) {
            addCriterion("expires_in <=", value, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInIn(List<Date> values) {
            addCriterion("expires_in in", values, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInNotIn(List<Date> values) {
            addCriterion("expires_in not in", values, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInBetween(Date value1, Date value2) {
            addCriterion("expires_in between", value1, value2, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andExpiresInNotBetween(Date value1, Date value2) {
            addCriterion("expires_in not between", value1, value2, "expiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenIsNull() {
            addCriterion("refresh_token is null");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenIsNotNull() {
            addCriterion("refresh_token is not null");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenEqualTo(String value) {
            addCriterion("refresh_token =", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenNotEqualTo(String value) {
            addCriterion("refresh_token <>", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenGreaterThan(String value) {
            addCriterion("refresh_token >", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenGreaterThanOrEqualTo(String value) {
            addCriterion("refresh_token >=", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenLessThan(String value) {
            addCriterion("refresh_token <", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenLessThanOrEqualTo(String value) {
            addCriterion("refresh_token <=", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenLike(String value) {
            addCriterion("refresh_token like", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenNotLike(String value) {
            addCriterion("refresh_token not like", value, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenIn(List<String> values) {
            addCriterion("refresh_token in", values, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenNotIn(List<String> values) {
            addCriterion("refresh_token not in", values, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenBetween(String value1, String value2) {
            addCriterion("refresh_token between", value1, value2, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshTokenNotBetween(String value1, String value2) {
            addCriterion("refresh_token not between", value1, value2, "refreshToken");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInIsNull() {
            addCriterion("refresh_expires_in is null");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInIsNotNull() {
            addCriterion("refresh_expires_in is not null");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInEqualTo(Date value) {
            addCriterion("refresh_expires_in =", value, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInNotEqualTo(Date value) {
            addCriterion("refresh_expires_in <>", value, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInGreaterThan(Date value) {
            addCriterion("refresh_expires_in >", value, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInGreaterThanOrEqualTo(Date value) {
            addCriterion("refresh_expires_in >=", value, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInLessThan(Date value) {
            addCriterion("refresh_expires_in <", value, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInLessThanOrEqualTo(Date value) {
            addCriterion("refresh_expires_in <=", value, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInIn(List<Date> values) {
            addCriterion("refresh_expires_in in", values, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInNotIn(List<Date> values) {
            addCriterion("refresh_expires_in not in", values, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInBetween(Date value1, Date value2) {
            addCriterion("refresh_expires_in between", value1, value2, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andRefreshExpiresInNotBetween(Date value1, Date value2) {
            addCriterion("refresh_expires_in not between", value1, value2, "refreshExpiresIn");
            return (Criteria) this;
        }

        public Criteria andOpenIdIsNull() {
            addCriterion("open_id is null");
            return (Criteria) this;
        }

        public Criteria andOpenIdIsNotNull() {
            addCriterion("open_id is not null");
            return (Criteria) this;
        }

        public Criteria andOpenIdEqualTo(String value) {
            addCriterion("open_id =", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdNotEqualTo(String value) {
            addCriterion("open_id <>", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdGreaterThan(String value) {
            addCriterion("open_id >", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdGreaterThanOrEqualTo(String value) {
            addCriterion("open_id >=", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdLessThan(String value) {
            addCriterion("open_id <", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdLessThanOrEqualTo(String value) {
            addCriterion("open_id <=", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdLike(String value) {
            addCriterion("open_id like", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdNotLike(String value) {
            addCriterion("open_id not like", value, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdIn(List<String> values) {
            addCriterion("open_id in", values, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdNotIn(List<String> values) {
            addCriterion("open_id not in", values, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdBetween(String value1, String value2) {
            addCriterion("open_id between", value1, value2, "openId");
            return (Criteria) this;
        }

        public Criteria andOpenIdNotBetween(String value1, String value2) {
            addCriterion("open_id not between", value1, value2, "openId");
            return (Criteria) this;
        }

        public Criteria andInviteTokenIsNull() {
            addCriterion("invite_token is null");
            return (Criteria) this;
        }

        public Criteria andInviteTokenIsNotNull() {
            addCriterion("invite_token is not null");
            return (Criteria) this;
        }

        public Criteria andInviteTokenEqualTo(String value) {
            addCriterion("invite_token =", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenNotEqualTo(String value) {
            addCriterion("invite_token <>", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenGreaterThan(String value) {
            addCriterion("invite_token >", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenGreaterThanOrEqualTo(String value) {
            addCriterion("invite_token >=", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenLessThan(String value) {
            addCriterion("invite_token <", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenLessThanOrEqualTo(String value) {
            addCriterion("invite_token <=", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenLike(String value) {
            addCriterion("invite_token like", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenNotLike(String value) {
            addCriterion("invite_token not like", value, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenIn(List<String> values) {
            addCriterion("invite_token in", values, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenNotIn(List<String> values) {
            addCriterion("invite_token not in", values, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenBetween(String value1, String value2) {
            addCriterion("invite_token between", value1, value2, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteTokenNotBetween(String value1, String value2) {
            addCriterion("invite_token not between", value1, value2, "inviteToken");
            return (Criteria) this;
        }

        public Criteria andInviteDoneIsNull() {
            addCriterion("invite_done is null");
            return (Criteria) this;
        }

        public Criteria andInviteDoneIsNotNull() {
            addCriterion("invite_done is not null");
            return (Criteria) this;
        }

        public Criteria andInviteDoneEqualTo(Boolean value) {
            addCriterion("invite_done =", value, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneNotEqualTo(Boolean value) {
            addCriterion("invite_done <>", value, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneGreaterThan(Boolean value) {
            addCriterion("invite_done >", value, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneGreaterThanOrEqualTo(Boolean value) {
            addCriterion("invite_done >=", value, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneLessThan(Boolean value) {
            addCriterion("invite_done <", value, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneLessThanOrEqualTo(Boolean value) {
            addCriterion("invite_done <=", value, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneIn(List<Boolean> values) {
            addCriterion("invite_done in", values, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneNotIn(List<Boolean> values) {
            addCriterion("invite_done not in", values, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneBetween(Boolean value1, Boolean value2) {
            addCriterion("invite_done between", value1, value2, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andInviteDoneNotBetween(Boolean value1, Boolean value2) {
            addCriterion("invite_done not between", value1, value2, "inviteDone");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTeamIdIsNull() {
            addCriterion("team_id is null");
            return (Criteria) this;
        }

        public Criteria andTeamIdIsNotNull() {
            addCriterion("team_id is not null");
            return (Criteria) this;
        }

        public Criteria andTeamIdEqualTo(Long value) {
            addCriterion("team_id =", value, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdNotEqualTo(Long value) {
            addCriterion("team_id <>", value, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdGreaterThan(Long value) {
            addCriterion("team_id >", value, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdGreaterThanOrEqualTo(Long value) {
            addCriterion("team_id >=", value, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdLessThan(Long value) {
            addCriterion("team_id <", value, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdLessThanOrEqualTo(Long value) {
            addCriterion("team_id <=", value, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdIn(List<Long> values) {
            addCriterion("team_id in", values, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdNotIn(List<Long> values) {
            addCriterion("team_id not in", values, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdBetween(Long value1, Long value2) {
            addCriterion("team_id between", value1, value2, "teamId");
            return (Criteria) this;
        }

        public Criteria andTeamIdNotBetween(Long value1, Long value2) {
            addCriterion("team_id not between", value1, value2, "teamId");
            return (Criteria) this;
        }

        public Criteria andSelfIdIsNull() {
            addCriterion("self_id is null");
            return (Criteria) this;
        }

        public Criteria andSelfIdIsNotNull() {
            addCriterion("self_id is not null");
            return (Criteria) this;
        }

        public Criteria andSelfIdEqualTo(Long value) {
            addCriterion("self_id =", value, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdNotEqualTo(Long value) {
            addCriterion("self_id <>", value, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdGreaterThan(Long value) {
            addCriterion("self_id >", value, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdGreaterThanOrEqualTo(Long value) {
            addCriterion("self_id >=", value, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdLessThan(Long value) {
            addCriterion("self_id <", value, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdLessThanOrEqualTo(Long value) {
            addCriterion("self_id <=", value, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdIn(List<Long> values) {
            addCriterion("self_id in", values, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdNotIn(List<Long> values) {
            addCriterion("self_id not in", values, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdBetween(Long value1, Long value2) {
            addCriterion("self_id between", value1, value2, "selfId");
            return (Criteria) this;
        }

        public Criteria andSelfIdNotBetween(Long value1, Long value2) {
            addCriterion("self_id not between", value1, value2, "selfId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}