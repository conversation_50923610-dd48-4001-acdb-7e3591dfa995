package com.fastclip.dao.conn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fastclip.common.model.ao.VideoClipAO;
import com.fastclip.common.model.ao.ChanxuanClipDatasAO;
import com.fastclip.common.model.ao.ChanxuanItemAO;
import com.fastclip.common.model.request.ChanxuanItemRequest;
import com.fastclip.common.utils.ChanxuanUtil;
import com.fastclip.common.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ChanxuanConnection {
    String connUrlV2 = "https://api-service.chanxuan.com/v2/clipKolLive/findClipsByAuthorId?" +
            "date=&clip_type=3&clip_tag=&&total_count=0";

    String connUrlV1 = "https://api-service.chanxuan.com/v1/clipKolLive/findClipsByAuthorId?" +
            "date=&clip_type=3&clip_tag=&&total_count=0";

    String clipConnUrl = "https://api-service.chanjian.cc/v1/clip/product/findProductClips?author_id=3249753514836668&date=&clip_type=3";

    String itemDataConnUrl = "https://api-service.chanxuan.com/v3/cooperationProduct/findPageByAuthorId?" +
            "search_str=&is_stock=0&explain_time=&order_by=&sort=&is_first_live_product=0";

    String downloadConnUrl = "https://api-service.chanjian.cc/v1/clip/clip/download-v2?task_id=";

    String confirmConnUrl = "https://api-service.chanjian.cc/v1/clip/clip/download-confirm?task_id=";

    @Value("${chanxuan.x_authorization_cx}")
    String xAuthorizationCx;

    @Value("${chanxuan.cooperation_id}")
    String cooperationId;

    @Value("${chanxuan.cookie}")
    String cookie;

    @Value("${chanxuan.account}")
    String cxAccount;

    @Value("${chanxuan.star_id}")
    String starId;

    @Value("${chanxuan.star_name}")
    String starName;


    public List<VideoClipAO> getClipKolData(Integer page, Integer pageSize){
        try {
            String url = connUrlV2 + "&cooperation_auto_id=" + cooperationId + "&page=" + page + "&size=" + pageSize;
            Map<String, String> headers = new HashMap<>();
            headers.put("x-authorization-cx", xAuthorizationCx);
            String dataStr = HttpUtil.sendGet(url, headers);
            JSONObject dataObject = JSON.parseObject(dataStr);
            return JSON.parseArray(dataObject.get("data").toString(), VideoClipAO.class);
        }catch (Exception e) {
            log.error("get data error", e);
        }
        return new ArrayList<>();
    }

    public List<VideoClipAO> getClipDataByProductV2(String productId, Integer page, Integer pageSize){
        try {
            String url = connUrlV2 + "&cooperation_auto_id=" + cooperationId + "&product_id=" + productId +
                        "&page=" + page + "&size=" + pageSize;
            Map<String, String> headers = new HashMap<>();
            headers.put("x-authorization-cx", xAuthorizationCx);
            String dataStr = HttpUtil.sendGet(url, headers);
            JSONObject dataObject = JSON.parseObject(dataStr);
            List<ChanxuanClipDatasAO> clipDatasDTOS = JSON.parseArray(dataObject.get("data").toString(), ChanxuanClipDatasAO.class);
            if(CollectionUtils.isEmpty(clipDatasDTOS)) {
                return new ArrayList<>();
            }
            List<VideoClipAO> clipDTOS =  ChanxuanUtil.getClipAOS(clipDatasDTOS);
            if(CollectionUtils.isEmpty(clipDTOS)) {
                return new ArrayList<>();
            }
            for (VideoClipAO clipDTO : clipDTOS) {
                clipDTO.setCxAccount(cxAccount);
                clipDTO.setStarId(starId);
                clipDTO.setStarName(starName);
            }
            return clipDTOS;
        }catch (Exception e) {
            log.error("get data error", e);
        }
        return new ArrayList<>();
    }

    public List<VideoClipAO> getClipDataByProductV1(String productId, Integer page, Integer pageSize){
        try {

            String url = connUrlV1 + "&cooperation_auto_id=" + cooperationId + "&product_id=" + productId +
                        "&page=" + page + "&size=" + pageSize;
            Map<String, String> headers = new HashMap<>();
            headers.put("x-authorization-cx", xAuthorizationCx);
            String dataStr = HttpUtil.sendGet(url, headers);
            JSONObject dataObject = JSON.parseObject(dataStr);
            List<VideoClipAO> clipDatasDTOS = JSON.parseArray(dataObject.get("data").toString(), VideoClipAO.class);
            if(CollectionUtils.isEmpty(clipDatasDTOS)) {
                return new ArrayList<>();
            }
            return clipDatasDTOS;
        }catch (Exception e) {
            log.error("get data error", e);
        }
        return new ArrayList<>();
    }


    /**
     * 获取现货商品列表
     * @param req
     * @return
     */
    public List<ChanxuanItemAO> getItemData(ChanxuanItemRequest req){
        try {
            String url = itemDataConnUrl + "&author_unique_id=" + req.getAuthorId() + "&page=" + req.getPage() + "&size=" +
                    req.getPageSize() + "&presell_type=" + req.getPreSellType();
            Map<String, String> headers = new HashMap<>();
            headers.put("x-authorization-cx", xAuthorizationCx);
            String dataStr = HttpUtil.sendGet(url, headers);
            JSONObject dataObject = JSON.parseObject(dataStr);
            List<ChanxuanItemAO> itemDTOS = JSON.parseArray(((JSONObject)(dataObject.get("data"))).get("list").toString(), ChanxuanItemAO.class);
            if(CollectionUtils.isEmpty(itemDTOS)) {
                return new ArrayList<>();
            }
            itemDTOS.forEach(item-> {
                item.setCx_account(cxAccount);
                item.setStar_id(starId);
                item.setStar_name(starName);
            });
            return itemDTOS;
        }catch (Exception e) {
            log.error("get data error", e);
        }
        return new ArrayList<>();
    }

    /**
     * 确认下载
     * @param taskId
     * @return
     */
    public String confirmDownload(String taskId){
        try {
            //先提交下载任务
            String url = downloadConnUrl + taskId;
            Map<String, String> headers = new HashMap<>();
            headers.put("Cookie", cookie);
            HttpUtil.sendGet(url, headers);
            //获取下载链接
            url = confirmConnUrl + taskId;
            String dataStr = HttpUtil.sendGet(url, headers);
            JSONObject dataObject = JSON.parseObject(dataStr);
            return ((JSONObject)(dataObject.get("data"))).get("url").toString();
        }catch (Exception e) {
            log.error("get data error", e);
        }
        return null;
    }


    public static void main(String[] args) {
    }
}
