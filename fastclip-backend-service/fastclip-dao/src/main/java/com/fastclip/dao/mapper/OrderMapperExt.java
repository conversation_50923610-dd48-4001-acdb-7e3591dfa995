package com.fastclip.dao.mapper;

import com.fastclip.common.model.dataobject.OrderDO;
import com.fastclip.common.model.request.GetOrderListReq;
import com.fastclip.common.model.request.OrderCountReq;
import com.fastclip.dao.model.dataobject.Orders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrderMapperExt {

    int batchInsertOrUpdateOrders(@Param("orders")List<Orders> orders);

    OrderDO getOrdersCount(@Param("req") GetOrderListReq req);

    List<Orders> getOrdersList(@Param("req")GetOrderListReq req);
}