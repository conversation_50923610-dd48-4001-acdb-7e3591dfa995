package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class VideoClip {
    private Long id;

    private Long projectId;

    private Long startSubtitlesCutId;

    private Long endSubtitlesCutId;

    private Long startSubtitlesId;

    private Long endSubtitlesId;

    private String subtitles;

    private Integer duration;

    private Integer subtitlesCutCount;

    private Integer sort;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getStartSubtitlesCutId() {
        return startSubtitlesCutId;
    }

    public void setStartSubtitlesCutId(Long startSubtitlesCutId) {
        this.startSubtitlesCutId = startSubtitlesCutId;
    }

    public Long getEndSubtitlesCutId() {
        return endSubtitlesCutId;
    }

    public void setEndSubtitlesCutId(Long endSubtitlesCutId) {
        this.endSubtitlesCutId = endSubtitlesCutId;
    }

    public Long getStartSubtitlesId() {
        return startSubtitlesId;
    }

    public void setStartSubtitlesId(Long startSubtitlesId) {
        this.startSubtitlesId = startSubtitlesId;
    }

    public Long getEndSubtitlesId() {
        return endSubtitlesId;
    }

    public void setEndSubtitlesId(Long endSubtitlesId) {
        this.endSubtitlesId = endSubtitlesId;
    }

    public String getSubtitles() {
        return subtitles;
    }

    public void setSubtitles(String subtitles) {
        this.subtitles = subtitles == null ? null : subtitles.trim();
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getSubtitlesCutCount() {
        return subtitlesCutCount;
    }

    public void setSubtitlesCutCount(Integer subtitlesCutCount) {
        this.subtitlesCutCount = subtitlesCutCount;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}