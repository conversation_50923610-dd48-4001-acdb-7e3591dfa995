package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class FUser {
    private Long id;

    private String userName;

    private Boolean isAdmin;

    private String passwd;

    private Date createTime;

    private Date updateTime;

    private String userAccount;

    private Boolean hasUploadPrevilege;

    private Boolean isSelfCutter;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public Boolean getIsAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(Boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd == null ? null : passwd.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount == null ? null : userAccount.trim();
    }

    public Boolean getHasUploadPrevilege() {
        return hasUploadPrevilege;
    }

    public void setHasUploadPrevilege(Boolean hasUploadPrevilege) {
        this.hasUploadPrevilege = hasUploadPrevilege;
    }

    public Boolean getIsSelfCutter() {
        return isSelfCutter;
    }

    public void setIsSelfCutter(Boolean isSelfCutter) {
        this.isSelfCutter = isSelfCutter;
    }
}