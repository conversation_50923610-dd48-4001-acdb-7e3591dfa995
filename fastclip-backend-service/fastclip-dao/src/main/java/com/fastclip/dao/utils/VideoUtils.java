package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.VideoMaterialClipDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.dao.model.dataobject.VideoMaterial;
import com.fastclip.dao.model.dataobject.VideoMaterialClip;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class VideoUtils {

    public static VideoMaterialClipDTO do2DTO(VideoMaterialClip videoMaterialClip) {
        VideoMaterialClipDTO videoMaterialClipDTO = new VideoMaterialClipDTO();
        BeanUtils.copyProperties(videoMaterialClip, videoMaterialClipDTO);
        return videoMaterialClipDTO;
    }

    public static VideoMaterialClip dto2DO(VideoMaterialClipDTO videoMaterialClipDTO) {
        VideoMaterialClip videoMaterialClip = new VideoMaterialClip();
        BeanUtils.copyProperties(videoMaterialClipDTO, videoMaterialClip);
        return videoMaterialClip;
    }

    public static List<VideoMaterialClipDTO> do2DTOs(List<VideoMaterialClip> videoMaterialClips) {
        if (CollectionUtils.isEmpty(videoMaterialClips)) {
            return new ArrayList<>();
        }
        return videoMaterialClips.stream().map(VideoUtils::do2DTO).collect(Collectors.toList());
    }

    public static VideoMaterialDTO materialDo2DTO(VideoMaterial videoMaterial) {
        if(videoMaterial == null) {
            return null;
        }
        VideoMaterialDTO videoMaterialDTO = new VideoMaterialDTO();
        BeanUtils.copyProperties(videoMaterial, videoMaterialDTO);
        return videoMaterialDTO;
    }

    public static List<VideoMaterialDTO> materialDo2DTOs(List<VideoMaterial> videoMaterials) {
        if (CollectionUtils.isEmpty(videoMaterials)) {
            return new ArrayList<>();
        }
        return videoMaterials.stream().map(VideoUtils::materialDo2DTO).collect(Collectors.toList());
    }

    public static VideoMaterial materialDto2Do(VideoMaterialDTO videoMaterialDTO) {
        VideoMaterial videoMaterial = new VideoMaterial();
        BeanUtils.copyProperties(videoMaterialDTO, videoMaterial);
        return videoMaterial;
    }


}
