package com.fastclip.dao.model.dataobject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorksExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorksExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("item_id is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("item_id is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(Long value) {
            addCriterion("item_id =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(Long value) {
            addCriterion("item_id <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(Long value) {
            addCriterion("item_id >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("item_id >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(Long value) {
            addCriterion("item_id <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(Long value) {
            addCriterion("item_id <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<Long> values) {
            addCriterion("item_id in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<Long> values) {
            addCriterion("item_id not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(Long value1, Long value2) {
            addCriterion("item_id between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(Long value1, Long value2) {
            addCriterion("item_id not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNull() {
            addCriterion("seller_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNotNull() {
            addCriterion("seller_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerIdEqualTo(Long value) {
            addCriterion("seller_id =", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotEqualTo(Long value) {
            addCriterion("seller_id <>", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThan(Long value) {
            addCriterion("seller_id >", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("seller_id >=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThan(Long value) {
            addCriterion("seller_id <", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThanOrEqualTo(Long value) {
            addCriterion("seller_id <=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdIn(List<Long> values) {
            addCriterion("seller_id in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotIn(List<Long> values) {
            addCriterion("seller_id not in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdBetween(Long value1, Long value2) {
            addCriterion("seller_id between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotBetween(Long value1, Long value2) {
            addCriterion("seller_id not between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Integer value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Integer value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Integer value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Integer value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Integer value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Integer> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Integer> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Integer value1, Integer value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeIsNull() {
            addCriterion("special_effects_template_code is null");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeIsNotNull() {
            addCriterion("special_effects_template_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeEqualTo(String value) {
            addCriterion("special_effects_template_code =", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeNotEqualTo(String value) {
            addCriterion("special_effects_template_code <>", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeGreaterThan(String value) {
            addCriterion("special_effects_template_code >", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeGreaterThanOrEqualTo(String value) {
            addCriterion("special_effects_template_code >=", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeLessThan(String value) {
            addCriterion("special_effects_template_code <", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeLessThanOrEqualTo(String value) {
            addCriterion("special_effects_template_code <=", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeLike(String value) {
            addCriterion("special_effects_template_code like", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeNotLike(String value) {
            addCriterion("special_effects_template_code not like", value, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeIn(List<String> values) {
            addCriterion("special_effects_template_code in", values, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeNotIn(List<String> values) {
            addCriterion("special_effects_template_code not in", values, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeBetween(String value1, String value2) {
            addCriterion("special_effects_template_code between", value1, value2, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andSpecialEffectsTemplateCodeNotBetween(String value1, String value2) {
            addCriterion("special_effects_template_code not between", value1, value2, "specialEffectsTemplateCode");
            return (Criteria) this;
        }

        public Criteria andIsComposedIsNull() {
            addCriterion("is_composed is null");
            return (Criteria) this;
        }

        public Criteria andIsComposedIsNotNull() {
            addCriterion("is_composed is not null");
            return (Criteria) this;
        }

        public Criteria andIsComposedEqualTo(Boolean value) {
            addCriterion("is_composed =", value, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedNotEqualTo(Boolean value) {
            addCriterion("is_composed <>", value, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedGreaterThan(Boolean value) {
            addCriterion("is_composed >", value, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_composed >=", value, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedLessThan(Boolean value) {
            addCriterion("is_composed <", value, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_composed <=", value, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedIn(List<Boolean> values) {
            addCriterion("is_composed in", values, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedNotIn(List<Boolean> values) {
            addCriterion("is_composed not in", values, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_composed between", value1, value2, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsComposedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_composed not between", value1, value2, "isComposed");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIsNull() {
            addCriterion("is_published is null");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIsNotNull() {
            addCriterion("is_published is not null");
            return (Criteria) this;
        }

        public Criteria andIsPublishedEqualTo(Boolean value) {
            addCriterion("is_published =", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotEqualTo(Boolean value) {
            addCriterion("is_published <>", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedGreaterThan(Boolean value) {
            addCriterion("is_published >", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_published >=", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedLessThan(Boolean value) {
            addCriterion("is_published <", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_published <=", value, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedIn(List<Boolean> values) {
            addCriterion("is_published in", values, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotIn(List<Boolean> values) {
            addCriterion("is_published not in", values, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_published between", value1, value2, "isPublished");
            return (Criteria) this;
        }

        public Criteria andIsPublishedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_published not between", value1, value2, "isPublished");
            return (Criteria) this;
        }

        public Criteria andVideoPathIsNull() {
            addCriterion("video_path is null");
            return (Criteria) this;
        }

        public Criteria andVideoPathIsNotNull() {
            addCriterion("video_path is not null");
            return (Criteria) this;
        }

        public Criteria andVideoPathEqualTo(String value) {
            addCriterion("video_path =", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathNotEqualTo(String value) {
            addCriterion("video_path <>", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathGreaterThan(String value) {
            addCriterion("video_path >", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathGreaterThanOrEqualTo(String value) {
            addCriterion("video_path >=", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathLessThan(String value) {
            addCriterion("video_path <", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathLessThanOrEqualTo(String value) {
            addCriterion("video_path <=", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathLike(String value) {
            addCriterion("video_path like", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathNotLike(String value) {
            addCriterion("video_path not like", value, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathIn(List<String> values) {
            addCriterion("video_path in", values, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathNotIn(List<String> values) {
            addCriterion("video_path not in", values, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathBetween(String value1, String value2) {
            addCriterion("video_path between", value1, value2, "videoPath");
            return (Criteria) this;
        }

        public Criteria andVideoPathNotBetween(String value1, String value2) {
            addCriterion("video_path not between", value1, value2, "videoPath");
            return (Criteria) this;
        }

        public Criteria andMusicIdIsNull() {
            addCriterion("music_id is null");
            return (Criteria) this;
        }

        public Criteria andMusicIdIsNotNull() {
            addCriterion("music_id is not null");
            return (Criteria) this;
        }

        public Criteria andMusicIdEqualTo(Integer value) {
            addCriterion("music_id =", value, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdNotEqualTo(Integer value) {
            addCriterion("music_id <>", value, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdGreaterThan(Integer value) {
            addCriterion("music_id >", value, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("music_id >=", value, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdLessThan(Integer value) {
            addCriterion("music_id <", value, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdLessThanOrEqualTo(Integer value) {
            addCriterion("music_id <=", value, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdIn(List<Integer> values) {
            addCriterion("music_id in", values, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdNotIn(List<Integer> values) {
            addCriterion("music_id not in", values, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdBetween(Integer value1, Integer value2) {
            addCriterion("music_id between", value1, value2, "musicId");
            return (Criteria) this;
        }

        public Criteria andMusicIdNotBetween(Integer value1, Integer value2) {
            addCriterion("music_id not between", value1, value2, "musicId");
            return (Criteria) this;
        }

        public Criteria andFilterIdIsNull() {
            addCriterion("filter_id is null");
            return (Criteria) this;
        }

        public Criteria andFilterIdIsNotNull() {
            addCriterion("filter_id is not null");
            return (Criteria) this;
        }

        public Criteria andFilterIdEqualTo(Integer value) {
            addCriterion("filter_id =", value, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdNotEqualTo(Integer value) {
            addCriterion("filter_id <>", value, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdGreaterThan(Integer value) {
            addCriterion("filter_id >", value, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("filter_id >=", value, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdLessThan(Integer value) {
            addCriterion("filter_id <", value, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdLessThanOrEqualTo(Integer value) {
            addCriterion("filter_id <=", value, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdIn(List<Integer> values) {
            addCriterion("filter_id in", values, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdNotIn(List<Integer> values) {
            addCriterion("filter_id not in", values, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdBetween(Integer value1, Integer value2) {
            addCriterion("filter_id between", value1, value2, "filterId");
            return (Criteria) this;
        }

        public Criteria andFilterIdNotBetween(Integer value1, Integer value2) {
            addCriterion("filter_id not between", value1, value2, "filterId");
            return (Criteria) this;
        }

        public Criteria andStickerIdIsNull() {
            addCriterion("sticker_id is null");
            return (Criteria) this;
        }

        public Criteria andStickerIdIsNotNull() {
            addCriterion("sticker_id is not null");
            return (Criteria) this;
        }

        public Criteria andStickerIdEqualTo(Integer value) {
            addCriterion("sticker_id =", value, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdNotEqualTo(Integer value) {
            addCriterion("sticker_id <>", value, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdGreaterThan(Integer value) {
            addCriterion("sticker_id >", value, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("sticker_id >=", value, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdLessThan(Integer value) {
            addCriterion("sticker_id <", value, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdLessThanOrEqualTo(Integer value) {
            addCriterion("sticker_id <=", value, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdIn(List<Integer> values) {
            addCriterion("sticker_id in", values, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdNotIn(List<Integer> values) {
            addCriterion("sticker_id not in", values, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdBetween(Integer value1, Integer value2) {
            addCriterion("sticker_id between", value1, value2, "stickerId");
            return (Criteria) this;
        }

        public Criteria andStickerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("sticker_id not between", value1, value2, "stickerId");
            return (Criteria) this;
        }

        public Criteria andSpeedUpIsNull() {
            addCriterion("speed_up is null");
            return (Criteria) this;
        }

        public Criteria andSpeedUpIsNotNull() {
            addCriterion("speed_up is not null");
            return (Criteria) this;
        }

        public Criteria andSpeedUpEqualTo(Integer value) {
            addCriterion("speed_up =", value, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpNotEqualTo(Integer value) {
            addCriterion("speed_up <>", value, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpGreaterThan(Integer value) {
            addCriterion("speed_up >", value, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpGreaterThanOrEqualTo(Integer value) {
            addCriterion("speed_up >=", value, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpLessThan(Integer value) {
            addCriterion("speed_up <", value, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpLessThanOrEqualTo(Integer value) {
            addCriterion("speed_up <=", value, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpIn(List<Integer> values) {
            addCriterion("speed_up in", values, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpNotIn(List<Integer> values) {
            addCriterion("speed_up not in", values, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpBetween(Integer value1, Integer value2) {
            addCriterion("speed_up between", value1, value2, "speedUp");
            return (Criteria) this;
        }

        public Criteria andSpeedUpNotBetween(Integer value1, Integer value2) {
            addCriterion("speed_up not between", value1, value2, "speedUp");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andWorksNameIsNull() {
            addCriterion("works_name is null");
            return (Criteria) this;
        }

        public Criteria andWorksNameIsNotNull() {
            addCriterion("works_name is not null");
            return (Criteria) this;
        }

        public Criteria andWorksNameEqualTo(String value) {
            addCriterion("works_name =", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameNotEqualTo(String value) {
            addCriterion("works_name <>", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameGreaterThan(String value) {
            addCriterion("works_name >", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameGreaterThanOrEqualTo(String value) {
            addCriterion("works_name >=", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameLessThan(String value) {
            addCriterion("works_name <", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameLessThanOrEqualTo(String value) {
            addCriterion("works_name <=", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameLike(String value) {
            addCriterion("works_name like", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameNotLike(String value) {
            addCriterion("works_name not like", value, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameIn(List<String> values) {
            addCriterion("works_name in", values, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameNotIn(List<String> values) {
            addCriterion("works_name not in", values, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameBetween(String value1, String value2) {
            addCriterion("works_name between", value1, value2, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksNameNotBetween(String value1, String value2) {
            addCriterion("works_name not between", value1, value2, "worksName");
            return (Criteria) this;
        }

        public Criteria andWorksDescIsNull() {
            addCriterion("works_desc is null");
            return (Criteria) this;
        }

        public Criteria andWorksDescIsNotNull() {
            addCriterion("works_desc is not null");
            return (Criteria) this;
        }

        public Criteria andWorksDescEqualTo(String value) {
            addCriterion("works_desc =", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescNotEqualTo(String value) {
            addCriterion("works_desc <>", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescGreaterThan(String value) {
            addCriterion("works_desc >", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescGreaterThanOrEqualTo(String value) {
            addCriterion("works_desc >=", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescLessThan(String value) {
            addCriterion("works_desc <", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescLessThanOrEqualTo(String value) {
            addCriterion("works_desc <=", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescLike(String value) {
            addCriterion("works_desc like", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescNotLike(String value) {
            addCriterion("works_desc not like", value, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescIn(List<String> values) {
            addCriterion("works_desc in", values, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescNotIn(List<String> values) {
            addCriterion("works_desc not in", values, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescBetween(String value1, String value2) {
            addCriterion("works_desc between", value1, value2, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksDescNotBetween(String value1, String value2) {
            addCriterion("works_desc not between", value1, value2, "worksDesc");
            return (Criteria) this;
        }

        public Criteria andWorksCoverIsNull() {
            addCriterion("works_cover is null");
            return (Criteria) this;
        }

        public Criteria andWorksCoverIsNotNull() {
            addCriterion("works_cover is not null");
            return (Criteria) this;
        }

        public Criteria andWorksCoverEqualTo(String value) {
            addCriterion("works_cover =", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverNotEqualTo(String value) {
            addCriterion("works_cover <>", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverGreaterThan(String value) {
            addCriterion("works_cover >", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverGreaterThanOrEqualTo(String value) {
            addCriterion("works_cover >=", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverLessThan(String value) {
            addCriterion("works_cover <", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverLessThanOrEqualTo(String value) {
            addCriterion("works_cover <=", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverLike(String value) {
            addCriterion("works_cover like", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverNotLike(String value) {
            addCriterion("works_cover not like", value, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverIn(List<String> values) {
            addCriterion("works_cover in", values, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverNotIn(List<String> values) {
            addCriterion("works_cover not in", values, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverBetween(String value1, String value2) {
            addCriterion("works_cover between", value1, value2, "worksCover");
            return (Criteria) this;
        }

        public Criteria andWorksCoverNotBetween(String value1, String value2) {
            addCriterion("works_cover not between", value1, value2, "worksCover");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameIsNull() {
            addCriterion("simple_item_name is null");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameIsNotNull() {
            addCriterion("simple_item_name is not null");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameEqualTo(String value) {
            addCriterion("simple_item_name =", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameNotEqualTo(String value) {
            addCriterion("simple_item_name <>", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameGreaterThan(String value) {
            addCriterion("simple_item_name >", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("simple_item_name >=", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameLessThan(String value) {
            addCriterion("simple_item_name <", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameLessThanOrEqualTo(String value) {
            addCriterion("simple_item_name <=", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameLike(String value) {
            addCriterion("simple_item_name like", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameNotLike(String value) {
            addCriterion("simple_item_name not like", value, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameIn(List<String> values) {
            addCriterion("simple_item_name in", values, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameNotIn(List<String> values) {
            addCriterion("simple_item_name not in", values, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameBetween(String value1, String value2) {
            addCriterion("simple_item_name between", value1, value2, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andSimpleItemNameNotBetween(String value1, String value2) {
            addCriterion("simple_item_name not between", value1, value2, "simpleItemName");
            return (Criteria) this;
        }

        public Criteria andWorksTagIsNull() {
            addCriterion("works_tag is null");
            return (Criteria) this;
        }

        public Criteria andWorksTagIsNotNull() {
            addCriterion("works_tag is not null");
            return (Criteria) this;
        }

        public Criteria andWorksTagEqualTo(String value) {
            addCriterion("works_tag =", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagNotEqualTo(String value) {
            addCriterion("works_tag <>", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagGreaterThan(String value) {
            addCriterion("works_tag >", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagGreaterThanOrEqualTo(String value) {
            addCriterion("works_tag >=", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagLessThan(String value) {
            addCriterion("works_tag <", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagLessThanOrEqualTo(String value) {
            addCriterion("works_tag <=", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagLike(String value) {
            addCriterion("works_tag like", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagNotLike(String value) {
            addCriterion("works_tag not like", value, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagIn(List<String> values) {
            addCriterion("works_tag in", values, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagNotIn(List<String> values) {
            addCriterion("works_tag not in", values, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagBetween(String value1, String value2) {
            addCriterion("works_tag between", value1, value2, "worksTag");
            return (Criteria) this;
        }

        public Criteria andWorksTagNotBetween(String value1, String value2) {
            addCriterion("works_tag not between", value1, value2, "worksTag");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Long value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Long value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Long value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Long value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Long> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Long> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Long value1, Long value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeIsNull() {
            addCriterion("published_time is null");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeIsNotNull() {
            addCriterion("published_time is not null");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeEqualTo(Date value) {
            addCriterion("published_time =", value, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeNotEqualTo(Date value) {
            addCriterion("published_time <>", value, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeGreaterThan(Date value) {
            addCriterion("published_time >", value, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("published_time >=", value, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeLessThan(Date value) {
            addCriterion("published_time <", value, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeLessThanOrEqualTo(Date value) {
            addCriterion("published_time <=", value, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeIn(List<Date> values) {
            addCriterion("published_time in", values, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeNotIn(List<Date> values) {
            addCriterion("published_time not in", values, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeBetween(Date value1, Date value2) {
            addCriterion("published_time between", value1, value2, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andPublishedTimeNotBetween(Date value1, Date value2) {
            addCriterion("published_time not between", value1, value2, "publishedTime");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesIsNull() {
            addCriterion("item_features is null");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesIsNotNull() {
            addCriterion("item_features is not null");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesEqualTo(String value) {
            addCriterion("item_features =", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesNotEqualTo(String value) {
            addCriterion("item_features <>", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesGreaterThan(String value) {
            addCriterion("item_features >", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesGreaterThanOrEqualTo(String value) {
            addCriterion("item_features >=", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesLessThan(String value) {
            addCriterion("item_features <", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesLessThanOrEqualTo(String value) {
            addCriterion("item_features <=", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesLike(String value) {
            addCriterion("item_features like", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesNotLike(String value) {
            addCriterion("item_features not like", value, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesIn(List<String> values) {
            addCriterion("item_features in", values, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesNotIn(List<String> values) {
            addCriterion("item_features not in", values, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesBetween(String value1, String value2) {
            addCriterion("item_features between", value1, value2, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemFeaturesNotBetween(String value1, String value2) {
            addCriterion("item_features not between", value1, value2, "itemFeatures");
            return (Criteria) this;
        }

        public Criteria andItemTagsIsNull() {
            addCriterion("item_tags is null");
            return (Criteria) this;
        }

        public Criteria andItemTagsIsNotNull() {
            addCriterion("item_tags is not null");
            return (Criteria) this;
        }

        public Criteria andItemTagsEqualTo(String value) {
            addCriterion("item_tags =", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsNotEqualTo(String value) {
            addCriterion("item_tags <>", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsGreaterThan(String value) {
            addCriterion("item_tags >", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsGreaterThanOrEqualTo(String value) {
            addCriterion("item_tags >=", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsLessThan(String value) {
            addCriterion("item_tags <", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsLessThanOrEqualTo(String value) {
            addCriterion("item_tags <=", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsLike(String value) {
            addCriterion("item_tags like", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsNotLike(String value) {
            addCriterion("item_tags not like", value, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsIn(List<String> values) {
            addCriterion("item_tags in", values, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsNotIn(List<String> values) {
            addCriterion("item_tags not in", values, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsBetween(String value1, String value2) {
            addCriterion("item_tags between", value1, value2, "itemTags");
            return (Criteria) this;
        }

        public Criteria andItemTagsNotBetween(String value1, String value2) {
            addCriterion("item_tags not between", value1, value2, "itemTags");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNull() {
            addCriterion("creator_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNotNull() {
            addCriterion("creator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdEqualTo(Long value) {
            addCriterion("creator_id =", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotEqualTo(Long value) {
            addCriterion("creator_id <>", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThan(Long value) {
            addCriterion("creator_id >", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("creator_id >=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThan(Long value) {
            addCriterion("creator_id <", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThanOrEqualTo(Long value) {
            addCriterion("creator_id <=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIn(List<Long> values) {
            addCriterion("creator_id in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotIn(List<Long> values) {
            addCriterion("creator_id not in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdBetween(Long value1, Long value2) {
            addCriterion("creator_id between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotBetween(Long value1, Long value2) {
            addCriterion("creator_id not between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andFontIdIsNull() {
            addCriterion("font_id is null");
            return (Criteria) this;
        }

        public Criteria andFontIdIsNotNull() {
            addCriterion("font_id is not null");
            return (Criteria) this;
        }

        public Criteria andFontIdEqualTo(Integer value) {
            addCriterion("font_id =", value, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdNotEqualTo(Integer value) {
            addCriterion("font_id <>", value, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdGreaterThan(Integer value) {
            addCriterion("font_id >", value, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("font_id >=", value, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdLessThan(Integer value) {
            addCriterion("font_id <", value, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdLessThanOrEqualTo(Integer value) {
            addCriterion("font_id <=", value, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdIn(List<Integer> values) {
            addCriterion("font_id in", values, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdNotIn(List<Integer> values) {
            addCriterion("font_id not in", values, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdBetween(Integer value1, Integer value2) {
            addCriterion("font_id between", value1, value2, "fontId");
            return (Criteria) this;
        }

        public Criteria andFontIdNotBetween(Integer value1, Integer value2) {
            addCriterion("font_id not between", value1, value2, "fontId");
            return (Criteria) this;
        }

        public Criteria andKeywordsIsNull() {
            addCriterion("keywords is null");
            return (Criteria) this;
        }

        public Criteria andKeywordsIsNotNull() {
            addCriterion("keywords is not null");
            return (Criteria) this;
        }

        public Criteria andKeywordsEqualTo(String value) {
            addCriterion("keywords =", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsNotEqualTo(String value) {
            addCriterion("keywords <>", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsGreaterThan(String value) {
            addCriterion("keywords >", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsGreaterThanOrEqualTo(String value) {
            addCriterion("keywords >=", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsLessThan(String value) {
            addCriterion("keywords <", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsLessThanOrEqualTo(String value) {
            addCriterion("keywords <=", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsLike(String value) {
            addCriterion("keywords like", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsNotLike(String value) {
            addCriterion("keywords not like", value, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsIn(List<String> values) {
            addCriterion("keywords in", values, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsNotIn(List<String> values) {
            addCriterion("keywords not in", values, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsBetween(String value1, String value2) {
            addCriterion("keywords between", value1, value2, "keywords");
            return (Criteria) this;
        }

        public Criteria andKeywordsNotBetween(String value1, String value2) {
            addCriterion("keywords not between", value1, value2, "keywords");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedIsNull() {
            addCriterion("is_desc_completed is null");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedIsNotNull() {
            addCriterion("is_desc_completed is not null");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedEqualTo(Boolean value) {
            addCriterion("is_desc_completed =", value, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedNotEqualTo(Boolean value) {
            addCriterion("is_desc_completed <>", value, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedGreaterThan(Boolean value) {
            addCriterion("is_desc_completed >", value, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_desc_completed >=", value, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedLessThan(Boolean value) {
            addCriterion("is_desc_completed <", value, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_desc_completed <=", value, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedIn(List<Boolean> values) {
            addCriterion("is_desc_completed in", values, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedNotIn(List<Boolean> values) {
            addCriterion("is_desc_completed not in", values, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_desc_completed between", value1, value2, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDescCompletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_desc_completed not between", value1, value2, "isDescCompleted");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdIsNull() {
            addCriterion("left_top_sticker_id is null");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdIsNotNull() {
            addCriterion("left_top_sticker_id is not null");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdEqualTo(Integer value) {
            addCriterion("left_top_sticker_id =", value, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdNotEqualTo(Integer value) {
            addCriterion("left_top_sticker_id <>", value, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdGreaterThan(Integer value) {
            addCriterion("left_top_sticker_id >", value, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("left_top_sticker_id >=", value, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdLessThan(Integer value) {
            addCriterion("left_top_sticker_id <", value, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdLessThanOrEqualTo(Integer value) {
            addCriterion("left_top_sticker_id <=", value, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdIn(List<Integer> values) {
            addCriterion("left_top_sticker_id in", values, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdNotIn(List<Integer> values) {
            addCriterion("left_top_sticker_id not in", values, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdBetween(Integer value1, Integer value2) {
            addCriterion("left_top_sticker_id between", value1, value2, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftTopStickerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("left_top_sticker_id not between", value1, value2, "leftTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdIsNull() {
            addCriterion("right_top_sticker_id is null");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdIsNotNull() {
            addCriterion("right_top_sticker_id is not null");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdEqualTo(Integer value) {
            addCriterion("right_top_sticker_id =", value, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdNotEqualTo(Integer value) {
            addCriterion("right_top_sticker_id <>", value, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdGreaterThan(Integer value) {
            addCriterion("right_top_sticker_id >", value, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("right_top_sticker_id >=", value, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdLessThan(Integer value) {
            addCriterion("right_top_sticker_id <", value, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdLessThanOrEqualTo(Integer value) {
            addCriterion("right_top_sticker_id <=", value, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdIn(List<Integer> values) {
            addCriterion("right_top_sticker_id in", values, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdNotIn(List<Integer> values) {
            addCriterion("right_top_sticker_id not in", values, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdBetween(Integer value1, Integer value2) {
            addCriterion("right_top_sticker_id between", value1, value2, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andRightTopStickerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("right_top_sticker_id not between", value1, value2, "rightTopStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdIsNull() {
            addCriterion("left_bottom_sticker_id is null");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdIsNotNull() {
            addCriterion("left_bottom_sticker_id is not null");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdEqualTo(Integer value) {
            addCriterion("left_bottom_sticker_id =", value, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdNotEqualTo(Integer value) {
            addCriterion("left_bottom_sticker_id <>", value, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdGreaterThan(Integer value) {
            addCriterion("left_bottom_sticker_id >", value, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("left_bottom_sticker_id >=", value, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdLessThan(Integer value) {
            addCriterion("left_bottom_sticker_id <", value, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdLessThanOrEqualTo(Integer value) {
            addCriterion("left_bottom_sticker_id <=", value, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdIn(List<Integer> values) {
            addCriterion("left_bottom_sticker_id in", values, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdNotIn(List<Integer> values) {
            addCriterion("left_bottom_sticker_id not in", values, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdBetween(Integer value1, Integer value2) {
            addCriterion("left_bottom_sticker_id between", value1, value2, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andLeftBottomStickerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("left_bottom_sticker_id not between", value1, value2, "leftBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdIsNull() {
            addCriterion("right_bottom_sticker_id is null");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdIsNotNull() {
            addCriterion("right_bottom_sticker_id is not null");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdEqualTo(Integer value) {
            addCriterion("right_bottom_sticker_id =", value, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdNotEqualTo(Integer value) {
            addCriterion("right_bottom_sticker_id <>", value, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdGreaterThan(Integer value) {
            addCriterion("right_bottom_sticker_id >", value, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("right_bottom_sticker_id >=", value, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdLessThan(Integer value) {
            addCriterion("right_bottom_sticker_id <", value, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdLessThanOrEqualTo(Integer value) {
            addCriterion("right_bottom_sticker_id <=", value, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdIn(List<Integer> values) {
            addCriterion("right_bottom_sticker_id in", values, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdNotIn(List<Integer> values) {
            addCriterion("right_bottom_sticker_id not in", values, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdBetween(Integer value1, Integer value2) {
            addCriterion("right_bottom_sticker_id between", value1, value2, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andRightBottomStickerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("right_bottom_sticker_id not between", value1, value2, "rightBottomStickerId");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssIsNull() {
            addCriterion("video_path_without_ass is null");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssIsNotNull() {
            addCriterion("video_path_without_ass is not null");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssEqualTo(String value) {
            addCriterion("video_path_without_ass =", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssNotEqualTo(String value) {
            addCriterion("video_path_without_ass <>", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssGreaterThan(String value) {
            addCriterion("video_path_without_ass >", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssGreaterThanOrEqualTo(String value) {
            addCriterion("video_path_without_ass >=", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssLessThan(String value) {
            addCriterion("video_path_without_ass <", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssLessThanOrEqualTo(String value) {
            addCriterion("video_path_without_ass <=", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssLike(String value) {
            addCriterion("video_path_without_ass like", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssNotLike(String value) {
            addCriterion("video_path_without_ass not like", value, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssIn(List<String> values) {
            addCriterion("video_path_without_ass in", values, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssNotIn(List<String> values) {
            addCriterion("video_path_without_ass not in", values, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssBetween(String value1, String value2) {
            addCriterion("video_path_without_ass between", value1, value2, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andVideoPathWithoutAssNotBetween(String value1, String value2) {
            addCriterion("video_path_without_ass not between", value1, value2, "videoPathWithoutAss");
            return (Criteria) this;
        }

        public Criteria andComposeStatusIsNull() {
            addCriterion("compose_status is null");
            return (Criteria) this;
        }

        public Criteria andComposeStatusIsNotNull() {
            addCriterion("compose_status is not null");
            return (Criteria) this;
        }

        public Criteria andComposeStatusEqualTo(Integer value) {
            addCriterion("compose_status =", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusNotEqualTo(Integer value) {
            addCriterion("compose_status <>", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusGreaterThan(Integer value) {
            addCriterion("compose_status >", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("compose_status >=", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusLessThan(Integer value) {
            addCriterion("compose_status <", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusLessThanOrEqualTo(Integer value) {
            addCriterion("compose_status <=", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusIn(List<Integer> values) {
            addCriterion("compose_status in", values, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusNotIn(List<Integer> values) {
            addCriterion("compose_status not in", values, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusBetween(Integer value1, Integer value2) {
            addCriterion("compose_status between", value1, value2, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("compose_status not between", value1, value2, "composeStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}