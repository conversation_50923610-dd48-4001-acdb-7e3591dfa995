package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.ApiOrdersDTO;
import com.fastclip.common.model.dto.OrdersDTO;
import com.fastclip.common.utils.NumberUtils;
import com.fastclip.dao.model.dataobject.Orders;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class OrderUtils {
    public static OrdersDTO do2DTO(Orders order) {
        if(order == null) {
            return null;
        }

        OrdersDTO ordersDTO = new OrdersDTO();
        BeanUtils.copyProperties(order, ordersDTO);
        ordersDTO.setCommissionRateStr((ordersDTO.getCommissionRate()/100d) + "%");
        ordersDTO.setTotalPayAmountStr(NumberUtils.doubleToStr((double) ordersDTO.getTotalPayAmount() / 100));
        return ordersDTO;
    }

    public static Orders dto2DO(OrdersDTO ordersDTO) {
        Orders order = new Orders();
        BeanUtils.copyProperties(ordersDTO, order);
        return order;
    }

    public static List<OrdersDTO> do2DTO(List<Orders> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return new ArrayList<>();
        }
        return orders.stream().map(OrderUtils::do2DTO).collect(Collectors.toList());
    }

    public static List<Orders> dto2DO(List<OrdersDTO> ordersDTOS) {
        if (CollectionUtils.isEmpty(ordersDTOS)) {
            return new ArrayList<>();
        }
        return ordersDTOS.stream().map(OrderUtils::dto2DO).collect(Collectors.toList());
    }

    public static OrdersDTO apiDTO2DTO(ApiOrdersDTO apiOrdersDTO) {
        OrdersDTO ordersDTO = new OrdersDTO();
        BeanUtils.copyProperties(apiOrdersDTO, ordersDTO);
        ordersDTO.setOrderId(apiOrdersDTO.getOrder_id());
        ordersDTO.setTotalPayAmount(apiOrdersDTO.getTotal_pay_amount());
        ordersDTO.setAuthorAccount(apiOrdersDTO.getAuthor_account());
        ordersDTO.setCommissionRate(apiOrdersDTO.getCommission_rate());
        ordersDTO.setAuthorShortId(apiOrdersDTO.getAuthor_short_id());
        ordersDTO.setEstimatedCommission(apiOrdersDTO.getEstimated_commission());
        ordersDTO.setEstimatedTechServiceFee(apiOrdersDTO.getEstimated_tech_service_fee());
        ordersDTO.setFlowPoint(apiOrdersDTO.getFlow_point());
        ordersDTO.setEstimatedTotalCommission(apiOrdersDTO.getEstimated_total_commission());
        ordersDTO.setItemNum(apiOrdersDTO.getItem_num());
        ordersDTO.setPaySuccessTime(apiOrdersDTO.getPay_success_time());
        ordersDTO.setPayGoodsAmount(apiOrdersDTO.getPay_goods_amount());
        ordersDTO.setSettleTime(apiOrdersDTO.getSettle_time());
        ordersDTO.setRefundTime(apiOrdersDTO.getRefund_time());
        ordersDTO.setConfirmTime(apiOrdersDTO.getConfirm_time());
        ordersDTO.setProductId(apiOrdersDTO.getProduct_id());
        ordersDTO.setProductName(apiOrdersDTO.getProduct_name());
        ordersDTO.setProductImg(apiOrdersDTO.getProduct_img());
        ordersDTO.setShopId(apiOrdersDTO.getShop_id());
        ordersDTO.setShopName(apiOrdersDTO.getShop_name());
        ordersDTO.setSettledGoodsAmount(apiOrdersDTO.getSettled_goods_amount());
        ordersDTO.setRealCommission(apiOrdersDTO.getReal_commission());
        ordersDTO.setUpdateTime(apiOrdersDTO.getUpdate_time());
        return ordersDTO;
    }

    public static List<OrdersDTO> apiDTO2DTO(List<ApiOrdersDTO> apiOrdersDTOS) {
        if (CollectionUtils.isEmpty(apiOrdersDTOS)) {
            return new ArrayList<>();
        }
        return apiOrdersDTOS.stream().map(OrderUtils::apiDTO2DTO).collect(Collectors.toList());
    }
}
