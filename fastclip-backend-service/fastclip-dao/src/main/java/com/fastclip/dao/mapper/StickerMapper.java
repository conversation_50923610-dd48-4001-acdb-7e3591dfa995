package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.Sticker;
import com.fastclip.dao.model.dataobject.StickerExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface StickerMapper {
    long countByExample(StickerExample example);

    int deleteByExample(StickerExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(Sticker record);

    int insertSelective(Sticker record);

    List<Sticker> selectByExampleWithRowbounds(StickerExample example, RowBounds rowBounds);

    List<Sticker> selectByExample(StickerExample example);

    Sticker selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") Sticker record, @Param("example") StickerExample example);

    int updateByExample(@Param("record") Sticker record, @Param("example") StickerExample example);

    int updateByPrimaryKeySelective(Sticker record);

    int updateByPrimaryKey(Sticker record);
}