package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.ProjectDTO;
import com.fastclip.dao.model.dataobject.Project;
import com.fastclip.dao.model.dataobject.ProjectExt;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ProjectUtils {
    public static ProjectDTO do2DTO(Project project) {
        ProjectDTO projectDTO = new ProjectDTO();
        BeanUtils.copyProperties(project, projectDTO);
        return projectDTO;
    }

    public static ProjectDTO extDo2DTO(ProjectExt projectExt) {
        ProjectDTO projectDTO = new ProjectDTO();
        BeanUtils.copyProperties(projectExt, projectDTO);
        return projectDTO;
    }


    public static Project dto2DO(ProjectDTO projectDTO) {
        Project project = new Project();
        BeanUtils.copyProperties(projectDTO, project);
        return project;
    }


    public static List<ProjectDTO> do2DTO(List<Project> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return new ArrayList<>();
        }
        return projects.stream().map(ProjectUtils::do2DTO).collect(Collectors.toList());
    }

    public static List<ProjectDTO> extDo2DTOs(List<ProjectExt> projectExts) {
        if (CollectionUtils.isEmpty(projectExts)) {
            return new ArrayList<>();
        }
        return projectExts.stream().map(ProjectUtils::extDo2DTO).collect(Collectors.toList());
    }
}
