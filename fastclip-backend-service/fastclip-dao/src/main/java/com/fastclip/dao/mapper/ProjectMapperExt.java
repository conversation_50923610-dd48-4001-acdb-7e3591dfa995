package com.fastclip.dao.mapper;

import com.fastclip.common.model.request.ProjectReq;
import com.fastclip.dao.model.dataobject.Project;
import com.fastclip.dao.model.dataobject.ProjectExample;
import com.fastclip.dao.model.dataobject.ProjectExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface ProjectMapperExt {
    List<ProjectExt> getProjects(@Param("req") ProjectReq req);
    Integer countProjects(@Param("req") ProjectReq req);

}