package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.DouyinAccountDTO;
import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.dao.model.dataobject.DouyinAccount;
import com.fastclip.dao.model.dataobject.Item;
import com.fastclip.dao.model.dataobject.ItemExt;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class DouyinAccountUtils {
    public static DouyinAccountDTO do2DTO(DouyinAccount douyinAccount) {
        DouyinAccountDTO douyinAccountDTO = new DouyinAccountDTO();
        BeanUtils.copyProperties(douyinAccount, douyinAccountDTO);
        return douyinAccountDTO;
    }

    public static List<DouyinAccountDTO> do2DTOs(List<DouyinAccount> douyinAccounts) {
        if (CollectionUtils.isEmpty(douyinAccounts)) {
            return new ArrayList<>();
        }
        return douyinAccounts.stream().map(DouyinAccountUtils::do2DTO).collect(Collectors.toList());
    }
}
