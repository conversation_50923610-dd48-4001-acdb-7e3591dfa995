package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class Seller {
    private Long id;

    private String sellerName;

    private String des;

    private Date createTime;

    private Date updateTime;

    private String materialBasePath;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des == null ? null : des.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getMaterialBasePath() {
        return materialBasePath;
    }

    public void setMaterialBasePath(String materialBasePath) {
        this.materialBasePath = materialBasePath == null ? null : materialBasePath.trim();
    }
}