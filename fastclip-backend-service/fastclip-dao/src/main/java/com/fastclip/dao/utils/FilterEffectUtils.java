package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.MusicDTO;
import com.fastclip.common.model.dto.specialEffect.FilterEffectDTO;
import com.fastclip.dao.model.dataobject.FilterEffect;
import com.fastclip.dao.model.dataobject.Music;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class FilterEffectUtils {

    public static FilterEffectDTO do2DTO(FilterEffect filterEffect) {
        FilterEffectDTO filterEffectDTO = new FilterEffectDTO();
        BeanUtils.copyProperties(filterEffect, filterEffectDTO);
        return filterEffectDTO;
    }

    public static List<FilterEffectDTO> do2DTOs(List<FilterEffect> filterEffects) {
        if(CollectionUtils.isEmpty(filterEffects)) {
            return new ArrayList<>();
        }
        return filterEffects.stream().map(FilterEffectUtils::do2DTO).collect(Collectors.toList());
    }
}
