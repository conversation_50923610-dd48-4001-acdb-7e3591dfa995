package com.fastclip.dao.conn;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fastclip.common.model.ao.DouyinAO;
import com.fastclip.common.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
@Slf4j
public class DouyinConnection {
    private String connUrl = "https://www.douyin.com/aweme/v1/web/search/item/?device_platform=webapp&aid=6383&channel=channel_pc_web&search_channel=aweme_video_web&sort_type=1&publish_time=0&search_source=normal_search&query_correct_type=1&is_filter_search=1&from_group_id=&pc_client_type=1&version_code=170400&version_name=17.4.0&cookie_enabled=true&screen_width=1800&screen_height=1169&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Chrome&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Mac+OS&os_version=10.15.7&cpu_core_num=8&device_memory=8&platform=PC&downlink=9.7&effective_type=4g&round_trip_time=50&webid=7326796623227635210&msToken=kqkw9m0SEPxJXHkLO9BgMAsa8oDkOgIBnAUuDcpNtTfJrTQZHcpzDKZuodeex9R4iPXpbuoH531u2ReOgK0yiqdV0LIOw-Je0XezGJpe-sbKnzqhSyNhUl5bOUA9&X-Bogus=DFSzswVLOosAN9XYtbS4cELNKBYr";
    private String cookie = "cookie -> " +
        "d_ticket=f3fb864ea496585d42c6736a968883247013a; LOGIN_STATUS=1; store-region=cn-js; store-region-src=uid; SEARCH_RESULT_LIST_TYPE=%22single%22; UIFID_TEMP=96cd3b166f3029d7c1cc3f64582454ab8a83ff1f9e6d6689076dd47ef1dca5f86b0dc10381d87ebe1d439fda4b0de905eb58c0a98df6c827cb88053f61cd1181727eee45d5e8afff5296fae3967128a2; fpk1=U2FsdGVkX1+u135gMMOkMz8VV2nFtQ8/1UIgOVODmTJt//VHTsZiVrXOZ+dY8ToaulUuG5JM2+hah8FAsPcv8g==; fpk2=10f9287deaf609ee36fb37783f2b89c0; s_v_web_id=verify_ly5nw7er_vJT3Hjd9_eREB_4gzh_9N84_KrdJDEcIlcaM; passport_csrf_token=e42ae9a407a25c49a50055ae9bae1b3f; passport_csrf_token_default=e42ae9a407a25c49a50055ae9bae1b3f; bd_ticket_guard_client_web_domain=2; UIFID=96cd3b166f3029d7c1cc3f64582454ab8a83ff1f9e6d6689076dd47ef1dca5f853ff77a775f4c111a4a1dc09174d3ed73f0ce269cfd739c81456fa6802a1ae15d06d9ffbcb50da8dd98c75cd8745685c732997ac6a1c15cd14ea660117dd214cd674cb8cd80baca7b0d4d81b04f24560eba4f9a75a098b659dff59c1e1f7cec4d9e2903d09aa0c2249b83083639d03197fc55673c05d9998925b6584614c3504; live_use_vvc=%22false%22; ttwid=1%7C2Xrj0Zc3OnTsBf-zWwt1GGNWy0sSTd3ggmECwsOAGyI%7C1722851967%7C03eb0aa5a75e5186db38fd4f3f6e7dbda3c344859462f0c7a1717908d88f967e; passport_assist_user=CjzV4lAMgyV90rVk2pLEMIiAkbvudevrmOEh5fkHO94KJwSvHMJRmm5eJyG8jJXLhe_Ih5_xyyT3jY-0c0oaSgo8EUBMa-gjlHSLv1Nje0JYEsQ1Q4EC0_0t30bOn8Hu-5taHoOA5hFvLBzeMqHgbY0REO_gH2I1WYzT7QWIEPzH2A0Yia_WVCABIgEDUcdrtg%3D%3D; n_mh=30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA; sso_uid_tt=856f0f670a843cf737deba2c4f39bfbc; sso_uid_tt_ss=856f0f670a843cf737deba2c4f39bfbc; toutiao_sso_user=732454c314e0979e8b44e7ff37620d3b; toutiao_sso_user_ss=732454c314e0979e8b44e7ff37620d3b; sid_ucp_sso_v1=1.0.0-KGJhOWY2YTYwNWRiNWIzYjRkYmJmZTc3Y2M0YTJkMWZmZjY1MDU3ZDAKHwivzJqhzQIQl83CtQYY7zEgDDC3jMfTBTgFQPsHSAYaAmxmIiA3MzI0NTRjMzE0ZTA5NzllOGI0NGU3ZmYzNzYyMGQzYg; ssid_ucp_sso_v1=1.0.0-KGJhOWY2YTYwNWRiNWIzYjRkYmJmZTc3Y2M0YTJkMWZmZjY1MDU3ZDAKHwivzJqhzQIQl83CtQYY7zEgDDC3jMfTBTgFQPsHSAYaAmxmIiA3MzI0NTRjMzE0ZTA5NzllOGI0NGU3ZmYzNzYyMGQzYg; uid_tt=5c8c3a1079ae538317cedf580303c979; uid_tt_ss=5c8c3a1079ae538317cedf580303c979; sid_tt=b60d9e8572fcbb349e1c9744ee2cd445; sessionid=b60d9e8572fcbb349e1c9744ee2cd445; sessionid_ss=b60d9e8572fcbb349e1c9744ee2cd445; is_staff_user=false; _bd_ticket_crypt_doamin=2; _bd_ticket_crypt_cookie=82a29e9a698aa0785cabebaf9e15635b; __security_server_data_status=1; sid_guard=b60d9e8572fcbb349e1c9744ee2cd445%7C1722853019%7C5183999%7CFri%2C+04-Oct-2024+10%3A16%3A58+GMT; sid_ucp_v1=1.0.0-KDlmZWYwM2NlNDU0ZTYzOTI2YWJjY2FkOWE5YTA4MzY1YmQ0OWQ3OTIKGQivzJqhzQIQm83CtQYY7zEgDDgFQPsHSAQaAmxxIiBiNjBkOWU4NTcyZmNiYjM0OWUxYzk3NDRlZTJjZDQ0NQ; ssid_ucp_v1=1.0.0-KDlmZWYwM2NlNDU0ZTYzOTI2YWJjY2FkOWE5YTA4MzY1YmQ0OWQ3OTIKGQivzJqhzQIQm83CtQYY7zEgDDgFQPsHSAQaAmxxIiBiNjBkOWU4NTcyZmNiYjM0OWUxYzk3NDRlZTJjZDQ0NQ; FRIEND_NUMBER_RED_POINT_INFO=%22MS4wLjABAAAAtvzu_ACywG0-q-nL9bEXKOVa7fSG-1FJZ6x1ck3Ujho%2F1723305600000%2F1723283582471%2F0%2F0%22; my_rd=2; msToken=UJLnhVZ3jDS6OMcq4V8qA7FmtBP9t5bmhuL9zeagFKB5nkjTmEJsaKwT7kUuI-LjSvflfR6xJ-D5GlVb3pzoynKp055EOnn6TZSWo9LTSgwKK7bMJ74=; douyin.com; device_web_cpu_core=8; device_web_memory_size=8; csrf_session_id=509b8150d5f0feb99843e106489b56fa; dy_swidth=1920; dy_sheight=1080; download_guide=%223%2F20240812%2F0%22; pwa2=%220%7C0%7C3%7C0%22; passport_fe_beating_status=true; strategyABtestKey=%************.194%22; xg_device_score=7.379406170887282; biz_trace_id=d2a12bed; volume_info=%7B%22isMute%22%3Afalse%2C%22isUserMute%22%3Afalse%2C%22volume%22%3A0.656%7D; __live_version__=%221.1.2.2679%22; webcast_local_quality=ld; __ac_nonce=066bb4d0f00bdd12c24fa; __ac_signature=_02B4Z6wo00f01hx2LewAAIDBTokN9F5CjXocVilAAOG0YnBChJKP-jmhT3VA5GXyqOqdHvhSV18usKMA.jHp8KnS0gFok6BKko.jai1weEt9.2flRxhuVIxw5WMjnfyReVDn2Kxsm8hfvtAVb2; live_can_add_dy_2_desktop=%221%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1920%2C%5C%22screen_height%5C%22%3A1080%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A250%7D%22; FOLLOW_LIVE_POINT_INFO=%22MS4wLjABAAAAtvzu_ACywG0-q-nL9bEXKOVa7fSG-1FJZ6x1ck3Ujho%2F1723564800000%2F0%2F0%2F1723551606199%22; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAAtvzu_ACywG0-q-nL9bEXKOVa7fSG-1FJZ6x1ck3Ujho%2F1723564800000%2F0%2F1723551006199%2F0%22; IsDouyinActive=true; home_can_add_dy_2_desktop=%221%22; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRkpUTEFSQVU5RTRyK0EzM1RrQTdFNnNzcktUQUorTXZPdEh0Uk1SRFA4ei9SSkZMckgyVFNOSGc5Vi9rWHVtb0lnU1hGcnI1NncrdVcxQmUzNEJpRG89IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoxfQ%3D%3D; publish_badge_show_info=%220%2C0%2C0%2C1723551064106%22; odin_tt=939fc5be932f8fbfc2bacbfe593bf2d60ddabf14e2d29421889c514c703bf99e97ded03b9d76937da55d0067d5bdb2a3; WallpaperGuide=%7B%22showTime%22%3A1723422093166%2C%22closeTime%22%3A0%2C%22showCount%22%3A1%2C%22cursor1%22%3A35%2C%22cursor2%22%3A0%7D";
    private String referer = "https://www.douyin.com/search/%E5%A5%BD%E7%89%A9%E5%88%86%E4%BA%AB%20%E5%B7%A5%E5%85%B7?type=video";

    public List<DouyinAO> getData(String keyword, Integer offset, Integer count){
        try {
            String url = connUrl + "&keyword=" + generateContent(keyword) + "&offset=" + offset + "&count=" + count;
            Map<String, String> headers = new HashMap<>();
            headers.put("cookie", cookie);
            headers.put("referer", referer);
            String dataStr = HttpUtil.sendGet(url, headers);
            JSONObject dataObject = JSON.parseObject(dataStr);
            return JSON.parseArray(dataObject.get("data").toString(), DouyinAO.class);
        }catch (Exception e) {
            log.error("get data error", e);
        }
        return new ArrayList<>();
    }

    public static String generateContent(String content) {
        // 将 content 进行 url 编码
        String ans;
        try {
            // -----------------------调整-----------------------
            ans = URLEncoder.encode(content, "UTF-8").replace("+", "%20");
        } catch (UnsupportedEncodingException e) {
            // 处理异常情况
            throw new RuntimeException(e);
        }

        return ans;
    }
}
