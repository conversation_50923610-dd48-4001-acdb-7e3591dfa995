package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.WorksDetail;
import com.fastclip.dao.model.dataobject.WorksDetailExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface WorksDetailMapper {
    long countByExample(WorksDetailExample example);

    int deleteByExample(WorksDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WorksDetail record);

    int insertSelective(WorksDetail record);

    List<WorksDetail> selectByExampleWithRowbounds(WorksDetailExample example, RowBounds rowBounds);

    List<WorksDetail> selectByExample(WorksDetailExample example);

    WorksDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") WorksDetail record, @Param("example") WorksDetailExample example);

    int updateByExample(@Param("record") WorksDetail record, @Param("example") WorksDetailExample example);

    int updateByPrimaryKeySelective(WorksDetail record);

    int updateByPrimaryKey(WorksDetail record);
}