//package com.fastclip.dao.redis;
//
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.Redisson;
//import org.redisson.api.RLock;
//import org.redisson.api.RedissonClient;
//import org.redisson.config.Config;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.util.concurrent.TimeUnit;
//
//@Slf4j
//@Service
//public class RedisLock {
//
//    RedissonClient redisson;
//
//    @PostConstruct
//    public void init(){
//        // 配置RedissonClient
//        Config config = new Config();
//        config.useSingleServer().setAddress("redis://127.0.0.1:6379");
//        redisson = Redisson.create(config);
//    }
//
//    public RLock getLock(String lockKey) {
//        // 获取锁
//        return redisson.getLock(lockKey);
//    }
//
//}
