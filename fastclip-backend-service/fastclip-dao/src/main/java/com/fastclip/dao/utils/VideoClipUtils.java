package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.dao.model.dataobject.Item;
import com.fastclip.dao.model.dataobject.VideoClip;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class VideoClipUtils {
    public static VideoClipDTO do2DTO(VideoClip videoClip) {
        VideoClipDTO videoClipDTO = new VideoClipDTO();
        BeanUtils.copyProperties(videoClip, videoClipDTO);
        return videoClipDTO;
    }

    public static List<VideoClipDTO> do2DTO(List<VideoClip> videoClips) {
        if (CollectionUtils.isEmpty(videoClips)) {
            return new ArrayList<>();
        }
        return videoClips.stream().map(VideoClipUtils::do2DTO).collect(Collectors.toList());
    }

    public static VideoClip dto2DO(VideoClipDTO videoClipDTO) {
        VideoClip videoClip = new VideoClip();
        BeanUtils.copyProperties(videoClipDTO, videoClip);
        return videoClip;
    }

    public static List<VideoClip> dto2DO(List<VideoClipDTO> videoClipDTOs) {
        if (CollectionUtils.isEmpty(videoClipDTOs)) {
            return new ArrayList<>();
        }
        return videoClipDTOs.stream().map(VideoClipUtils::dto2DO).collect(Collectors.toList());
    }
}
