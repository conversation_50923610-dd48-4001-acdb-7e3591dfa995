package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.BaseCover;
import com.fastclip.dao.model.dataobject.BaseCoverExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface BaseCoverMapper {
    long countByExample(BaseCoverExample example);

    int deleteByExample(BaseCoverExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(BaseCover record);

    int insertSelective(BaseCover record);

    List<BaseCover> selectByExampleWithRowbounds(BaseCoverExample example, RowBounds rowBounds);

    List<BaseCover> selectByExample(BaseCoverExample example);

    BaseCover selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") BaseCover record, @Param("example") BaseCoverExample example);

    int updateByExample(@Param("record") BaseCover record, @Param("example") BaseCoverExample example);

    int updateByPrimaryKeySelective(BaseCover record);

    int updateByPrimaryKey(BaseCover record);
}