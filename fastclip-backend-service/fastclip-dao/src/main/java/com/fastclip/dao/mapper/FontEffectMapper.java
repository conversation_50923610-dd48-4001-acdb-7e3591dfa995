package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.FontEffect;
import com.fastclip.dao.model.dataobject.FontEffectExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface FontEffectMapper {
    long countByExample(FontEffectExample example);

    int deleteByExample(FontEffectExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(FontEffect record);

    int insertSelective(FontEffect record);

    List<FontEffect> selectByExampleWithRowbounds(FontEffectExample example, RowBounds rowBounds);

    List<FontEffect> selectByExample(FontEffectExample example);

    FontEffect selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") FontEffect record, @Param("example") FontEffectExample example);

    int updateByExample(@Param("record") FontEffect record, @Param("example") FontEffectExample example);

    int updateByPrimaryKeySelective(FontEffect record);

    int updateByPrimaryKey(FontEffect record);
}