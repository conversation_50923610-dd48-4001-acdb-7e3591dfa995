package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.FontEffectDTO;
import com.fastclip.common.model.dto.MusicDTO;
import com.fastclip.dao.model.dataobject.FontEffect;
import com.fastclip.dao.model.dataobject.Music;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class FontEffectUtils {
    public static FontEffectDTO do2DTO(FontEffect fontEffect) {
        if(fontEffect == null) {
            return null;
        }
        FontEffectDTO fontEffectDTO = new FontEffectDTO();
        BeanUtils.copyProperties(fontEffect, fontEffectDTO);
        return fontEffectDTO;
    }

    public static List<FontEffectDTO> do2DTOs(List<FontEffect> fontEffects) {
        if(CollectionUtils.isEmpty(fontEffects)) {
            return new ArrayList<>();
        }
        return fontEffects.stream().map(FontEffectUtils::do2DTO).collect(Collectors.toList());
    }
}
