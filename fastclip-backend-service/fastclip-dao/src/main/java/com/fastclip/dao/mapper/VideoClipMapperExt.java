package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.UpdateSortReq;
import com.fastclip.dao.model.dataobject.VideoClip;
import com.fastclip.dao.model.dataobject.VideoClipExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface VideoClipMapperExt {
    int updateSort(@Param("req") UpdateSortReq req);
    int batchUpdateByPrimaryKey(@Param("list") List<VideoClip> list);
}