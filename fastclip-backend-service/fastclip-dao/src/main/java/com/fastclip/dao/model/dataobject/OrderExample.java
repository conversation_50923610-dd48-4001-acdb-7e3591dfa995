package com.fastclip.dao.model.dataobject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public OrderExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(String value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(String value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(String value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(String value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(String value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLike(String value) {
            addCriterion("product_id like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotLike(String value) {
            addCriterion("product_id not like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<String> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<String> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(String value1, String value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(String value1, String value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(String value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(String value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(String value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(String value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(String value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLike(String value) {
            addCriterion("product_name like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotLike(String value) {
            addCriterion("product_name not like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<String> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<String> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(String value1, String value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(String value1, String value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductImgIsNull() {
            addCriterion("product_img is null");
            return (Criteria) this;
        }

        public Criteria andProductImgIsNotNull() {
            addCriterion("product_img is not null");
            return (Criteria) this;
        }

        public Criteria andProductImgEqualTo(String value) {
            addCriterion("product_img =", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgNotEqualTo(String value) {
            addCriterion("product_img <>", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgGreaterThan(String value) {
            addCriterion("product_img >", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgGreaterThanOrEqualTo(String value) {
            addCriterion("product_img >=", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgLessThan(String value) {
            addCriterion("product_img <", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgLessThanOrEqualTo(String value) {
            addCriterion("product_img <=", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgLike(String value) {
            addCriterion("product_img like", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgNotLike(String value) {
            addCriterion("product_img not like", value, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgIn(List<String> values) {
            addCriterion("product_img in", values, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgNotIn(List<String> values) {
            addCriterion("product_img not in", values, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgBetween(String value1, String value2) {
            addCriterion("product_img between", value1, value2, "productImg");
            return (Criteria) this;
        }

        public Criteria andProductImgNotBetween(String value1, String value2) {
            addCriterion("product_img not between", value1, value2, "productImg");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountIsNull() {
            addCriterion("author_account is null");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountIsNotNull() {
            addCriterion("author_account is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountEqualTo(String value) {
            addCriterion("author_account =", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountNotEqualTo(String value) {
            addCriterion("author_account <>", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountGreaterThan(String value) {
            addCriterion("author_account >", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountGreaterThanOrEqualTo(String value) {
            addCriterion("author_account >=", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountLessThan(String value) {
            addCriterion("author_account <", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountLessThanOrEqualTo(String value) {
            addCriterion("author_account <=", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountLike(String value) {
            addCriterion("author_account like", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountNotLike(String value) {
            addCriterion("author_account not like", value, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountIn(List<String> values) {
            addCriterion("author_account in", values, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountNotIn(List<String> values) {
            addCriterion("author_account not in", values, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountBetween(String value1, String value2) {
            addCriterion("author_account between", value1, value2, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andAuthorAccountNotBetween(String value1, String value2) {
            addCriterion("author_account not between", value1, value2, "authorAccount");
            return (Criteria) this;
        }

        public Criteria andShopNameIsNull() {
            addCriterion("shop_name is null");
            return (Criteria) this;
        }

        public Criteria andShopNameIsNotNull() {
            addCriterion("shop_name is not null");
            return (Criteria) this;
        }

        public Criteria andShopNameEqualTo(String value) {
            addCriterion("shop_name =", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotEqualTo(String value) {
            addCriterion("shop_name <>", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameGreaterThan(String value) {
            addCriterion("shop_name >", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameGreaterThanOrEqualTo(String value) {
            addCriterion("shop_name >=", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameLessThan(String value) {
            addCriterion("shop_name <", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameLessThanOrEqualTo(String value) {
            addCriterion("shop_name <=", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameLike(String value) {
            addCriterion("shop_name like", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotLike(String value) {
            addCriterion("shop_name not like", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameIn(List<String> values) {
            addCriterion("shop_name in", values, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotIn(List<String> values) {
            addCriterion("shop_name not in", values, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameBetween(String value1, String value2) {
            addCriterion("shop_name between", value1, value2, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotBetween(String value1, String value2) {
            addCriterion("shop_name not between", value1, value2, "shopName");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountIsNull() {
            addCriterion("total_pay_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountIsNotNull() {
            addCriterion("total_pay_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountEqualTo(Long value) {
            addCriterion("total_pay_amount =", value, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountNotEqualTo(Long value) {
            addCriterion("total_pay_amount <>", value, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountGreaterThan(Long value) {
            addCriterion("total_pay_amount >", value, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("total_pay_amount >=", value, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountLessThan(Long value) {
            addCriterion("total_pay_amount <", value, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountLessThanOrEqualTo(Long value) {
            addCriterion("total_pay_amount <=", value, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountIn(List<Long> values) {
            addCriterion("total_pay_amount in", values, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountNotIn(List<Long> values) {
            addCriterion("total_pay_amount not in", values, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountBetween(Long value1, Long value2) {
            addCriterion("total_pay_amount between", value1, value2, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andTotalPayAmountNotBetween(Long value1, Long value2) {
            addCriterion("total_pay_amount not between", value1, value2, "totalPayAmount");
            return (Criteria) this;
        }

        public Criteria andCommissionRateIsNull() {
            addCriterion("commission_rate is null");
            return (Criteria) this;
        }

        public Criteria andCommissionRateIsNotNull() {
            addCriterion("commission_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCommissionRateEqualTo(Long value) {
            addCriterion("commission_rate =", value, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateNotEqualTo(Long value) {
            addCriterion("commission_rate <>", value, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateGreaterThan(Long value) {
            addCriterion("commission_rate >", value, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateGreaterThanOrEqualTo(Long value) {
            addCriterion("commission_rate >=", value, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateLessThan(Long value) {
            addCriterion("commission_rate <", value, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateLessThanOrEqualTo(Long value) {
            addCriterion("commission_rate <=", value, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateIn(List<Long> values) {
            addCriterion("commission_rate in", values, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateNotIn(List<Long> values) {
            addCriterion("commission_rate not in", values, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateBetween(Long value1, Long value2) {
            addCriterion("commission_rate between", value1, value2, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andCommissionRateNotBetween(Long value1, Long value2) {
            addCriterion("commission_rate not between", value1, value2, "commissionRate");
            return (Criteria) this;
        }

        public Criteria andFlowPointIsNull() {
            addCriterion("flow_point is null");
            return (Criteria) this;
        }

        public Criteria andFlowPointIsNotNull() {
            addCriterion("flow_point is not null");
            return (Criteria) this;
        }

        public Criteria andFlowPointEqualTo(String value) {
            addCriterion("flow_point =", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointNotEqualTo(String value) {
            addCriterion("flow_point <>", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointGreaterThan(String value) {
            addCriterion("flow_point >", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointGreaterThanOrEqualTo(String value) {
            addCriterion("flow_point >=", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointLessThan(String value) {
            addCriterion("flow_point <", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointLessThanOrEqualTo(String value) {
            addCriterion("flow_point <=", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointLike(String value) {
            addCriterion("flow_point like", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointNotLike(String value) {
            addCriterion("flow_point not like", value, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointIn(List<String> values) {
            addCriterion("flow_point in", values, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointNotIn(List<String> values) {
            addCriterion("flow_point not in", values, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointBetween(String value1, String value2) {
            addCriterion("flow_point between", value1, value2, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andFlowPointNotBetween(String value1, String value2) {
            addCriterion("flow_point not between", value1, value2, "flowPoint");
            return (Criteria) this;
        }

        public Criteria andAppIsNull() {
            addCriterion("app is null");
            return (Criteria) this;
        }

        public Criteria andAppIsNotNull() {
            addCriterion("app is not null");
            return (Criteria) this;
        }

        public Criteria andAppEqualTo(String value) {
            addCriterion("app =", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppNotEqualTo(String value) {
            addCriterion("app <>", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppGreaterThan(String value) {
            addCriterion("app >", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppGreaterThanOrEqualTo(String value) {
            addCriterion("app >=", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppLessThan(String value) {
            addCriterion("app <", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppLessThanOrEqualTo(String value) {
            addCriterion("app <=", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppLike(String value) {
            addCriterion("app like", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppNotLike(String value) {
            addCriterion("app not like", value, "app");
            return (Criteria) this;
        }

        public Criteria andAppIn(List<String> values) {
            addCriterion("app in", values, "app");
            return (Criteria) this;
        }

        public Criteria andAppNotIn(List<String> values) {
            addCriterion("app not in", values, "app");
            return (Criteria) this;
        }

        public Criteria andAppBetween(String value1, String value2) {
            addCriterion("app between", value1, value2, "app");
            return (Criteria) this;
        }

        public Criteria andAppNotBetween(String value1, String value2) {
            addCriterion("app not between", value1, value2, "app");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeIsNull() {
            addCriterion("pay_success_time is null");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeIsNotNull() {
            addCriterion("pay_success_time is not null");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeEqualTo(Date value) {
            addCriterion("pay_success_time =", value, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeNotEqualTo(Date value) {
            addCriterion("pay_success_time <>", value, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeGreaterThan(Date value) {
            addCriterion("pay_success_time >", value, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("pay_success_time >=", value, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeLessThan(Date value) {
            addCriterion("pay_success_time <", value, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeLessThanOrEqualTo(Date value) {
            addCriterion("pay_success_time <=", value, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeIn(List<Date> values) {
            addCriterion("pay_success_time in", values, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeNotIn(List<Date> values) {
            addCriterion("pay_success_time not in", values, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeBetween(Date value1, Date value2) {
            addCriterion("pay_success_time between", value1, value2, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andPaySuccessTimeNotBetween(Date value1, Date value2) {
            addCriterion("pay_success_time not between", value1, value2, "paySuccessTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeIsNull() {
            addCriterion("settle_time is null");
            return (Criteria) this;
        }

        public Criteria andSettleTimeIsNotNull() {
            addCriterion("settle_time is not null");
            return (Criteria) this;
        }

        public Criteria andSettleTimeEqualTo(Date value) {
            addCriterion("settle_time =", value, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeNotEqualTo(Date value) {
            addCriterion("settle_time <>", value, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeGreaterThan(Date value) {
            addCriterion("settle_time >", value, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("settle_time >=", value, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeLessThan(Date value) {
            addCriterion("settle_time <", value, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeLessThanOrEqualTo(Date value) {
            addCriterion("settle_time <=", value, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeIn(List<Date> values) {
            addCriterion("settle_time in", values, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeNotIn(List<Date> values) {
            addCriterion("settle_time not in", values, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeBetween(Date value1, Date value2) {
            addCriterion("settle_time between", value1, value2, "settleTime");
            return (Criteria) this;
        }

        public Criteria andSettleTimeNotBetween(Date value1, Date value2) {
            addCriterion("settle_time not between", value1, value2, "settleTime");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountIsNull() {
            addCriterion("pay_goods_amount is null");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountIsNotNull() {
            addCriterion("pay_goods_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountEqualTo(Long value) {
            addCriterion("pay_goods_amount =", value, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountNotEqualTo(Long value) {
            addCriterion("pay_goods_amount <>", value, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountGreaterThan(Long value) {
            addCriterion("pay_goods_amount >", value, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("pay_goods_amount >=", value, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountLessThan(Long value) {
            addCriterion("pay_goods_amount <", value, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountLessThanOrEqualTo(Long value) {
            addCriterion("pay_goods_amount <=", value, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountIn(List<Long> values) {
            addCriterion("pay_goods_amount in", values, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountNotIn(List<Long> values) {
            addCriterion("pay_goods_amount not in", values, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountBetween(Long value1, Long value2) {
            addCriterion("pay_goods_amount between", value1, value2, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andPayGoodsAmountNotBetween(Long value1, Long value2) {
            addCriterion("pay_goods_amount not between", value1, value2, "payGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountIsNull() {
            addCriterion("settled_goods_amount is null");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountIsNotNull() {
            addCriterion("settled_goods_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountEqualTo(Long value) {
            addCriterion("settled_goods_amount =", value, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountNotEqualTo(Long value) {
            addCriterion("settled_goods_amount <>", value, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountGreaterThan(Long value) {
            addCriterion("settled_goods_amount >", value, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("settled_goods_amount >=", value, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountLessThan(Long value) {
            addCriterion("settled_goods_amount <", value, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountLessThanOrEqualTo(Long value) {
            addCriterion("settled_goods_amount <=", value, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountIn(List<Long> values) {
            addCriterion("settled_goods_amount in", values, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountNotIn(List<Long> values) {
            addCriterion("settled_goods_amount not in", values, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountBetween(Long value1, Long value2) {
            addCriterion("settled_goods_amount between", value1, value2, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andSettledGoodsAmountNotBetween(Long value1, Long value2) {
            addCriterion("settled_goods_amount not between", value1, value2, "settledGoodsAmount");
            return (Criteria) this;
        }

        public Criteria andRealCommissionIsNull() {
            addCriterion("real_commission is null");
            return (Criteria) this;
        }

        public Criteria andRealCommissionIsNotNull() {
            addCriterion("real_commission is not null");
            return (Criteria) this;
        }

        public Criteria andRealCommissionEqualTo(Long value) {
            addCriterion("real_commission =", value, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionNotEqualTo(Long value) {
            addCriterion("real_commission <>", value, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionGreaterThan(Long value) {
            addCriterion("real_commission >", value, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionGreaterThanOrEqualTo(Long value) {
            addCriterion("real_commission >=", value, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionLessThan(Long value) {
            addCriterion("real_commission <", value, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionLessThanOrEqualTo(Long value) {
            addCriterion("real_commission <=", value, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionIn(List<Long> values) {
            addCriterion("real_commission in", values, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionNotIn(List<Long> values) {
            addCriterion("real_commission not in", values, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionBetween(Long value1, Long value2) {
            addCriterion("real_commission between", value1, value2, "realCommission");
            return (Criteria) this;
        }

        public Criteria andRealCommissionNotBetween(Long value1, Long value2) {
            addCriterion("real_commission not between", value1, value2, "realCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionIsNull() {
            addCriterion("estimated_commission is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionIsNotNull() {
            addCriterion("estimated_commission is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionEqualTo(Long value) {
            addCriterion("estimated_commission =", value, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionNotEqualTo(Long value) {
            addCriterion("estimated_commission <>", value, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionGreaterThan(Long value) {
            addCriterion("estimated_commission >", value, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionGreaterThanOrEqualTo(Long value) {
            addCriterion("estimated_commission >=", value, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionLessThan(Long value) {
            addCriterion("estimated_commission <", value, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionLessThanOrEqualTo(Long value) {
            addCriterion("estimated_commission <=", value, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionIn(List<Long> values) {
            addCriterion("estimated_commission in", values, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionNotIn(List<Long> values) {
            addCriterion("estimated_commission not in", values, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionBetween(Long value1, Long value2) {
            addCriterion("estimated_commission between", value1, value2, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedCommissionNotBetween(Long value1, Long value2) {
            addCriterion("estimated_commission not between", value1, value2, "estimatedCommission");
            return (Criteria) this;
        }

        public Criteria andItemNumIsNull() {
            addCriterion("item_num is null");
            return (Criteria) this;
        }

        public Criteria andItemNumIsNotNull() {
            addCriterion("item_num is not null");
            return (Criteria) this;
        }

        public Criteria andItemNumEqualTo(Long value) {
            addCriterion("item_num =", value, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumNotEqualTo(Long value) {
            addCriterion("item_num <>", value, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumGreaterThan(Long value) {
            addCriterion("item_num >", value, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumGreaterThanOrEqualTo(Long value) {
            addCriterion("item_num >=", value, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumLessThan(Long value) {
            addCriterion("item_num <", value, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumLessThanOrEqualTo(Long value) {
            addCriterion("item_num <=", value, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumIn(List<Long> values) {
            addCriterion("item_num in", values, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumNotIn(List<Long> values) {
            addCriterion("item_num not in", values, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumBetween(Long value1, Long value2) {
            addCriterion("item_num between", value1, value2, "itemNum");
            return (Criteria) this;
        }

        public Criteria andItemNumNotBetween(Long value1, Long value2) {
            addCriterion("item_num not between", value1, value2, "itemNum");
            return (Criteria) this;
        }

        public Criteria andShopIdIsNull() {
            addCriterion("shop_id is null");
            return (Criteria) this;
        }

        public Criteria andShopIdIsNotNull() {
            addCriterion("shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andShopIdEqualTo(Long value) {
            addCriterion("shop_id =", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotEqualTo(Long value) {
            addCriterion("shop_id <>", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdGreaterThan(Long value) {
            addCriterion("shop_id >", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("shop_id >=", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdLessThan(Long value) {
            addCriterion("shop_id <", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdLessThanOrEqualTo(Long value) {
            addCriterion("shop_id <=", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdIn(List<Long> values) {
            addCriterion("shop_id in", values, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotIn(List<Long> values) {
            addCriterion("shop_id not in", values, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdBetween(Long value1, Long value2) {
            addCriterion("shop_id between", value1, value2, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotBetween(Long value1, Long value2) {
            addCriterion("shop_id not between", value1, value2, "shopId");
            return (Criteria) this;
        }

        public Criteria andRefundTimeIsNull() {
            addCriterion("refund_time is null");
            return (Criteria) this;
        }

        public Criteria andRefundTimeIsNotNull() {
            addCriterion("refund_time is not null");
            return (Criteria) this;
        }

        public Criteria andRefundTimeEqualTo(Date value) {
            addCriterion("refund_time =", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeNotEqualTo(Date value) {
            addCriterion("refund_time <>", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeGreaterThan(Date value) {
            addCriterion("refund_time >", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("refund_time >=", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeLessThan(Date value) {
            addCriterion("refund_time <", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeLessThanOrEqualTo(Date value) {
            addCriterion("refund_time <=", value, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeIn(List<Date> values) {
            addCriterion("refund_time in", values, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeNotIn(List<Date> values) {
            addCriterion("refund_time not in", values, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeBetween(Date value1, Date value2) {
            addCriterion("refund_time between", value1, value2, "refundTime");
            return (Criteria) this;
        }

        public Criteria andRefundTimeNotBetween(Date value1, Date value2) {
            addCriterion("refund_time not between", value1, value2, "refundTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionIsNull() {
            addCriterion("estimated_total_commission is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionIsNotNull() {
            addCriterion("estimated_total_commission is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionEqualTo(Long value) {
            addCriterion("estimated_total_commission =", value, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionNotEqualTo(Long value) {
            addCriterion("estimated_total_commission <>", value, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionGreaterThan(Long value) {
            addCriterion("estimated_total_commission >", value, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionGreaterThanOrEqualTo(Long value) {
            addCriterion("estimated_total_commission >=", value, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionLessThan(Long value) {
            addCriterion("estimated_total_commission <", value, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionLessThanOrEqualTo(Long value) {
            addCriterion("estimated_total_commission <=", value, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionIn(List<Long> values) {
            addCriterion("estimated_total_commission in", values, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionNotIn(List<Long> values) {
            addCriterion("estimated_total_commission not in", values, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionBetween(Long value1, Long value2) {
            addCriterion("estimated_total_commission between", value1, value2, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTotalCommissionNotBetween(Long value1, Long value2) {
            addCriterion("estimated_total_commission not between", value1, value2, "estimatedTotalCommission");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeIsNull() {
            addCriterion("estimated_tech_service_fee is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeIsNotNull() {
            addCriterion("estimated_tech_service_fee is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeEqualTo(Long value) {
            addCriterion("estimated_tech_service_fee =", value, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeNotEqualTo(Long value) {
            addCriterion("estimated_tech_service_fee <>", value, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeGreaterThan(Long value) {
            addCriterion("estimated_tech_service_fee >", value, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeGreaterThanOrEqualTo(Long value) {
            addCriterion("estimated_tech_service_fee >=", value, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeLessThan(Long value) {
            addCriterion("estimated_tech_service_fee <", value, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeLessThanOrEqualTo(Long value) {
            addCriterion("estimated_tech_service_fee <=", value, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeIn(List<Long> values) {
            addCriterion("estimated_tech_service_fee in", values, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeNotIn(List<Long> values) {
            addCriterion("estimated_tech_service_fee not in", values, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeBetween(Long value1, Long value2) {
            addCriterion("estimated_tech_service_fee between", value1, value2, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andEstimatedTechServiceFeeNotBetween(Long value1, Long value2) {
            addCriterion("estimated_tech_service_fee not between", value1, value2, "estimatedTechServiceFee");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyIsNull() {
            addCriterion("pick_source_client_key is null");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyIsNotNull() {
            addCriterion("pick_source_client_key is not null");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyEqualTo(String value) {
            addCriterion("pick_source_client_key =", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyNotEqualTo(String value) {
            addCriterion("pick_source_client_key <>", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyGreaterThan(String value) {
            addCriterion("pick_source_client_key >", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyGreaterThanOrEqualTo(String value) {
            addCriterion("pick_source_client_key >=", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyLessThan(String value) {
            addCriterion("pick_source_client_key <", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyLessThanOrEqualTo(String value) {
            addCriterion("pick_source_client_key <=", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyLike(String value) {
            addCriterion("pick_source_client_key like", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyNotLike(String value) {
            addCriterion("pick_source_client_key not like", value, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyIn(List<String> values) {
            addCriterion("pick_source_client_key in", values, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyNotIn(List<String> values) {
            addCriterion("pick_source_client_key not in", values, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyBetween(String value1, String value2) {
            addCriterion("pick_source_client_key between", value1, value2, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickSourceClientKeyNotBetween(String value1, String value2) {
            addCriterion("pick_source_client_key not between", value1, value2, "pickSourceClientKey");
            return (Criteria) this;
        }

        public Criteria andPickExtraIsNull() {
            addCriterion("pick_extra is null");
            return (Criteria) this;
        }

        public Criteria andPickExtraIsNotNull() {
            addCriterion("pick_extra is not null");
            return (Criteria) this;
        }

        public Criteria andPickExtraEqualTo(String value) {
            addCriterion("pick_extra =", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraNotEqualTo(String value) {
            addCriterion("pick_extra <>", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraGreaterThan(String value) {
            addCriterion("pick_extra >", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraGreaterThanOrEqualTo(String value) {
            addCriterion("pick_extra >=", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraLessThan(String value) {
            addCriterion("pick_extra <", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraLessThanOrEqualTo(String value) {
            addCriterion("pick_extra <=", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraLike(String value) {
            addCriterion("pick_extra like", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraNotLike(String value) {
            addCriterion("pick_extra not like", value, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraIn(List<String> values) {
            addCriterion("pick_extra in", values, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraNotIn(List<String> values) {
            addCriterion("pick_extra not in", values, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraBetween(String value1, String value2) {
            addCriterion("pick_extra between", value1, value2, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andPickExtraNotBetween(String value1, String value2) {
            addCriterion("pick_extra not between", value1, value2, "pickExtra");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdIsNull() {
            addCriterion("author_short_id is null");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdIsNotNull() {
            addCriterion("author_short_id is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdEqualTo(String value) {
            addCriterion("author_short_id =", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdNotEqualTo(String value) {
            addCriterion("author_short_id <>", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdGreaterThan(String value) {
            addCriterion("author_short_id >", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdGreaterThanOrEqualTo(String value) {
            addCriterion("author_short_id >=", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdLessThan(String value) {
            addCriterion("author_short_id <", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdLessThanOrEqualTo(String value) {
            addCriterion("author_short_id <=", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdLike(String value) {
            addCriterion("author_short_id like", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdNotLike(String value) {
            addCriterion("author_short_id not like", value, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdIn(List<String> values) {
            addCriterion("author_short_id in", values, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdNotIn(List<String> values) {
            addCriterion("author_short_id not in", values, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdBetween(String value1, String value2) {
            addCriterion("author_short_id between", value1, value2, "authorShortId");
            return (Criteria) this;
        }

        public Criteria andAuthorShortIdNotBetween(String value1, String value2) {
            addCriterion("author_short_id not between", value1, value2, "authorShortId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}