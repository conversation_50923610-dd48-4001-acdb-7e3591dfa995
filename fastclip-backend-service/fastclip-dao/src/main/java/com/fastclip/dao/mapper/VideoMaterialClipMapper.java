package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.VideoMaterialClip;
import com.fastclip.dao.model.dataobject.VideoMaterialClipExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface VideoMaterialClipMapper {
    int countByExample(VideoMaterialClipExample example);

    int deleteByExample(VideoMaterialClipExample example);

    int deleteByPrimaryKey(Long id);

    int insert(VideoMaterialClip record);

    int insertSelective(VideoMaterialClip record);

    List<VideoMaterialClip> selectByExample(VideoMaterialClipExample example);

    VideoMaterialClip selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") VideoMaterialClip record, @Param("example") VideoMaterialClipExample example);

    int updateByExample(@Param("record") VideoMaterialClip record, @Param("example") VideoMaterialClipExample example);

    int updateByPrimaryKeySelective(VideoMaterialClip record);

    int updateByPrimaryKey(VideoMaterialClip record);
}