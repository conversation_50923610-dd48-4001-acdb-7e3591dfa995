//package com.fastclip.dao.redis;
//
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.dao.DataAccessException;
//import org.springframework.data.redis.connection.RedisConnection;
//import org.springframework.data.redis.connection.RedisZSetCommands;
//import org.springframework.data.redis.core.RedisCallback;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.serializer.RedisSerializer;
//import org.springframework.stereotype.Component;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
//@Component
//@Slf4j
//public class RedisClient {
//
//    @Autowired
//    RedisConfig redisConfig;
//
//    @Autowired
//    private RedisTemplate cacheRedisTemplate;
//
//
//    public String get(String key) {
//        RedisSerializer serializer = cacheRedisTemplate.getStringSerializer();
//        Object result = cacheRedisTemplate.execute(new RedisCallback<Object>() {
//            @Override
//            public Object doInRedis(RedisConnection connection) throws DataAccessException {
//                byte[] value = connection.get(serializer.serialize(key));
//                if (value == null) {
//                    return null;
//                }
//                return serializer.deserialize(value);
//            }
//        });
//        return (String) result;
//    }
//
//    public List<String> multiGet(List<String> keys) {
//        RedisSerializer serializer = cacheRedisTemplate.getStringSerializer();
//        List<String> result = cacheRedisTemplate.executePipelined(new RedisCallback<Object>() {
//            @Override
//            public List<String> doInRedis(RedisConnection connection) throws DataAccessException {
//                keys.forEach(key -> {
//                    connection.get(serializer.serialize(key));
//                });
//                return null;
//            }
//        }, serializer);
//        return  result;
//    }
//
//    public void addAll(String key, Set<Object> values) {
//        cacheRedisTemplate.opsForSet().add(key, values);
//    }
//
//    public void add(String key, Object value) {
//        cacheRedisTemplate.opsForSet().add(key, value);
//    }
//
//
//    public void remove(String key, Object value) {
//        cacheRedisTemplate.opsForSet().remove(key, value);
//    }
//
//    public Object pop(Object key) {
//        return cacheRedisTemplate.opsForSet().pop(key);
//    }
//
//    public Long getSize(String key) {
//        return cacheRedisTemplate.opsForSet().size(key);
//    }
//}
