package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.BaseCoverDTO;
import com.fastclip.common.model.dto.StickerDTO;
import com.fastclip.dao.model.dataobject.BaseCover;
import com.fastclip.dao.model.dataobject.BaseCoverExample;
import com.fastclip.dao.model.dataobject.Sticker;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class BaseCoverUtils {

    public static BaseCoverDTO do2DTO(BaseCover baseCover) {
        BaseCoverDTO baseCoverDTO = new BaseCoverDTO();
        BeanUtils.copyProperties(baseCover, baseCoverDTO);
        return baseCoverDTO;
    }

    public static List<BaseCoverDTO> do2DTOs(List<BaseCover> baseCovers) {
        if(CollectionUtils.isEmpty(baseCovers)) {
            return new ArrayList<>();
        }
        return baseCovers.stream().map(BaseCoverUtils::do2DTO).collect(Collectors.toList());
    }
}
