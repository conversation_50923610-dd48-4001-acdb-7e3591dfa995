//package com.fastclip.dao.redis;
//
//
//import com.fasterxml.jackson.annotation.JsonAutoDetect;
//import com.fasterxml.jackson.annotation.PropertyAccessor;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.Data;
//import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.RedisClusterConfiguration;
//import org.springframework.data.redis.connection.RedisPassword;
//import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
//import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
//import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
//import org.springframework.data.redis.serializer.StringRedisSerializer;
//
//import java.time.Duration;
//import java.util.List;
//
//@Configuration
//@EnableConfigurationProperties(value = { RedisProperties.class })
//@Data
//public class RedisConfig {
//    @Value("${redis.hosts}")
//    private List<String> hostNames;
//
//    @Value("${redis.instanceId}")
//    private String instanceId;
//
//    @Value("${redis.password}")
//    private String password;
//
//    @Value("${redis.lettuce.pool.max-idle}")
//    private Integer maxIdle;
//
//    @Value("${redis.lettuce.pool.min-idle}")
//    private Integer minIdle;
//
//    @Value("${redis.lettuce.pool.max-active}")
//    private Integer maxActive;
//
//    @Value("${redis.lettuce.pool.max-wait}")
//    private Long maxWait;
//
//    @Value("${redis.timeout}")
//    private Long timeOut;
//
//    @Value("${redis.lettuce.shutdown-timeout}")
//    private Long shutdownTimeOut;
//
//    @Value("${redis.expireTime}")
//    private Long expireTme;
//
//    @Value("${redis.prefix}")
//    private String prefix;
//
//    /**
//     * 配置 Jackson2JsonRedisSerializer 序列化器，在配置 redisTemplate需要用来做k,v的
//     * 序列化器
//     */
//    static Jackson2JsonRedisSerializer getJackson2JsonRedisSerializer() {
//        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = null;
//        jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
//        ObjectMapper om = new ObjectMapper();
//        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
//        jackson2JsonRedisSerializer.setObjectMapper(om);
//        return jackson2JsonRedisSerializer;
//    }
//
//    static final Jackson2JsonRedisSerializer jackson2JsonRedisSerializer =
//            getJackson2JsonRedisSerializer();
//
//
//    @Bean(value = "connectionFactory")
//    public LettuceConnectionFactory createLettuceConnectionFactory() {
//
//        //redis配置
//        RedisClusterConfiguration redisConfiguration = new RedisClusterConfiguration(hostNames);
//        if(instanceId != null) {
//            redisConfiguration.getClusterNodes().stream().forEach(redisNode -> redisNode.setId(instanceId));
//        }
//        if(password != null) {
//            redisConfiguration.setPassword(RedisPassword.of(password));
//        }
//        //连接池配置
//        GenericObjectPoolConfig genericObjectPoolConfig =
//                new GenericObjectPoolConfig();
//        genericObjectPoolConfig.setMaxIdle(maxIdle);
//        genericObjectPoolConfig.setMinIdle(minIdle);
//        genericObjectPoolConfig.setMaxTotal(maxActive);
//        genericObjectPoolConfig.setMaxWaitMillis(maxWait);
//
//        //redis客户端配置
//        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder
//                builder = LettucePoolingClientConfiguration.builder().
//                commandTimeout(Duration.ofMillis(timeOut));
//
//        builder.shutdownTimeout(Duration.ofMillis(shutdownTimeOut));
//        builder.poolConfig(genericObjectPoolConfig);
//        LettuceClientConfiguration lettuceClientConfiguration = builder.build();
//
//        //根据配置和客户端配置创建连接
//        LettuceConnectionFactory lettuceConnectionFactory = new
//                LettuceConnectionFactory(redisConfiguration, lettuceClientConfiguration);
//
//
//        return lettuceConnectionFactory;
//    }
//
//
//    /**
//     * 配置 cache RedisTemplate
//     *
//     * @return RedisTemplate<String, Serializable>r
//     */
//    @Bean(value = "cacheRedisTemplate")
//    public RedisTemplate<String, Object> getCacheRedisTemplate(@Qualifier("connectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
//        //创建客户端连接
//        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
//        redisTemplate.setConnectionFactory(lettuceConnectionFactory);
//        /**
//         * 使用 String 作为 Key 的序列化器,使用 Jackson 作为 Value 的序列化器
//         */
//        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
//        // key采用String的序列化方式
//        redisTemplate.setKeySerializer(stringRedisSerializer);
//        // hash的key也采用String的序列化方式
//        redisTemplate.setHashKeySerializer(stringRedisSerializer);
//        // value序列化方式采用jackson
//        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
//        // hash的value序列化方式采用jackson
//        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);
//        redisTemplate.afterPropertiesSet();
//        return redisTemplate;
//    }
//
//
//}