package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.DownloadLog;
import com.fastclip.dao.model.dataobject.DownloadLogExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface DownloadLogMapper {
    long countByExample(DownloadLogExample example);

    int deleteByExample(DownloadLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DownloadLog record);

    int insertSelective(DownloadLog record);

    List<DownloadLog> selectByExampleWithRowbounds(DownloadLogExample example, RowBounds rowBounds);

    List<DownloadLog> selectByExample(DownloadLogExample example);

    DownloadLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DownloadLog record, @Param("example") DownloadLogExample example);

    int updateByExample(@Param("record") DownloadLog record, @Param("example") DownloadLogExample example);

    int updateByPrimaryKeySelective(DownloadLog record);

    int updateByPrimaryKey(DownloadLog record);
}