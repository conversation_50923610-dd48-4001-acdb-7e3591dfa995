package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.DouyinAccount;
import com.fastclip.dao.model.dataobject.DouyinAccountExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface DouyinAccountMapper {
    long countByExample(DouyinAccountExample example);

    int deleteByExample(DouyinAccountExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DouyinAccount record);

    int insertSelective(DouyinAccount record);

    List<DouyinAccount> selectByExampleWithRowbounds(DouyinAccountExample example, RowBounds rowBounds);

    List<DouyinAccount> selectByExample(DouyinAccountExample example);

    DouyinAccount selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DouyinAccount record, @Param("example") DouyinAccountExample example);

    int updateByExample(@Param("record") DouyinAccount record, @Param("example") DouyinAccountExample example);

    int updateByPrimaryKeySelective(DouyinAccount record);

    int updateByPrimaryKey(DouyinAccount record);
}