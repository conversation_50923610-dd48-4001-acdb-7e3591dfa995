package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class Orders {
    private Long id;

    private String orderId;

    private String productId;

    private String productName;

    private String productImg;

    private String authorAccount;

    private String shopName;

    private Long totalPayAmount;

    private Long commissionRate;

    private String flowPoint;

    private String app;

    private Date updateTime;

    private Date paySuccessTime;

    private Date settleTime;

    private Long payGoodsAmount;

    private Long settledGoodsAmount;

    private Long realCommission;

    private Long estimatedCommission;

    private Long itemNum;

    private Long shopId;

    private Date refundTime;

    private Long estimatedTotalCommission;

    private Long estimatedTechServiceFee;

    private String pickSourceClientKey;

    private String pickExtra;

    private String authorShortId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductImg() {
        return productImg;
    }

    public void setProductImg(String productImg) {
        this.productImg = productImg == null ? null : productImg.trim();
    }

    public String getAuthorAccount() {
        return authorAccount;
    }

    public void setAuthorAccount(String authorAccount) {
        this.authorAccount = authorAccount == null ? null : authorAccount.trim();
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName == null ? null : shopName.trim();
    }

    public Long getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(Long totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }

    public Long getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(Long commissionRate) {
        this.commissionRate = commissionRate;
    }

    public String getFlowPoint() {
        return flowPoint;
    }

    public void setFlowPoint(String flowPoint) {
        this.flowPoint = flowPoint == null ? null : flowPoint.trim();
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app == null ? null : app.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getPaySuccessTime() {
        return paySuccessTime;
    }

    public void setPaySuccessTime(Date paySuccessTime) {
        this.paySuccessTime = paySuccessTime;
    }

    public Date getSettleTime() {
        return settleTime;
    }

    public void setSettleTime(Date settleTime) {
        this.settleTime = settleTime;
    }

    public Long getPayGoodsAmount() {
        return payGoodsAmount;
    }

    public void setPayGoodsAmount(Long payGoodsAmount) {
        this.payGoodsAmount = payGoodsAmount;
    }

    public Long getSettledGoodsAmount() {
        return settledGoodsAmount;
    }

    public void setSettledGoodsAmount(Long settledGoodsAmount) {
        this.settledGoodsAmount = settledGoodsAmount;
    }

    public Long getRealCommission() {
        return realCommission;
    }

    public void setRealCommission(Long realCommission) {
        this.realCommission = realCommission;
    }

    public Long getEstimatedCommission() {
        return estimatedCommission;
    }

    public void setEstimatedCommission(Long estimatedCommission) {
        this.estimatedCommission = estimatedCommission;
    }

    public Long getItemNum() {
        return itemNum;
    }

    public void setItemNum(Long itemNum) {
        this.itemNum = itemNum;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    public Long getEstimatedTotalCommission() {
        return estimatedTotalCommission;
    }

    public void setEstimatedTotalCommission(Long estimatedTotalCommission) {
        this.estimatedTotalCommission = estimatedTotalCommission;
    }

    public Long getEstimatedTechServiceFee() {
        return estimatedTechServiceFee;
    }

    public void setEstimatedTechServiceFee(Long estimatedTechServiceFee) {
        this.estimatedTechServiceFee = estimatedTechServiceFee;
    }

    public String getPickSourceClientKey() {
        return pickSourceClientKey;
    }

    public void setPickSourceClientKey(String pickSourceClientKey) {
        this.pickSourceClientKey = pickSourceClientKey == null ? null : pickSourceClientKey.trim();
    }

    public String getPickExtra() {
        return pickExtra;
    }

    public void setPickExtra(String pickExtra) {
        this.pickExtra = pickExtra == null ? null : pickExtra.trim();
    }

    public String getAuthorShortId() {
        return authorShortId;
    }

    public void setAuthorShortId(String authorShortId) {
        this.authorShortId = authorShortId == null ? null : authorShortId.trim();
    }
}