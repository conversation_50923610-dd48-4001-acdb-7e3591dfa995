package com.fastclip.dao.mapper;

import com.fastclip.common.model.dataobject.VideoClipDO;
import com.fastclip.common.model.dataobject.ChanxuanCutClipDO;
import com.fastclip.common.model.dataobject.ChanxuanItemDO;
import com.fastclip.common.model.request.VideoClipsReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChanxuanMapper {

    void insertOrUpdateCxClip(@Param("chanxuanClipDOs") List<VideoClipDO> chanxuanClipDOs);

    void insertOrUpdateCxItem(@Param("chanxuanItems") List<ChanxuanItemDO> chanxuanItemDOS);

    void insertCxCutClip(@Param("chanxuanCutClipDOS") List<ChanxuanCutClipDO> chanxuanCutClipDOS);

    ChanxuanItemDO getItem(@Param("productId") String productId);

    ChanxuanItemDO getItemByMaterialId(@Param("materialId") String materialId);


    List<ChanxuanItemDO> getItemsNoCut(String cxAccount);

    VideoClipDO getClipByTaskId(@Param("taskId") String taskId);

    ChanxuanCutClipDO getCutClipByTaskId(@Param("taskId") String taskId);

    List<VideoClipDO> getClipsByProductId(@Param("productId") String productId);

    int getCountClipsByProductId(@Param("productId") String productId);

    List<VideoClipDO> getClipWithoutSrt(@Param("limit") Integer limit);

    List<VideoClipDO> getClipWithoutSrtByProductId(@Param("productId") String productId);

    List<VideoClipDO> getAllClips();

    List<VideoClipDO> getClipsByPage(@Param("req") VideoClipsReq req);

    List<VideoClipDO> getAllClipsNoCut(@Param("cxAccount") String cxAccount);

    void deleteClip(@Param("id") Integer id);

    void deleteItem(@Param("id") Integer id);

    List<ChanxuanItemDO> getAllItems();
}
