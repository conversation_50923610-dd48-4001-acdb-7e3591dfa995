package com.fastclip.dao.mapper;

import static org.apache.ibatis.jdbc.SqlBuilder.BEGIN;
import static org.apache.ibatis.jdbc.SqlBuilder.DELETE_FROM;
import static org.apache.ibatis.jdbc.SqlBuilder.FROM;
import static org.apache.ibatis.jdbc.SqlBuilder.INSERT_INTO;
import static org.apache.ibatis.jdbc.SqlBuilder.ORDER_BY;
import static org.apache.ibatis.jdbc.SqlBuilder.SELECT;
import static org.apache.ibatis.jdbc.SqlBuilder.SELECT_DISTINCT;
import static org.apache.ibatis.jdbc.SqlBuilder.SET;
import static org.apache.ibatis.jdbc.SqlBuilder.SQL;
import static org.apache.ibatis.jdbc.SqlBuilder.UPDATE;
import static org.apache.ibatis.jdbc.SqlBuilder.VALUES;
import static org.apache.ibatis.jdbc.SqlBuilder.WHERE;

import com.fastclip.dao.model.dataobject.VideoMaterial;
import com.fastclip.dao.model.dataobject.VideoMaterialExample.Criteria;
import com.fastclip.dao.model.dataobject.VideoMaterialExample.Criterion;
import com.fastclip.dao.model.dataobject.VideoMaterialExample;
import java.util.List;
import java.util.Map;

public class VideoMaterialSqlProvider {

    public String countByExample(VideoMaterialExample example) {
        BEGIN();
        SELECT("count(*)");
        FROM("video_material");
        applyWhere(example, false);
        return SQL();
    }

    public String deleteByExample(VideoMaterialExample example) {
        BEGIN();
        DELETE_FROM("video_material");
        applyWhere(example, false);
        return SQL();
    }

    public String insertSelective(VideoMaterial record) {
        BEGIN();
        INSERT_INTO("video_material");
        
        if (record.getId() != null) {
            VALUES("id", "#{id,jdbcType=BIGINT}");
        }
        
        if (record.getSellerId() != null) {
            VALUES("seller_id", "#{sellerId,jdbcType=BIGINT}");
        }
        
        if (record.getDuration() != null) {
            VALUES("duration", "#{duration,jdbcType=INTEGER}");
        }
        
        if (record.getPath() != null) {
            VALUES("path", "#{path,jdbcType=VARCHAR}");
        }
        
        if (record.getSize() != null) {
            VALUES("size", "#{size,jdbcType=INTEGER}");
        }
        
        if (record.getIsSubtitlesDone() != null) {
            VALUES("is_subtitles_done", "#{isSubtitlesDone,jdbcType=BIT}");
        }
        
        if (record.getSubtitlesBpTs() != null) {
            VALUES("subtitles_bp_ts", "#{subtitlesBpTs,jdbcType=INTEGER}");
        }
        
        if (record.getCreateTime() != null) {
            VALUES("create_time", "#{createTime,jdbcType=TIMESTAMP}");
        }
        
        if (record.getUpdateTime() != null) {
            VALUES("update_time", "#{updateTime,jdbcType=TIMESTAMP}");
        }
        
        return SQL();
    }

    public String selectByExample(VideoMaterialExample example) {
        BEGIN();
        if (example != null && example.isDistinct()) {
            SELECT_DISTINCT("id");
        } else {
            SELECT("id");
        }
        SELECT("seller_id");
        SELECT("duration");
        SELECT("path");
        SELECT("size");
        SELECT("is_subtitles_done");
        SELECT("subtitles_bp_ts");
        SELECT("create_time");
        SELECT("update_time");
        FROM("video_material");
        applyWhere(example, false);
        
        if (example != null && example.getOrderByClause() != null) {
            ORDER_BY(example.getOrderByClause());
        }
        
        return SQL();
    }

    public String updateByExampleSelective(Map<String, Object> parameter) {
        VideoMaterial record = (VideoMaterial) parameter.get("record");
        VideoMaterialExample example = (VideoMaterialExample) parameter.get("example");
        
        BEGIN();
        UPDATE("video_material");
        
        if (record.getId() != null) {
            SET("id = #{record.id,jdbcType=BIGINT}");
        }
        
        if (record.getSellerId() != null) {
            SET("seller_id = #{record.sellerId,jdbcType=BIGINT}");
        }
        
        if (record.getDuration() != null) {
            SET("duration = #{record.duration,jdbcType=INTEGER}");
        }
        
        if (record.getPath() != null) {
            SET("path = #{record.path,jdbcType=VARCHAR}");
        }
        
        if (record.getSize() != null) {
            SET("size = #{record.size,jdbcType=INTEGER}");
        }
        
        if (record.getIsSubtitlesDone() != null) {
            SET("is_subtitles_done = #{record.isSubtitlesDone,jdbcType=BIT}");
        }
        
        if (record.getSubtitlesBpTs() != null) {
            SET("subtitles_bp_ts = #{record.subtitlesBpTs,jdbcType=INTEGER}");
        }
        
        if (record.getCreateTime() != null) {
            SET("create_time = #{record.createTime,jdbcType=TIMESTAMP}");
        }
        
        if (record.getUpdateTime() != null) {
            SET("update_time = #{record.updateTime,jdbcType=TIMESTAMP}");
        }
        
        applyWhere(example, true);
        return SQL();
    }

    public String updateByExample(Map<String, Object> parameter) {
        BEGIN();
        UPDATE("video_material");
        
        SET("id = #{record.id,jdbcType=BIGINT}");
        SET("seller_id = #{record.sellerId,jdbcType=BIGINT}");
        SET("duration = #{record.duration,jdbcType=INTEGER}");
        SET("path = #{record.path,jdbcType=VARCHAR}");
        SET("size = #{record.size,jdbcType=INTEGER}");
        SET("is_subtitles_done = #{record.isSubtitlesDone,jdbcType=BIT}");
        SET("subtitles_bp_ts = #{record.subtitlesBpTs,jdbcType=INTEGER}");
        SET("create_time = #{record.createTime,jdbcType=TIMESTAMP}");
        SET("update_time = #{record.updateTime,jdbcType=TIMESTAMP}");
        
        VideoMaterialExample example = (VideoMaterialExample) parameter.get("example");
        applyWhere(example, true);
        return SQL();
    }

    public String updateByPrimaryKeySelective(VideoMaterial record) {
        BEGIN();
        UPDATE("video_material");
        
        if (record.getSellerId() != null) {
            SET("seller_id = #{sellerId,jdbcType=BIGINT}");
        }
        
        if (record.getDuration() != null) {
            SET("duration = #{duration,jdbcType=INTEGER}");
        }
        
        if (record.getPath() != null) {
            SET("path = #{path,jdbcType=VARCHAR}");
        }
        
        if (record.getSize() != null) {
            SET("size = #{size,jdbcType=INTEGER}");
        }
        
        if (record.getIsSubtitlesDone() != null) {
            SET("is_subtitles_done = #{isSubtitlesDone,jdbcType=BIT}");
        }
        
        if (record.getSubtitlesBpTs() != null) {
            SET("subtitles_bp_ts = #{subtitlesBpTs,jdbcType=INTEGER}");
        }
        
        if (record.getCreateTime() != null) {
            SET("create_time = #{createTime,jdbcType=TIMESTAMP}");
        }
        
        if (record.getUpdateTime() != null) {
            SET("update_time = #{updateTime,jdbcType=TIMESTAMP}");
        }
        
        WHERE("id = #{id,jdbcType=BIGINT}");
        
        return SQL();
    }

    protected void applyWhere(VideoMaterialExample example, boolean includeExamplePhrase) {
        if (example == null) {
            return;
        }
        
        String parmPhrase1;
        String parmPhrase1_th;
        String parmPhrase2;
        String parmPhrase2_th;
        String parmPhrase3;
        String parmPhrase3_th;
        if (includeExamplePhrase) {
            parmPhrase1 = "%s #{example.oredCriteria[%d].allCriteria[%d].value}";
            parmPhrase1_th = "%s #{example.oredCriteria[%d].allCriteria[%d].value,typeHandler=%s}";
            parmPhrase2 = "%s #{example.oredCriteria[%d].allCriteria[%d].value} and #{example.oredCriteria[%d].criteria[%d].secondValue}";
            parmPhrase2_th = "%s #{example.oredCriteria[%d].allCriteria[%d].value,typeHandler=%s} and #{example.oredCriteria[%d].criteria[%d].secondValue,typeHandler=%s}";
            parmPhrase3 = "#{example.oredCriteria[%d].allCriteria[%d].value[%d]}";
            parmPhrase3_th = "#{example.oredCriteria[%d].allCriteria[%d].value[%d],typeHandler=%s}";
        } else {
            parmPhrase1 = "%s #{oredCriteria[%d].allCriteria[%d].value}";
            parmPhrase1_th = "%s #{oredCriteria[%d].allCriteria[%d].value,typeHandler=%s}";
            parmPhrase2 = "%s #{oredCriteria[%d].allCriteria[%d].value} and #{oredCriteria[%d].criteria[%d].secondValue}";
            parmPhrase2_th = "%s #{oredCriteria[%d].allCriteria[%d].value,typeHandler=%s} and #{oredCriteria[%d].criteria[%d].secondValue,typeHandler=%s}";
            parmPhrase3 = "#{oredCriteria[%d].allCriteria[%d].value[%d]}";
            parmPhrase3_th = "#{oredCriteria[%d].allCriteria[%d].value[%d],typeHandler=%s}";
        }
        
        StringBuilder sb = new StringBuilder();
        List<Criteria> oredCriteria = example.getOredCriteria();
        boolean firstCriteria = true;
        for (int i = 0; i < oredCriteria.size(); i++) {
            Criteria criteria = oredCriteria.get(i);
            if (criteria.isValid()) {
                if (firstCriteria) {
                    firstCriteria = false;
                } else {
                    sb.append(" or ");
                }
                
                sb.append('(');
                List<Criterion> criterions = criteria.getAllCriteria();
                boolean firstCriterion = true;
                for (int j = 0; j < criterions.size(); j++) {
                    Criterion criterion = criterions.get(j);
                    if (firstCriterion) {
                        firstCriterion = false;
                    } else {
                        sb.append(" and ");
                    }
                    
                    if (criterion.isNoValue()) {
                        sb.append(criterion.getCondition());
                    } else if (criterion.isSingleValue()) {
                        if (criterion.getTypeHandler() == null) {
                            sb.append(String.format(parmPhrase1, criterion.getCondition(), i, j));
                        } else {
                            sb.append(String.format(parmPhrase1_th, criterion.getCondition(), i, j,criterion.getTypeHandler()));
                        }
                    } else if (criterion.isBetweenValue()) {
                        if (criterion.getTypeHandler() == null) {
                            sb.append(String.format(parmPhrase2, criterion.getCondition(), i, j, i, j));
                        } else {
                            sb.append(String.format(parmPhrase2_th, criterion.getCondition(), i, j, criterion.getTypeHandler(), i, j, criterion.getTypeHandler()));
                        }
                    } else if (criterion.isListValue()) {
                        sb.append(criterion.getCondition());
                        sb.append(" (");
                        List<?> listItems = (List<?>) criterion.getValue();
                        boolean comma = false;
                        for (int k = 0; k < listItems.size(); k++) {
                            if (comma) {
                                sb.append(", ");
                            } else {
                                comma = true;
                            }
                            if (criterion.getTypeHandler() == null) {
                                sb.append(String.format(parmPhrase3, i, j, k));
                            } else {
                                sb.append(String.format(parmPhrase3_th, i, j, k, criterion.getTypeHandler()));
                            }
                        }
                        sb.append(')');
                    }
                }
                sb.append(')');
            }
        }
        
        if (sb.length() > 0) {
            WHERE(sb.toString());
        }
    }
}