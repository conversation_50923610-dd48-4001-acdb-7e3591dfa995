package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.Subtitles;
import com.fastclip.dao.model.dataobject.SubtitlesExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SubtitlesMapper {
    int countByExample(SubtitlesExample example);

    int deleteByExample(SubtitlesExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Subtitles record);

    int insertSelective(Subtitles record);

    List<Subtitles> selectByExample(SubtitlesExample example);

    Subtitles selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") Subtitles record, @Param("example") SubtitlesExample example);

    int updateByExample(@Param("record") Subtitles record, @Param("example") SubtitlesExample example);

    int updateByPrimaryKeySelective(Subtitles record);

    int updateByPrimaryKey(Subtitles record);
}