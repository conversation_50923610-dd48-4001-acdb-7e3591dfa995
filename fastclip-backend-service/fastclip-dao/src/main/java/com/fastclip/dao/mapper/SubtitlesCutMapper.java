package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.SubtitlesCut;
import com.fastclip.dao.model.dataobject.SubtitlesCutExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface SubtitlesCutMapper {
    long countByExample(SubtitlesCutExample example);

    int deleteByExample(SubtitlesCutExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SubtitlesCut record);

    int insertSelective(SubtitlesCut record);

    List<SubtitlesCut> selectByExampleWithRowbounds(SubtitlesCutExample example, RowBounds rowBounds);

    List<SubtitlesCut> selectByExample(SubtitlesCutExample example);

    SubtitlesCut selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SubtitlesCut record, @Param("example") SubtitlesCutExample example);

    int updateByExample(@Param("record") SubtitlesCut record, @Param("example") SubtitlesCutExample example);

    int updateByPrimaryKeySelective(SubtitlesCut record);

    int updateByPrimaryKey(SubtitlesCut record);
}