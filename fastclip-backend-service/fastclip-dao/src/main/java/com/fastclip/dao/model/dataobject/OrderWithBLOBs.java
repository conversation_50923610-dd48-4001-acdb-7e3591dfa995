package com.fastclip.dao.model.dataobject;

public class OrderWithBLOBs extends Orders {
    private String productName;

    private String productImg;

    private String pickExtra;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductImg() {
        return productImg;
    }

    public void setProductImg(String productImg) {
        this.productImg = productImg == null ? null : productImg.trim();
    }

    public String getPickExtra() {
        return pickExtra;
    }

    public void setPickExtra(String pickExtra) {
        this.pickExtra = pickExtra == null ? null : pickExtra.trim();
    }
}