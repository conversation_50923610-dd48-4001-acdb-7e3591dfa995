package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class SubtitlesCut {
    private Long id;

    private Long projectId;

    private Long videoId;

    private Long subtitlesId;

    private String cutStartContent;

    private String cutEndContent;

    private String content;

    private Integer cutStartTs;

    private Integer cutEndTs;

    private Integer duration;

    private Integer length;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public Long getSubtitlesId() {
        return subtitlesId;
    }

    public void setSubtitlesId(Long subtitlesId) {
        this.subtitlesId = subtitlesId;
    }

    public String getCutStartContent() {
        return cutStartContent;
    }

    public void setCutStartContent(String cutStartContent) {
        this.cutStartContent = cutStartContent == null ? null : cutStartContent.trim();
    }

    public String getCutEndContent() {
        return cutEndContent;
    }

    public void setCutEndContent(String cutEndContent) {
        this.cutEndContent = cutEndContent == null ? null : cutEndContent.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Integer getCutStartTs() {
        return cutStartTs;
    }

    public void setCutStartTs(Integer cutStartTs) {
        this.cutStartTs = cutStartTs;
    }

    public Integer getCutEndTs() {
        return cutEndTs;
    }

    public void setCutEndTs(Integer cutEndTs) {
        this.cutEndTs = cutEndTs;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}