package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.QRCodeAPIDTO;
import com.fastclip.common.model.dto.QRCodeDTO;
import com.fastclip.dao.model.dataobject.QRCode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class QRCodeUtils {
    public static QRCodeDTO do2DTO(QRCode qrCode) {
        if(qrCode == null) {
            return null;
        }
        QRCodeDTO qrCodeDTO = new QRCodeDTO();
        BeanUtils.copyProperties(qrCode, qrCodeDTO);
        return qrCodeDTO;
    }

    public static QRCode dto2DO(QRCodeDTO qrCodeDTO) {
        QRCode qrCode = new QRCode();
        BeanUtils.copyProperties(qrCodeDTO, qrCode);
        return qrCode;
    }

    public static List<QRCodeDTO> do2DTO(List<QRCode> qrCodes) {
        if (CollectionUtils.isEmpty(qrCodes)) {
            return new ArrayList<>();
        }
        return qrCodes.stream().map(QRCodeUtils::do2DTO).collect(Collectors.toList());
    }

    public static List<QRCode> dto2DO(List<QRCodeDTO> qrCodeDTOS) {
        if (CollectionUtils.isEmpty(qrCodeDTOS)) {
            return new ArrayList<>();
        }
        return qrCodeDTOS.stream().map(QRCodeUtils::dto2DO).collect(Collectors.toList());
    }

    public static QRCodeDTO apiDTO2DTO(QRCodeAPIDTO qrCodeAPIDTO) {
        if(qrCodeAPIDTO == null) {
            return null;
        }
        QRCodeDTO qrCodeDTO = new QRCodeDTO();
        qrCodeDTO.setErrorCode(qrCodeAPIDTO.getError_code());
        qrCodeDTO.setCaptcha(qrCodeAPIDTO.getCaptcha());
        qrCodeDTO.setDescription(qrCodeAPIDTO.getDescription());
        qrCodeDTO.setDescUrl(qrCodeAPIDTO.getDesc_url());
        qrCodeDTO.setQrcode(qrCodeAPIDTO.getQrcode());
        qrCodeDTO.setIsFrontier(qrCodeAPIDTO.getIs_frontier());
        qrCodeDTO.setQrcodeIndexUrl(qrCodeAPIDTO.getQrcode_index_url());
        qrCodeDTO.setToken(qrCodeAPIDTO.getToken());
        return qrCodeDTO;
    }



    public static List<QRCodeDTO> apiDTO2DTO(List<QRCodeAPIDTO> qrCodeAPIDTOS) {
        if (CollectionUtils.isEmpty(qrCodeAPIDTOS)) {
            return new ArrayList<>();
        }
        return qrCodeAPIDTOS.stream().map(QRCodeUtils::apiDTO2DTO).collect(Collectors.toList());
    }

}
