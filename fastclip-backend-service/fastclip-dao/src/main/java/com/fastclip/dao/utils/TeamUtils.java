package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.ApiOrdersDTO;
import com.fastclip.common.model.dto.OrdersDTO;
import com.fastclip.common.model.dto.TeamDTO;
import com.fastclip.common.utils.NumberUtils;
import com.fastclip.dao.model.dataobject.Orders;
import com.fastclip.dao.model.dataobject.Team;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class TeamUtils {
    public static TeamDTO do2DTO(Team team) {
        if(team == null) {
            return null;
        }

        TeamDTO teamDTO = new TeamDTO();
        BeanUtils.copyProperties(team, teamDTO);
        return teamDTO;
    }

    public static Team dto2DO(TeamDTO teamDTO) {
        Team team = new Team();
        BeanUtils.copyProperties(teamDTO, team);
        return team;
    }

    public static List<TeamDTO> do2DTO(List<Team> teams) {
        if (CollectionUtils.isEmpty(teams)) {
            return new ArrayList<>();
        }
        return teams.stream().map(TeamUtils::do2DTO).collect(Collectors.toList());
    }

    public static List<Team> dto2DO(List<TeamDTO> teamDTOS) {
        if (CollectionUtils.isEmpty(teamDTOS)) {
            return new ArrayList<>();
        }
        return teamDTOS.stream().map(TeamUtils::dto2DO).collect(Collectors.toList());
    }
}
