package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class DouyinAccount {
    private Long id;

    private String phone;

    private Long sellerId;

    private String douyinName;

    private Date createTime;

    private Date updateTime;

    private String coverContainerPath;

    private String coverBackground;

    private String code;

    private String accessToken;

    private Date expireTime;

    private Date expiresIn;

    private String refreshToken;

    private Date refreshExpiresIn;

    private String openId;

    private String inviteToken;

    private Boolean inviteDone;

    private Integer type;

    private Long teamId;

    private Long selfId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getDouyinName() {
        return douyinName;
    }

    public void setDouyinName(String douyinName) {
        this.douyinName = douyinName == null ? null : douyinName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCoverContainerPath() {
        return coverContainerPath;
    }

    public void setCoverContainerPath(String coverContainerPath) {
        this.coverContainerPath = coverContainerPath == null ? null : coverContainerPath.trim();
    }

    public String getCoverBackground() {
        return coverBackground;
    }

    public void setCoverBackground(String coverBackground) {
        this.coverBackground = coverBackground == null ? null : coverBackground.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken == null ? null : accessToken.trim();
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Date getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Date expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken == null ? null : refreshToken.trim();
    }

    public Date getRefreshExpiresIn() {
        return refreshExpiresIn;
    }

    public void setRefreshExpiresIn(Date refreshExpiresIn) {
        this.refreshExpiresIn = refreshExpiresIn;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId == null ? null : openId.trim();
    }

    public String getInviteToken() {
        return inviteToken;
    }

    public void setInviteToken(String inviteToken) {
        this.inviteToken = inviteToken == null ? null : inviteToken.trim();
    }

    public Boolean getInviteDone() {
        return inviteDone;
    }

    public void setInviteDone(Boolean inviteDone) {
        this.inviteDone = inviteDone;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getSelfId() {
        return selfId;
    }

    public void setSelfId(Long selfId) {
        this.selfId = selfId;
    }
}