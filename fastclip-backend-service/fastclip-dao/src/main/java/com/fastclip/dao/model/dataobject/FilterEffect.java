package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class FilterEffect {
    private Integer id;

    private String name;

    private Integer saturate;

    private Integer lightness;

    private Integer contrast;

    private Integer shadow;

    private Integer highlight;

    private Integer temperature;

    private Date createTime;

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getSaturate() {
        return saturate;
    }

    public void setSaturate(Integer saturate) {
        this.saturate = saturate;
    }

    public Integer getLightness() {
        return lightness;
    }

    public void setLightness(Integer lightness) {
        this.lightness = lightness;
    }

    public Integer getContrast() {
        return contrast;
    }

    public void setContrast(Integer contrast) {
        this.contrast = contrast;
    }

    public Integer getShadow() {
        return shadow;
    }

    public void setShadow(Integer shadow) {
        this.shadow = shadow;
    }

    public Integer getHighlight() {
        return highlight;
    }

    public void setHighlight(Integer highlight) {
        this.highlight = highlight;
    }

    public Integer getTemperature() {
        return temperature;
    }

    public void setTemperature(Integer temperature) {
        this.temperature = temperature;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}