package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.FUser;
import com.fastclip.dao.model.dataobject.FUserExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface FUserMapper {
    long countByExample(FUserExample example);

    int deleteByExample(FUserExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FUser record);

    int insertSelective(FUser record);

    List<FUser> selectByExampleWithRowbounds(FUserExample example, RowBounds rowBounds);

    List<FUser> selectByExample(FUserExample example);

    FUser selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FUser record, @Param("example") FUserExample example);

    int updateByExample(@Param("record") FUser record, @Param("example") FUserExample example);

    int updateByPrimaryKeySelective(FUser record);

    int updateByPrimaryKey(FUser record);
}