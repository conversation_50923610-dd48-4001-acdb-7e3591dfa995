package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class VideoMaterialClip {
    private Long id;

    private Long itemId;

    private Long videoId;

    private Long sellerId;

    private Integer duration;

    private Integer startTs;

    private Integer endTs;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getStartTs() {
        return startTs;
    }

    public void setStartTs(Integer startTs) {
        this.startTs = startTs;
    }

    public Integer getEndTs() {
        return endTs;
    }

    public void setEndTs(Integer endTs) {
        this.endTs = endTs;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}