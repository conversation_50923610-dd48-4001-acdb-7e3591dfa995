package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class Works {
    private Long id;

    private Long projectId;

    private Long itemId;

    private Long sellerId;

    private Integer duration;

    private String specialEffectsTemplateCode;

    private Boolean isComposed;

    private Boolean isPublished;

    private String videoPath;

    private Integer musicId;

    private Integer filterId;

    private Integer stickerId;

    private Integer speedUp;

    private Date createTime;

    private Date updateTime;

    private String worksName;

    private String worksDesc;

    private String worksCover;

    private String simpleItemName;

    private String worksTag;

    private Long accountId;

    private String phone;

    private Date publishedTime;

    private String itemFeatures;

    private String itemTags;

    private Long creatorId;

    private Integer fontId;

    private String keywords;

    private Boolean isDescCompleted;

    private Integer leftTopStickerId;

    private Integer rightTopStickerId;

    private Integer leftBottomStickerId;

    private Integer rightBottomStickerId;

    private String videoPathWithoutAss;

    private Integer composeStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getSpecialEffectsTemplateCode() {
        return specialEffectsTemplateCode;
    }

    public void setSpecialEffectsTemplateCode(String specialEffectsTemplateCode) {
        this.specialEffectsTemplateCode = specialEffectsTemplateCode == null ? null : specialEffectsTemplateCode.trim();
    }

    public Boolean getIsComposed() {
        return isComposed;
    }

    public void setIsComposed(Boolean isComposed) {
        this.isComposed = isComposed;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public String getVideoPath() {
        return videoPath;
    }

    public void setVideoPath(String videoPath) {
        this.videoPath = videoPath == null ? null : videoPath.trim();
    }

    public Integer getMusicId() {
        return musicId;
    }

    public void setMusicId(Integer musicId) {
        this.musicId = musicId;
    }

    public Integer getFilterId() {
        return filterId;
    }

    public void setFilterId(Integer filterId) {
        this.filterId = filterId;
    }

    public Integer getStickerId() {
        return stickerId;
    }

    public void setStickerId(Integer stickerId) {
        this.stickerId = stickerId;
    }

    public Integer getSpeedUp() {
        return speedUp;
    }

    public void setSpeedUp(Integer speedUp) {
        this.speedUp = speedUp;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getWorksName() {
        return worksName;
    }

    public void setWorksName(String worksName) {
        this.worksName = worksName == null ? null : worksName.trim();
    }

    public String getWorksDesc() {
        return worksDesc;
    }

    public void setWorksDesc(String worksDesc) {
        this.worksDesc = worksDesc == null ? null : worksDesc.trim();
    }

    public String getWorksCover() {
        return worksCover;
    }

    public void setWorksCover(String worksCover) {
        this.worksCover = worksCover == null ? null : worksCover.trim();
    }

    public String getSimpleItemName() {
        return simpleItemName;
    }

    public void setSimpleItemName(String simpleItemName) {
        this.simpleItemName = simpleItemName == null ? null : simpleItemName.trim();
    }

    public String getWorksTag() {
        return worksTag;
    }

    public void setWorksTag(String worksTag) {
        this.worksTag = worksTag == null ? null : worksTag.trim();
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public Date getPublishedTime() {
        return publishedTime;
    }

    public void setPublishedTime(Date publishedTime) {
        this.publishedTime = publishedTime;
    }

    public String getItemFeatures() {
        return itemFeatures;
    }

    public void setItemFeatures(String itemFeatures) {
        this.itemFeatures = itemFeatures == null ? null : itemFeatures.trim();
    }

    public String getItemTags() {
        return itemTags;
    }

    public void setItemTags(String itemTags) {
        this.itemTags = itemTags == null ? null : itemTags.trim();
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Integer getFontId() {
        return fontId;
    }

    public void setFontId(Integer fontId) {
        this.fontId = fontId;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords == null ? null : keywords.trim();
    }

    public Boolean getIsDescCompleted() {
        return isDescCompleted;
    }

    public void setIsDescCompleted(Boolean isDescCompleted) {
        this.isDescCompleted = isDescCompleted;
    }

    public Integer getLeftTopStickerId() {
        return leftTopStickerId;
    }

    public void setLeftTopStickerId(Integer leftTopStickerId) {
        this.leftTopStickerId = leftTopStickerId;
    }

    public Integer getRightTopStickerId() {
        return rightTopStickerId;
    }

    public void setRightTopStickerId(Integer rightTopStickerId) {
        this.rightTopStickerId = rightTopStickerId;
    }

    public Integer getLeftBottomStickerId() {
        return leftBottomStickerId;
    }

    public void setLeftBottomStickerId(Integer leftBottomStickerId) {
        this.leftBottomStickerId = leftBottomStickerId;
    }

    public Integer getRightBottomStickerId() {
        return rightBottomStickerId;
    }

    public void setRightBottomStickerId(Integer rightBottomStickerId) {
        this.rightBottomStickerId = rightBottomStickerId;
    }

    public String getVideoPathWithoutAss() {
        return videoPathWithoutAss;
    }

    public void setVideoPathWithoutAss(String videoPathWithoutAss) {
        this.videoPathWithoutAss = videoPathWithoutAss == null ? null : videoPathWithoutAss.trim();
    }

    public Integer getComposeStatus() {
        return composeStatus;
    }

    public void setComposeStatus(Integer composeStatus) {
        this.composeStatus = composeStatus;
    }
}