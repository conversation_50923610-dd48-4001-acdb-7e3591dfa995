package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.Orders;
import com.fastclip.dao.model.dataobject.OrderExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface OrdersMapper {
    long countByExample(OrderExample example);

    int deleteByExample(OrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Orders record);

    int insertSelective(Orders record);

    List<Orders> selectByExampleWithRowbounds(OrderExample example, RowBounds rowBounds);

    List<Orders> selectByExample(OrderExample example);

    Orders selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") Orders record, @Param("example") OrderExample example);

    int updateByExample(@Param("record") Orders record, @Param("example") OrderExample example);

    int updateByPrimaryKeySelective(Orders record);

    int updateByPrimaryKey(Orders record);
}