package com.fastclip.dao.mapper;

import com.fastclip.common.model.dataobject.OrderDO;
import com.fastclip.common.model.request.DouyinAccountByTeamReq;
import com.fastclip.common.model.request.OrderCountReq;
import com.fastclip.common.model.response.DouyinAccountByTeamRes;
import com.fastclip.dao.model.dataobject.Orders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DouyinAccountMapperExt {

    List<DouyinAccountByTeamRes> countByTeamId(@Param("req")DouyinAccountByTeamReq req);

}