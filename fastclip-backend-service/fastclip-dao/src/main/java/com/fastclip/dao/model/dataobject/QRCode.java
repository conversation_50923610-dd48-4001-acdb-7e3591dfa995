package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class QRCode {
    private Long id;

    private Integer status;

    private Long douyinAccountId;

    private String captcha;

    private String descUrl;

    private String description;

    private Integer errorCode;

    private Boolean isFrontier;

    private String qrcodeIndexUrl;

    private String token;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getDouyinAccountId() {
        return douyinAccountId;
    }

    public void setDouyinAccountId(Long douyinAccountId) {
        this.douyinAccountId = douyinAccountId;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha == null ? null : captcha.trim();
    }

    public String getDescUrl() {
        return descUrl;
    }

    public void setDescUrl(String descUrl) {
        this.descUrl = descUrl == null ? null : descUrl.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public Boolean getIsFrontier() {
        return isFrontier;
    }

    public void setIsFrontier(Boolean isFrontier) {
        this.isFrontier = isFrontier;
    }

    public String getQrcodeIndexUrl() {
        return qrcodeIndexUrl;
    }

    public void setQrcodeIndexUrl(String qrcodeIndexUrl) {
        this.qrcodeIndexUrl = qrcodeIndexUrl == null ? null : qrcodeIndexUrl.trim();
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token == null ? null : token.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}