package com.fastclip.dao.mapper;

import com.fastclip.common.model.request.ItemReq;
import com.fastclip.dao.model.dataobject.Item;
import com.fastclip.dao.model.dataobject.ItemExample;
import com.fastclip.dao.model.dataobject.ItemExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface ItemMapperExt {
    List<ItemExt> getItems(@Param("req")ItemReq req);

    Integer count(@Param("req")ItemReq req);

    List<ItemExt> getLiveItems(@Param("req")ItemReq req);
}