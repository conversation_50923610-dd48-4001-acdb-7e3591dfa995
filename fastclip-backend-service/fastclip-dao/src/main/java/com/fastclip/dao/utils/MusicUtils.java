package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.MusicDTO;
import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.common.model.dto.WorksDetailDTO;
import com.fastclip.dao.model.dataobject.Music;
import com.fastclip.dao.model.dataobject.Works;
import com.fastclip.dao.model.dataobject.WorksDetail;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class MusicUtils {

    public static MusicDTO do2DTO(Music music) {
        MusicDTO musicDTO = new MusicDTO();
        BeanUtils.copyProperties(music, musicDTO);
        return musicDTO;
    }

    public static List<MusicDTO> do2DTOs(List<Music> musics) {
        if(CollectionUtils.isEmpty(musics)) {
            return new ArrayList<>();
        }
        return musics.stream().map(MusicUtils::do2DTO).collect(Collectors.toList());
    }
}
