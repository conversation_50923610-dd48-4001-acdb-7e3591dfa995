package com.fastclip.dao.model.dataobject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VideoMaterialExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public VideoMaterialExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNull() {
            addCriterion("seller_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNotNull() {
            addCriterion("seller_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerIdEqualTo(Long value) {
            addCriterion("seller_id =", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotEqualTo(Long value) {
            addCriterion("seller_id <>", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThan(Long value) {
            addCriterion("seller_id >", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("seller_id >=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThan(Long value) {
            addCriterion("seller_id <", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThanOrEqualTo(Long value) {
            addCriterion("seller_id <=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdIn(List<Long> values) {
            addCriterion("seller_id in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotIn(List<Long> values) {
            addCriterion("seller_id not in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdBetween(Long value1, Long value2) {
            addCriterion("seller_id between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotBetween(Long value1, Long value2) {
            addCriterion("seller_id not between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Integer value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Integer value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Integer value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Integer value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Integer value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Integer> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Integer> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Integer value1, Integer value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andPathIsNull() {
            addCriterion("path is null");
            return (Criteria) this;
        }

        public Criteria andPathIsNotNull() {
            addCriterion("path is not null");
            return (Criteria) this;
        }

        public Criteria andPathEqualTo(String value) {
            addCriterion("path =", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotEqualTo(String value) {
            addCriterion("path <>", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathGreaterThan(String value) {
            addCriterion("path >", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathGreaterThanOrEqualTo(String value) {
            addCriterion("path >=", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathLessThan(String value) {
            addCriterion("path <", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathLessThanOrEqualTo(String value) {
            addCriterion("path <=", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathLike(String value) {
            addCriterion("path like", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotLike(String value) {
            addCriterion("path not like", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathIn(List<String> values) {
            addCriterion("path in", values, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotIn(List<String> values) {
            addCriterion("path not in", values, "path");
            return (Criteria) this;
        }

        public Criteria andPathBetween(String value1, String value2) {
            addCriterion("path between", value1, value2, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotBetween(String value1, String value2) {
            addCriterion("path not between", value1, value2, "path");
            return (Criteria) this;
        }

        public Criteria andSizeIsNull() {
            addCriterion("size is null");
            return (Criteria) this;
        }

        public Criteria andSizeIsNotNull() {
            addCriterion("size is not null");
            return (Criteria) this;
        }

        public Criteria andSizeEqualTo(Integer value) {
            addCriterion("size =", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotEqualTo(Integer value) {
            addCriterion("size <>", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThan(Integer value) {
            addCriterion("size >", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("size >=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThan(Integer value) {
            addCriterion("size <", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThanOrEqualTo(Integer value) {
            addCriterion("size <=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeIn(List<Integer> values) {
            addCriterion("size in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotIn(List<Integer> values) {
            addCriterion("size not in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeBetween(Integer value1, Integer value2) {
            addCriterion("size between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("size not between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneIsNull() {
            addCriterion("is_subtitles_done is null");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneIsNotNull() {
            addCriterion("is_subtitles_done is not null");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneEqualTo(Boolean value) {
            addCriterion("is_subtitles_done =", value, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneNotEqualTo(Boolean value) {
            addCriterion("is_subtitles_done <>", value, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneGreaterThan(Boolean value) {
            addCriterion("is_subtitles_done >", value, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_subtitles_done >=", value, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneLessThan(Boolean value) {
            addCriterion("is_subtitles_done <", value, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneLessThanOrEqualTo(Boolean value) {
            addCriterion("is_subtitles_done <=", value, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneIn(List<Boolean> values) {
            addCriterion("is_subtitles_done in", values, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneNotIn(List<Boolean> values) {
            addCriterion("is_subtitles_done not in", values, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneBetween(Boolean value1, Boolean value2) {
            addCriterion("is_subtitles_done between", value1, value2, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andIsSubtitlesDoneNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_subtitles_done not between", value1, value2, "isSubtitlesDone");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsIsNull() {
            addCriterion("subtitles_bp_ts is null");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsIsNotNull() {
            addCriterion("subtitles_bp_ts is not null");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsEqualTo(Integer value) {
            addCriterion("subtitles_bp_ts =", value, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsNotEqualTo(Integer value) {
            addCriterion("subtitles_bp_ts <>", value, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsGreaterThan(Integer value) {
            addCriterion("subtitles_bp_ts >", value, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsGreaterThanOrEqualTo(Integer value) {
            addCriterion("subtitles_bp_ts >=", value, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsLessThan(Integer value) {
            addCriterion("subtitles_bp_ts <", value, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsLessThanOrEqualTo(Integer value) {
            addCriterion("subtitles_bp_ts <=", value, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsIn(List<Integer> values) {
            addCriterion("subtitles_bp_ts in", values, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsNotIn(List<Integer> values) {
            addCriterion("subtitles_bp_ts not in", values, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsBetween(Integer value1, Integer value2) {
            addCriterion("subtitles_bp_ts between", value1, value2, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBpTsNotBetween(Integer value1, Integer value2) {
            addCriterion("subtitles_bp_ts not between", value1, value2, "subtitlesBpTs");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andVideoNameIsNull() {
            addCriterion("video_name is null");
            return (Criteria) this;
        }

        public Criteria andVideoNameIsNotNull() {
            addCriterion("video_name is not null");
            return (Criteria) this;
        }

        public Criteria andVideoNameEqualTo(String value) {
            addCriterion("video_name =", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameNotEqualTo(String value) {
            addCriterion("video_name <>", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameGreaterThan(String value) {
            addCriterion("video_name >", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameGreaterThanOrEqualTo(String value) {
            addCriterion("video_name >=", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameLessThan(String value) {
            addCriterion("video_name <", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameLessThanOrEqualTo(String value) {
            addCriterion("video_name <=", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameLike(String value) {
            addCriterion("video_name like", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameNotLike(String value) {
            addCriterion("video_name not like", value, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameIn(List<String> values) {
            addCriterion("video_name in", values, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameNotIn(List<String> values) {
            addCriterion("video_name not in", values, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameBetween(String value1, String value2) {
            addCriterion("video_name between", value1, value2, "videoName");
            return (Criteria) this;
        }

        public Criteria andVideoNameNotBetween(String value1, String value2) {
            addCriterion("video_name not between", value1, value2, "videoName");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Integer value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Integer value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Integer value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Integer value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Integer value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Integer> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Integer> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Integer value1, Integer value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andVideoTypeIsNull() {
            addCriterion("video_type is null");
            return (Criteria) this;
        }

        public Criteria andVideoTypeIsNotNull() {
            addCriterion("video_type is not null");
            return (Criteria) this;
        }

        public Criteria andVideoTypeEqualTo(Integer value) {
            addCriterion("video_type =", value, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeNotEqualTo(Integer value) {
            addCriterion("video_type <>", value, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeGreaterThan(Integer value) {
            addCriterion("video_type >", value, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("video_type >=", value, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeLessThan(Integer value) {
            addCriterion("video_type <", value, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeLessThanOrEqualTo(Integer value) {
            addCriterion("video_type <=", value, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeIn(List<Integer> values) {
            addCriterion("video_type in", values, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeNotIn(List<Integer> values) {
            addCriterion("video_type not in", values, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeBetween(Integer value1, Integer value2) {
            addCriterion("video_type between", value1, value2, "videoType");
            return (Criteria) this;
        }

        public Criteria andVideoTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("video_type not between", value1, value2, "videoType");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdIsNull() {
            addCriterion("live_room_id is null");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdIsNotNull() {
            addCriterion("live_room_id is not null");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdEqualTo(String value) {
            addCriterion("live_room_id =", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdNotEqualTo(String value) {
            addCriterion("live_room_id <>", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdGreaterThan(String value) {
            addCriterion("live_room_id >", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdGreaterThanOrEqualTo(String value) {
            addCriterion("live_room_id >=", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdLessThan(String value) {
            addCriterion("live_room_id <", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdLessThanOrEqualTo(String value) {
            addCriterion("live_room_id <=", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdLike(String value) {
            addCriterion("live_room_id like", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdNotLike(String value) {
            addCriterion("live_room_id not like", value, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdIn(List<String> values) {
            addCriterion("live_room_id in", values, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdNotIn(List<String> values) {
            addCriterion("live_room_id not in", values, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdBetween(String value1, String value2) {
            addCriterion("live_room_id between", value1, value2, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andLiveRoomIdNotBetween(String value1, String value2) {
            addCriterion("live_room_id not between", value1, value2, "liveRoomId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdIsNull() {
            addCriterion("latest_slice_merged_id is null");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdIsNotNull() {
            addCriterion("latest_slice_merged_id is not null");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdEqualTo(Integer value) {
            addCriterion("latest_slice_merged_id =", value, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdNotEqualTo(Integer value) {
            addCriterion("latest_slice_merged_id <>", value, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdGreaterThan(Integer value) {
            addCriterion("latest_slice_merged_id >", value, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("latest_slice_merged_id >=", value, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdLessThan(Integer value) {
            addCriterion("latest_slice_merged_id <", value, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdLessThanOrEqualTo(Integer value) {
            addCriterion("latest_slice_merged_id <=", value, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdIn(List<Integer> values) {
            addCriterion("latest_slice_merged_id in", values, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdNotIn(List<Integer> values) {
            addCriterion("latest_slice_merged_id not in", values, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdBetween(Integer value1, Integer value2) {
            addCriterion("latest_slice_merged_id between", value1, value2, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andLatestSliceMergedIdNotBetween(Integer value1, Integer value2) {
            addCriterion("latest_slice_merged_id not between", value1, value2, "latestSliceMergedId");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andCombineStatusIsNull() {
            addCriterion("combine_status is null");
            return (Criteria) this;
        }

        public Criteria andCombineStatusIsNotNull() {
            addCriterion("combine_status is not null");
            return (Criteria) this;
        }

        public Criteria andCombineStatusEqualTo(Integer value) {
            addCriterion("combine_status =", value, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusNotEqualTo(Integer value) {
            addCriterion("combine_status <>", value, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusGreaterThan(Integer value) {
            addCriterion("combine_status >", value, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("combine_status >=", value, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusLessThan(Integer value) {
            addCriterion("combine_status <", value, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusLessThanOrEqualTo(Integer value) {
            addCriterion("combine_status <=", value, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusIn(List<Integer> values) {
            addCriterion("combine_status in", values, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusNotIn(List<Integer> values) {
            addCriterion("combine_status not in", values, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusBetween(Integer value1, Integer value2) {
            addCriterion("combine_status between", value1, value2, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andCombineStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("combine_status not between", value1, value2, "combineStatus");
            return (Criteria) this;
        }

        public Criteria andStartSceneIsNull() {
            addCriterion("start_scene is null");
            return (Criteria) this;
        }

        public Criteria andStartSceneIsNotNull() {
            addCriterion("start_scene is not null");
            return (Criteria) this;
        }

        public Criteria andStartSceneEqualTo(Integer value) {
            addCriterion("start_scene =", value, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneNotEqualTo(Integer value) {
            addCriterion("start_scene <>", value, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneGreaterThan(Integer value) {
            addCriterion("start_scene >", value, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneGreaterThanOrEqualTo(Integer value) {
            addCriterion("start_scene >=", value, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneLessThan(Integer value) {
            addCriterion("start_scene <", value, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneLessThanOrEqualTo(Integer value) {
            addCriterion("start_scene <=", value, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneIn(List<Integer> values) {
            addCriterion("start_scene in", values, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneNotIn(List<Integer> values) {
            addCriterion("start_scene not in", values, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneBetween(Integer value1, Integer value2) {
            addCriterion("start_scene between", value1, value2, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneNotBetween(Integer value1, Integer value2) {
            addCriterion("start_scene not between", value1, value2, "startScene");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagIsNull() {
            addCriterion("start_scene_flag is null");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagIsNotNull() {
            addCriterion("start_scene_flag is not null");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagEqualTo(Boolean value) {
            addCriterion("start_scene_flag =", value, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagNotEqualTo(Boolean value) {
            addCriterion("start_scene_flag <>", value, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagGreaterThan(Boolean value) {
            addCriterion("start_scene_flag >", value, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("start_scene_flag >=", value, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagLessThan(Boolean value) {
            addCriterion("start_scene_flag <", value, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("start_scene_flag <=", value, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagIn(List<Boolean> values) {
            addCriterion("start_scene_flag in", values, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagNotIn(List<Boolean> values) {
            addCriterion("start_scene_flag not in", values, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("start_scene_flag between", value1, value2, "startSceneFlag");
            return (Criteria) this;
        }

        public Criteria andStartSceneFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("start_scene_flag not between", value1, value2, "startSceneFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}