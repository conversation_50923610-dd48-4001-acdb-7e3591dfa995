package com.fastclip.dao.model.dataobject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VideoClipExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public VideoClipExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdIsNull() {
            addCriterion("start_subtitles_cut_id is null");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdIsNotNull() {
            addCriterion("start_subtitles_cut_id is not null");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdEqualTo(Long value) {
            addCriterion("start_subtitles_cut_id =", value, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdNotEqualTo(Long value) {
            addCriterion("start_subtitles_cut_id <>", value, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdGreaterThan(Long value) {
            addCriterion("start_subtitles_cut_id >", value, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdGreaterThanOrEqualTo(Long value) {
            addCriterion("start_subtitles_cut_id >=", value, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdLessThan(Long value) {
            addCriterion("start_subtitles_cut_id <", value, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdLessThanOrEqualTo(Long value) {
            addCriterion("start_subtitles_cut_id <=", value, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdIn(List<Long> values) {
            addCriterion("start_subtitles_cut_id in", values, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdNotIn(List<Long> values) {
            addCriterion("start_subtitles_cut_id not in", values, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdBetween(Long value1, Long value2) {
            addCriterion("start_subtitles_cut_id between", value1, value2, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesCutIdNotBetween(Long value1, Long value2) {
            addCriterion("start_subtitles_cut_id not between", value1, value2, "startSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdIsNull() {
            addCriterion("end_subtitles_cut_id is null");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdIsNotNull() {
            addCriterion("end_subtitles_cut_id is not null");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdEqualTo(Long value) {
            addCriterion("end_subtitles_cut_id =", value, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdNotEqualTo(Long value) {
            addCriterion("end_subtitles_cut_id <>", value, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdGreaterThan(Long value) {
            addCriterion("end_subtitles_cut_id >", value, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdGreaterThanOrEqualTo(Long value) {
            addCriterion("end_subtitles_cut_id >=", value, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdLessThan(Long value) {
            addCriterion("end_subtitles_cut_id <", value, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdLessThanOrEqualTo(Long value) {
            addCriterion("end_subtitles_cut_id <=", value, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdIn(List<Long> values) {
            addCriterion("end_subtitles_cut_id in", values, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdNotIn(List<Long> values) {
            addCriterion("end_subtitles_cut_id not in", values, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdBetween(Long value1, Long value2) {
            addCriterion("end_subtitles_cut_id between", value1, value2, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesCutIdNotBetween(Long value1, Long value2) {
            addCriterion("end_subtitles_cut_id not between", value1, value2, "endSubtitlesCutId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdIsNull() {
            addCriterion("start_subtitles_id is null");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdIsNotNull() {
            addCriterion("start_subtitles_id is not null");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdEqualTo(Long value) {
            addCriterion("start_subtitles_id =", value, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdNotEqualTo(Long value) {
            addCriterion("start_subtitles_id <>", value, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdGreaterThan(Long value) {
            addCriterion("start_subtitles_id >", value, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdGreaterThanOrEqualTo(Long value) {
            addCriterion("start_subtitles_id >=", value, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdLessThan(Long value) {
            addCriterion("start_subtitles_id <", value, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdLessThanOrEqualTo(Long value) {
            addCriterion("start_subtitles_id <=", value, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdIn(List<Long> values) {
            addCriterion("start_subtitles_id in", values, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdNotIn(List<Long> values) {
            addCriterion("start_subtitles_id not in", values, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdBetween(Long value1, Long value2) {
            addCriterion("start_subtitles_id between", value1, value2, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andStartSubtitlesIdNotBetween(Long value1, Long value2) {
            addCriterion("start_subtitles_id not between", value1, value2, "startSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdIsNull() {
            addCriterion("end_subtitles_id is null");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdIsNotNull() {
            addCriterion("end_subtitles_id is not null");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdEqualTo(Long value) {
            addCriterion("end_subtitles_id =", value, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdNotEqualTo(Long value) {
            addCriterion("end_subtitles_id <>", value, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdGreaterThan(Long value) {
            addCriterion("end_subtitles_id >", value, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdGreaterThanOrEqualTo(Long value) {
            addCriterion("end_subtitles_id >=", value, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdLessThan(Long value) {
            addCriterion("end_subtitles_id <", value, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdLessThanOrEqualTo(Long value) {
            addCriterion("end_subtitles_id <=", value, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdIn(List<Long> values) {
            addCriterion("end_subtitles_id in", values, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdNotIn(List<Long> values) {
            addCriterion("end_subtitles_id not in", values, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdBetween(Long value1, Long value2) {
            addCriterion("end_subtitles_id between", value1, value2, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andEndSubtitlesIdNotBetween(Long value1, Long value2) {
            addCriterion("end_subtitles_id not between", value1, value2, "endSubtitlesId");
            return (Criteria) this;
        }

        public Criteria andSubtitlesIsNull() {
            addCriterion("subtitles is null");
            return (Criteria) this;
        }

        public Criteria andSubtitlesIsNotNull() {
            addCriterion("subtitles is not null");
            return (Criteria) this;
        }

        public Criteria andSubtitlesEqualTo(String value) {
            addCriterion("subtitles =", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesNotEqualTo(String value) {
            addCriterion("subtitles <>", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesGreaterThan(String value) {
            addCriterion("subtitles >", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesGreaterThanOrEqualTo(String value) {
            addCriterion("subtitles >=", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesLessThan(String value) {
            addCriterion("subtitles <", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesLessThanOrEqualTo(String value) {
            addCriterion("subtitles <=", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesLike(String value) {
            addCriterion("subtitles like", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesNotLike(String value) {
            addCriterion("subtitles not like", value, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesIn(List<String> values) {
            addCriterion("subtitles in", values, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesNotIn(List<String> values) {
            addCriterion("subtitles not in", values, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesBetween(String value1, String value2) {
            addCriterion("subtitles between", value1, value2, "subtitles");
            return (Criteria) this;
        }

        public Criteria andSubtitlesNotBetween(String value1, String value2) {
            addCriterion("subtitles not between", value1, value2, "subtitles");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Integer value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Integer value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Integer value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Integer value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Integer value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Integer> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Integer> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Integer value1, Integer value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountIsNull() {
            addCriterion("subtitles_cut_count is null");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountIsNotNull() {
            addCriterion("subtitles_cut_count is not null");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountEqualTo(Integer value) {
            addCriterion("subtitles_cut_count =", value, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountNotEqualTo(Integer value) {
            addCriterion("subtitles_cut_count <>", value, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountGreaterThan(Integer value) {
            addCriterion("subtitles_cut_count >", value, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("subtitles_cut_count >=", value, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountLessThan(Integer value) {
            addCriterion("subtitles_cut_count <", value, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountLessThanOrEqualTo(Integer value) {
            addCriterion("subtitles_cut_count <=", value, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountIn(List<Integer> values) {
            addCriterion("subtitles_cut_count in", values, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountNotIn(List<Integer> values) {
            addCriterion("subtitles_cut_count not in", values, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountBetween(Integer value1, Integer value2) {
            addCriterion("subtitles_cut_count between", value1, value2, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSubtitlesCutCountNotBetween(Integer value1, Integer value2) {
            addCriterion("subtitles_cut_count not between", value1, value2, "subtitlesCutCount");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}