package com.fastclip.dao.model.dataobject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FilterEffectExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public FilterEffectExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andSaturateIsNull() {
            addCriterion("saturate is null");
            return (Criteria) this;
        }

        public Criteria andSaturateIsNotNull() {
            addCriterion("saturate is not null");
            return (Criteria) this;
        }

        public Criteria andSaturateEqualTo(Integer value) {
            addCriterion("saturate =", value, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateNotEqualTo(Integer value) {
            addCriterion("saturate <>", value, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateGreaterThan(Integer value) {
            addCriterion("saturate >", value, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateGreaterThanOrEqualTo(Integer value) {
            addCriterion("saturate >=", value, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateLessThan(Integer value) {
            addCriterion("saturate <", value, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateLessThanOrEqualTo(Integer value) {
            addCriterion("saturate <=", value, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateIn(List<Integer> values) {
            addCriterion("saturate in", values, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateNotIn(List<Integer> values) {
            addCriterion("saturate not in", values, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateBetween(Integer value1, Integer value2) {
            addCriterion("saturate between", value1, value2, "saturate");
            return (Criteria) this;
        }

        public Criteria andSaturateNotBetween(Integer value1, Integer value2) {
            addCriterion("saturate not between", value1, value2, "saturate");
            return (Criteria) this;
        }

        public Criteria andLightnessIsNull() {
            addCriterion("lightness is null");
            return (Criteria) this;
        }

        public Criteria andLightnessIsNotNull() {
            addCriterion("lightness is not null");
            return (Criteria) this;
        }

        public Criteria andLightnessEqualTo(Integer value) {
            addCriterion("lightness =", value, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessNotEqualTo(Integer value) {
            addCriterion("lightness <>", value, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessGreaterThan(Integer value) {
            addCriterion("lightness >", value, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessGreaterThanOrEqualTo(Integer value) {
            addCriterion("lightness >=", value, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessLessThan(Integer value) {
            addCriterion("lightness <", value, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessLessThanOrEqualTo(Integer value) {
            addCriterion("lightness <=", value, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessIn(List<Integer> values) {
            addCriterion("lightness in", values, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessNotIn(List<Integer> values) {
            addCriterion("lightness not in", values, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessBetween(Integer value1, Integer value2) {
            addCriterion("lightness between", value1, value2, "lightness");
            return (Criteria) this;
        }

        public Criteria andLightnessNotBetween(Integer value1, Integer value2) {
            addCriterion("lightness not between", value1, value2, "lightness");
            return (Criteria) this;
        }

        public Criteria andContrastIsNull() {
            addCriterion("contrast is null");
            return (Criteria) this;
        }

        public Criteria andContrastIsNotNull() {
            addCriterion("contrast is not null");
            return (Criteria) this;
        }

        public Criteria andContrastEqualTo(Integer value) {
            addCriterion("contrast =", value, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastNotEqualTo(Integer value) {
            addCriterion("contrast <>", value, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastGreaterThan(Integer value) {
            addCriterion("contrast >", value, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastGreaterThanOrEqualTo(Integer value) {
            addCriterion("contrast >=", value, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastLessThan(Integer value) {
            addCriterion("contrast <", value, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastLessThanOrEqualTo(Integer value) {
            addCriterion("contrast <=", value, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastIn(List<Integer> values) {
            addCriterion("contrast in", values, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastNotIn(List<Integer> values) {
            addCriterion("contrast not in", values, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastBetween(Integer value1, Integer value2) {
            addCriterion("contrast between", value1, value2, "contrast");
            return (Criteria) this;
        }

        public Criteria andContrastNotBetween(Integer value1, Integer value2) {
            addCriterion("contrast not between", value1, value2, "contrast");
            return (Criteria) this;
        }

        public Criteria andShadowIsNull() {
            addCriterion("shadow is null");
            return (Criteria) this;
        }

        public Criteria andShadowIsNotNull() {
            addCriterion("shadow is not null");
            return (Criteria) this;
        }

        public Criteria andShadowEqualTo(Integer value) {
            addCriterion("shadow =", value, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowNotEqualTo(Integer value) {
            addCriterion("shadow <>", value, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowGreaterThan(Integer value) {
            addCriterion("shadow >", value, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowGreaterThanOrEqualTo(Integer value) {
            addCriterion("shadow >=", value, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowLessThan(Integer value) {
            addCriterion("shadow <", value, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowLessThanOrEqualTo(Integer value) {
            addCriterion("shadow <=", value, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowIn(List<Integer> values) {
            addCriterion("shadow in", values, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowNotIn(List<Integer> values) {
            addCriterion("shadow not in", values, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowBetween(Integer value1, Integer value2) {
            addCriterion("shadow between", value1, value2, "shadow");
            return (Criteria) this;
        }

        public Criteria andShadowNotBetween(Integer value1, Integer value2) {
            addCriterion("shadow not between", value1, value2, "shadow");
            return (Criteria) this;
        }

        public Criteria andHighlightIsNull() {
            addCriterion("highLight is null");
            return (Criteria) this;
        }

        public Criteria andHighlightIsNotNull() {
            addCriterion("highLight is not null");
            return (Criteria) this;
        }

        public Criteria andHighlightEqualTo(Integer value) {
            addCriterion("highLight =", value, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightNotEqualTo(Integer value) {
            addCriterion("highLight <>", value, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightGreaterThan(Integer value) {
            addCriterion("highLight >", value, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightGreaterThanOrEqualTo(Integer value) {
            addCriterion("highLight >=", value, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightLessThan(Integer value) {
            addCriterion("highLight <", value, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightLessThanOrEqualTo(Integer value) {
            addCriterion("highLight <=", value, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightIn(List<Integer> values) {
            addCriterion("highLight in", values, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightNotIn(List<Integer> values) {
            addCriterion("highLight not in", values, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightBetween(Integer value1, Integer value2) {
            addCriterion("highLight between", value1, value2, "highlight");
            return (Criteria) this;
        }

        public Criteria andHighlightNotBetween(Integer value1, Integer value2) {
            addCriterion("highLight not between", value1, value2, "highlight");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNull() {
            addCriterion("temperature is null");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNotNull() {
            addCriterion("temperature is not null");
            return (Criteria) this;
        }

        public Criteria andTemperatureEqualTo(Integer value) {
            addCriterion("temperature =", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotEqualTo(Integer value) {
            addCriterion("temperature <>", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThan(Integer value) {
            addCriterion("temperature >", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThanOrEqualTo(Integer value) {
            addCriterion("temperature >=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThan(Integer value) {
            addCriterion("temperature <", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThanOrEqualTo(Integer value) {
            addCriterion("temperature <=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureIn(List<Integer> values) {
            addCriterion("temperature in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotIn(List<Integer> values) {
            addCriterion("temperature not in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureBetween(Integer value1, Integer value2) {
            addCriterion("temperature between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotBetween(Integer value1, Integer value2) {
            addCriterion("temperature not between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}