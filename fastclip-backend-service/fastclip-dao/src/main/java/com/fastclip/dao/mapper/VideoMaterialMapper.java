package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.VideoMaterial;
import com.fastclip.dao.model.dataobject.VideoMaterialExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface VideoMaterialMapper {
    long countByExample(VideoMaterialExample example);

    int deleteByExample(VideoMaterialExample example);

    int deleteByPrimaryKey(Long id);

    int insert(VideoMaterial record);

    int insertSelective(VideoMaterial record);

    List<VideoMaterial> selectByExampleWithRowbounds(VideoMaterialExample example, RowBounds rowBounds);

    List<VideoMaterial> selectByExample(VideoMaterialExample example);

    VideoMaterial selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") VideoMaterial record, @Param("example") VideoMaterialExample example);

    int updateByExample(@Param("record") VideoMaterial record, @Param("example") VideoMaterialExample example);

    int updateByPrimaryKeySelective(VideoMaterial record);

    int updateByPrimaryKey(VideoMaterial record);
}