package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.VideoMaterialSliceDTO;
import com.fastclip.dao.model.dataobject.VideoMaterialSlice;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class VideoMaterialSliceUtils {
    public static VideoMaterialSliceDTO do2DTO(VideoMaterialSlice videoMaterialSplit) {
        VideoMaterialSliceDTO videoMaterialSplitDTO = new VideoMaterialSliceDTO();
        BeanUtils.copyProperties(videoMaterialSplit, videoMaterialSplitDTO);
        return videoMaterialSplitDTO;
    }

    public static List<VideoMaterialSliceDTO> do2DTO(List<VideoMaterialSlice> videoMaterialSplits) {
        if (CollectionUtils.isEmpty(videoMaterialSplits)) {
            return new ArrayList<>();
        }
        return videoMaterialSplits.stream().map(VideoMaterialSliceUtils::do2DTO).collect(Collectors.toList());
    }

    public static VideoMaterialSlice dto2DO(VideoMaterialSliceDTO videoMaterialSplitDTO) {
        VideoMaterialSlice videoMaterialSplit = new VideoMaterialSlice();
        BeanUtils.copyProperties(videoMaterialSplitDTO, videoMaterialSplit);
        return videoMaterialSplit;
    }

    public static List<VideoMaterialSlice> dto2DO(List<VideoMaterialSliceDTO> videoMaterialSplitDTOS) {
        if (CollectionUtils.isEmpty(videoMaterialSplitDTOS)) {
            return new ArrayList<>();
        }
        return videoMaterialSplitDTOS.stream().map(VideoMaterialSliceUtils::dto2DO).collect(Collectors.toList());
    }
}
