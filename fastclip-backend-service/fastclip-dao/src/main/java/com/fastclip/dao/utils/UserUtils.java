package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.dao.model.dataobject.FUser;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class UserUtils {
    public static UserDTO do2DTO(FUser fUser) {
        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(fUser, userDTO);
        return userDTO;
    }



    public static List<UserDTO> do2DTOs(List<FUser> users) {
        if (CollectionUtils.isEmpty(users)) {
            return new ArrayList<>();
        }
        return users.stream().map(UserUtils::do2DTO).collect(Collectors.toList());
    }

}
