package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.QRCode;
import com.fastclip.dao.model.dataobject.QRCodeExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface QRCodeMapper {
    long countByExample(QRCodeExample example);

    int deleteByExample(QRCodeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(QRCode record);

    int insertSelective(QRCode record);

    List<QRCode> selectByExampleWithRowbounds(QRCodeExample example, RowBounds rowBounds);

    List<QRCode> selectByExample(QRCodeExample example);

    QRCode selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") QRCode record, @Param("example") QRCodeExample example);

    int updateByExample(@Param("record") QRCode record, @Param("example") QRCodeExample example);

    int updateByPrimaryKeySelective(QRCode record);

    int updateByPrimaryKey(QRCode record);
}