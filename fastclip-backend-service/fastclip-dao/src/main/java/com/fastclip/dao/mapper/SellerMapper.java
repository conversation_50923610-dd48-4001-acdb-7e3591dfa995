package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.Seller;
import com.fastclip.dao.model.dataobject.SellerExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface SellerMapper {
    long countByExample(SellerExample example);

    int deleteByExample(SellerExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Seller record);

    int insertSelective(Seller record);

    List<Seller> selectByExampleWithRowbounds(SellerExample example, RowBounds rowBounds);

    List<Seller> selectByExample(SellerExample example);

    Seller selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") Seller record, @Param("example") SellerExample example);

    int updateByExample(@Param("record") Seller record, @Param("example") SellerExample example);

    int updateByPrimaryKeySelective(Seller record);

    int updateByPrimaryKey(Seller record);
}