package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.VideoClip;
import com.fastclip.dao.model.dataobject.VideoClipExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface VideoClipMapper {
    long countByExample(VideoClipExample example);

    int deleteByExample(VideoClipExample example);

    int deleteByPrimaryKey(Long id);

    int insert(VideoClip record);

    int insertSelective(VideoClip record);

    List<VideoClip> selectByExampleWithRowbounds(VideoClipExample example, RowBounds rowBounds);

    List<VideoClip> selectByExample(VideoClipExample example);

    VideoClip selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") VideoClip record, @Param("example") VideoClipExample example);

    int updateByExample(@Param("record") VideoClip record, @Param("example") VideoClipExample example);

    int updateByPrimaryKeySelective(VideoClip record);

    int updateByPrimaryKey(VideoClip record);
}