package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.VideoMaterialSlice;
import com.fastclip.dao.model.dataobject.VideoMaterialSliceExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface VideoMaterialSliceMapper {
    long countByExample(VideoMaterialSliceExample example);

    int deleteByExample(VideoMaterialSliceExample example);

    int deleteByPrimaryKey(Long id);

    int insert(VideoMaterialSlice record);

    int insertSelective(VideoMaterialSlice record);

    List<VideoMaterialSlice> selectByExampleWithRowbounds(VideoMaterialSliceExample example, RowBounds rowBounds);

    List<VideoMaterialSlice> selectByExample(VideoMaterialSliceExample example);

    VideoMaterialSlice selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") VideoMaterialSlice record, @Param("example") VideoMaterialSliceExample example);

    int updateByExample(@Param("record") VideoMaterialSlice record, @Param("example") VideoMaterialSliceExample example);

    int updateByPrimaryKeySelective(VideoMaterialSlice record);

    int updateByPrimaryKey(VideoMaterialSlice record);
}