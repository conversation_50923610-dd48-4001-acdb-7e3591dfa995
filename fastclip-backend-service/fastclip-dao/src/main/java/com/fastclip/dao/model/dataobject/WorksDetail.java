package com.fastclip.dao.model.dataobject;

import java.util.Date;

public class WorksDetail {
    private Long id;

    private Long projectId;

    private Long worksId;

    private Long sellerId;

    private Long videoClipId;

    private Integer sort;

    private Integer duration;

    private String tranCode;

    private String tagCode;

    private Integer cutPercent;

    private Integer transId;

    private Date createTime;

    private Date updateTime;

    private Boolean flipFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getWorksId() {
        return worksId;
    }

    public void setWorksId(Long worksId) {
        this.worksId = worksId;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Long getVideoClipId() {
        return videoClipId;
    }

    public void setVideoClipId(Long videoClipId) {
        this.videoClipId = videoClipId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getTranCode() {
        return tranCode;
    }

    public void setTranCode(String tranCode) {
        this.tranCode = tranCode == null ? null : tranCode.trim();
    }

    public String getTagCode() {
        return tagCode;
    }

    public void setTagCode(String tagCode) {
        this.tagCode = tagCode == null ? null : tagCode.trim();
    }

    public Integer getCutPercent() {
        return cutPercent;
    }

    public void setCutPercent(Integer cutPercent) {
        this.cutPercent = cutPercent;
    }

    public Integer getTransId() {
        return transId;
    }

    public void setTransId(Integer transId) {
        this.transId = transId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getFlipFlag() {
        return flipFlag;
    }

    public void setFlipFlag(Boolean flipFlag) {
        this.flipFlag = flipFlag;
    }
}