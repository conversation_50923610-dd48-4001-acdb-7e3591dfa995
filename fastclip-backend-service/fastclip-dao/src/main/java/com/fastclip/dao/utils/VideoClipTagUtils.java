package com.fastclip.dao.utils;

import com.fastclip.common.constant.VideoClipTagEnum;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.VideoClipTagDTO;
import com.fastclip.dao.model.dataobject.VideoClip;
import com.fastclip.dao.model.dataobject.VideoClipTag;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class VideoClipTagUtils {
    public static VideoClipTagDTO do2DTO(VideoClipTag videoCliptag) {
        VideoClipTagDTO videoClipTagDTO = new VideoClipTagDTO();
        videoClipTagDTO.setId(videoCliptag.getId());
        videoClipTagDTO.setVideoClipId(videoCliptag.getClipId());
        videoClipTagDTO.setCode(videoCliptag.getTagCode());
        videoClipTagDTO.setName(VideoClipTagEnum.getName(videoCliptag.getTagCode()));
        return videoClipTagDTO;
    }

    public static List<VideoClipTagDTO> do2DTO(List<VideoClipTag> videoClipTags) {
        if (CollectionUtils.isEmpty(videoClipTags)) {
            return new ArrayList<>();
        }
        return videoClipTags.stream().map(VideoClipTagUtils::do2DTO).collect(Collectors.toList());
    }

    public static VideoClipTag dto2DO(VideoClipTagDTO videoClipTagDTO) {
        VideoClipTag videoClipTag = new VideoClipTag();
        BeanUtils.copyProperties(videoClipTagDTO, videoClipTag);
        videoClipTag.setTagCode(videoClipTagDTO.getCode());
        videoClipTag.setClipId(videoClipTagDTO.getVideoClipId());
        return videoClipTag;
    }

    public static List<VideoClipTag> dto2DO(List<VideoClipTagDTO> videoClipTagDTOS) {
        if (CollectionUtils.isEmpty(videoClipTagDTOS)) {
            return new ArrayList<>();
        }
        return videoClipTagDTOS.stream().map(VideoClipTagUtils::dto2DO).collect(Collectors.toList());
    }
}
