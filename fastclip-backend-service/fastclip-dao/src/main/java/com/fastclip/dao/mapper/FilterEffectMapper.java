package com.fastclip.dao.mapper;

import com.fastclip.dao.model.dataobject.FilterEffect;
import com.fastclip.dao.model.dataobject.FilterEffectExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface FilterEffectMapper {
    long countByExample(FilterEffectExample example);

    int deleteByExample(FilterEffectExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(FilterEffect record);

    int insertSelective(FilterEffect record);

    List<FilterEffect> selectByExampleWithRowbounds(FilterEffectExample example, RowBounds rowBounds);

    List<FilterEffect> selectByExample(FilterEffectExample example);

    FilterEffect selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") FilterEffect record, @Param("example") FilterEffectExample example);

    int updateByExample(@Param("record") FilterEffect record, @Param("example") FilterEffectExample example);

    int updateByPrimaryKeySelective(FilterEffect record);

    int updateByPrimaryKey(FilterEffect record);
}