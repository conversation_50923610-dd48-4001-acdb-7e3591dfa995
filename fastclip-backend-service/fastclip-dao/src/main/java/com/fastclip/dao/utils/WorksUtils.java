package com.fastclip.dao.utils;

import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.common.model.dto.WorksDetailDTO;
import com.fastclip.dao.model.dataobject.Works;
import com.fastclip.dao.model.dataobject.WorksDetail;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class WorksUtils {

    public static WorksDetailDTO detailsDo2DTO(WorksDetail worksDetail) {
        WorksDetailDTO worksDetailDTO = new WorksDetailDTO();
        BeanUtils.copyProperties(worksDetail, worksDetailDTO);
        return worksDetailDTO;
    }

    public static List<WorksDetailDTO> detailsDo2DTOs(List<WorksDetail> worksDetails) {
        if(CollectionUtils.isEmpty(worksDetails)) {
            return new ArrayList<>();
        }
        return worksDetails.stream().map(WorksUtils::detailsDo2DTO).collect(Collectors.toList());
    }

    public static WorksDetail detailsDto2DO(WorksDetailDTO worksDetailDTO) {
        WorksDetail worksDetail = new WorksDetail();
        BeanUtils.copyProperties(worksDetailDTO, worksDetail);
        return worksDetail;
    }

    public static List<WorksDetail> detailsDto2DO(List<WorksDetailDTO> worksDetailDTOs) {
        if(CollectionUtils.isEmpty(worksDetailDTOs)) {
            return new ArrayList<>();
        }
        return worksDetailDTOs.stream().map(WorksUtils::detailsDto2DO).collect(Collectors.toList());
    }

    public static WorksDTO do2DTO(Works works) {
        WorksDTO worksDTO = new WorksDTO();
        BeanUtils.copyProperties(works, worksDTO);
        return worksDTO;
    }

    public static List<WorksDTO> do2DTOs(List<Works> works) {
        if(CollectionUtils.isEmpty(works)) {
            return new ArrayList<>();
        }
        return works.stream().map(WorksUtils::do2DTO).collect(Collectors.toList());
    }

    public static Works dto2DO(WorksDTO worksDTO) {
        Works works = new Works();
        BeanUtils.copyProperties(worksDTO, works);
        works.setAccountId(worksDTO.getAccountId());
        if(worksDTO.getMusicDTO() != null) {
            works.setMusicId(worksDTO.getMusicDTO().getId());
        }
        return works;
    }

    public static List<Works> dto2DO(List<WorksDTO> worksDTOS) {
        if(CollectionUtils.isEmpty(worksDTOS)) {
            return new ArrayList<>();
        }
        return worksDTOS.stream().map(WorksUtils::dto2DO).collect(Collectors.toList());
    }

}
