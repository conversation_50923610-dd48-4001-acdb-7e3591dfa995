package com.fastclip.dao.mapper;

import com.fastclip.common.model.dataobject.AccountWorksDO;
import com.fastclip.common.model.request.WorksCountReq;
import com.fastclip.dao.model.dataobject.Works;
import com.fastclip.dao.model.dataobject.WorksExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface WorksMapperExt {
    List<AccountWorksDO> getWorkCountsToday(@Param("req")WorksCountReq req);
    List<AccountWorksDO>  getPublishedCountsToday(@Param("req")WorksCountReq req);
    List<AccountWorksDO>  getUnPublishedCounts(@Param("req")WorksCountReq req);
}