package com.fastclip.starter;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

public class DataProducer {
    public static void main(String[] args) {
        Date startDate = new Date(1704092047000L);
        Date endDate = new Date(1719730447000L+ 86400000L);
        Date tmp = startDate;
        Workbook workbook = new XSSFWorkbook();
        Sheet nodeSheet = workbook.createSheet("订单");
        Row headerRow = nodeSheet.createRow(0);
        Cell cell0 = headerRow.createCell(0);
        cell0.setCellValue("订单号");
        Cell cell1 = headerRow.createCell(1);
        cell1.setCellValue("日期");
        Cell cell2 = headerRow.createCell(2);
        cell2.setCellValue("时段");
        Cell cell3 = headerRow.createCell(3);
        cell3.setCellValue("菜品名称");
        Cell cell4 = headerRow.createCell(4);
        cell4.setCellValue("单价");
        Cell cell5 = headerRow.createCell(5);
        cell5.setCellValue("销售数量");
        Cell cell6 = headerRow.createCell(6);
        cell6.setCellValue("顾客类型");

        Map<String, Integer> menu = getManu();

        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        int j=0;
        int orderNum = 10000000;
        while(tmp.before(endDate)) {
            int i = getOrderNum(tmp);
            System.out.println(tmp +"-----" + i);
            while(i > 0) {
                orderNum++;
                j++;
                Row dataRow = nodeSheet.createRow(j);
                Cell dataCell0 = dataRow.createCell(0);
                dataCell0.setCellValue(String.valueOf(orderNum));
                Cell dataCell1 = dataRow.createCell(1);
                dataCell1.setCellValue(format.format(tmp));
                Cell dataCell2 = dataRow.createCell(2);
                String period = getPeriod();
                dataCell2.setCellValue(period);
                Cell dataCell3 = dataRow.createCell(3);
                String foodName = getFoodName(menu);
                dataCell3.setCellValue(foodName);
                Cell dataCell4 = dataRow.createCell(4);
                dataCell4.setCellValue(menu.get(foodName));
                Cell dataCell5 = dataRow.createCell(5);
                dataCell5.setCellValue(1);
                Cell dataCell6 = dataRow.createCell(6);
                dataCell6.setCellValue(getCustomType(period));
                i--;
            }
            tmp = new Date(tmp.getTime() + 86400000L);
        }
        try{
            FileOutputStream fileOut = new FileOutputStream("/Users/<USER>/Documents/orders.xlsx");
            workbook.write(fileOut);
        }catch (Exception e) {
        }
    }

    public static int getOrderNum(Date date){
       if(isWeekend(date)) {
           return 600 + (int)(400*Math.random());
        }
        if(isHoliday(date)) {
            return 500 + (int)(300*Math.random());
        }
        return 200 + (int)(300*Math.random());
    }

    private static boolean isWeekend(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY;
    }

    private static boolean isHoliday(Date date) {
        Set<String> holidays = getHolidays();
        int year = date.getYear();
        int month = date.getMonth();
        int day = date.getDate();
        String monthStr = month>=10?"" + month:"0" + month;
        String dayStr = day>=10?"" + day:"0" + day;
        String dateStr = year + monthStr + dayStr;
        if(holidays.contains(dateStr)) {
            return true;
        }
        return false;
    }

    public static Set<String> getHolidays() {
        Set<String> set = new HashSet();
        set.add("20240101");
        set.add("20240210");
        set.add("20240211");
        set.add("20240213");
        set.add("20240214");
        set.add("20240215");
        set.add("20240216");
        set.add("20240217");
        set.add("20240404");
        set.add("20240405");
        set.add("20240406");
        set.add("20240501");
        set.add("20240502");
        set.add("20240503");
        set.add("20240504");
        set.add("20240505");
        set.add("20240610");
        set.add("20240915");
        set.add("20240916");
        set.add("20240917");
        set.add("20241001");
        set.add("20241002");
        set.add("20241003");
        set.add("20241004");
        set.add("20241005");
        set.add("20241006");
        set.add("20241007");
        return set;
    }

    public static Map<String, Integer> getManu() {
        Map<String, Integer> map = new HashMap<>();
        map.put("宫保鸡丁",30);
        map.put("凉拌黄瓜",15);
        map.put("清蒸鲈鱼",60);
        map.put("蒜泥白肉", 48);
        map.put("酸辣土豆丝",28);
        map.put("红烧肉", 58);
        map.put("水煮牛肉",58);
        map.put("西红柿炒蛋", 28);
        map.put("麻婆豆腐", 28);
        map.put("锅包肉", 38);
        map.put("芝麻酱拌菠菜",24);
        map.put("糖醋排骨",48);
        map.put("油爆虾", 68);
        map.put("豆腐脑",16);
        map.put("辣子鸡丁", 48);
        map.put("白灼菜心", 18);
        map.put("香煎三文鱼配柠檬黄油酱", 58);
        map.put("干煸四季豆", 28);
        map.put("雪菜肉丝炒年糕", 36);
        map.put("葱油拌面", 18);
        map.put("清蒸海鲈鱼", 78);
        map.put("韩式泡菜炒五花肉", 38);
        map.put("翡翠白玉汤（菠菜豆腐汤）", 28);
        map.put("蚝油牛肉炒芥蓝", 48);
        map.put("蜜汁叉烧", 58);
        map.put("西湖牛肉羹", 38);
        map.put("铁板黑椒牛柳", 48);
        map.put("芝士焗烤生蚝", 68);
        map.put("金针菇肥牛卷", 58);
        return map;
    }

    private static String getPeriod() {
        int i = (int)(Math.random() * 10);
        if(i >=5) {
            return "晚餐";
        }
        return "午餐";
    }

    private static String getCustomType(String period) {
        if("晚餐".equals(period)) {
            return (int)(Math.random()*10) > 2 ? "堂食":"外卖";
        }
        if("午餐".equals(period)) {
            return (int)(Math.random()*10) < 3 ? "堂食":"外卖";
        }
        return "堂食";
    }

    private static String getFoodName(Map<String, Integer> map) {
        List<String> foodNames = new ArrayList<>(map.keySet());
        return foodNames.get((int)(map.size() * Math.random()));
    }
}


