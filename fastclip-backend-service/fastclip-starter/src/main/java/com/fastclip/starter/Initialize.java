//package com.fastclip.starter;
//
//import com.fastclip.dao.conn.DouyinConnection;
//import com.fastclip.dao.mapper.DouyinMapper;
//import com.fastclip.service.douyin.DouyinService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
//@Component
//@Slf4j
//public class Initialize implements CommandLineRunner {
//
//    @Autowired
//    DouyinConnection douyinConnection;
//
//
//
//    @Value("${douyin.sleep_duration}")
//    Integer sleepDuration;
//
//    @Value("${douyin.searchkey}")
//    String searchkey;
//
//    @Value("${douyin.max_page}")
//    Integer max_page;
//
//    @Autowired
//    DouyinMapper douyinMapper;
//
//    @Autowired
//    DouyinService douyinService;
//
//    @Override
//    public void run(String... args) throws Exception {
////        while(true) {
////            Boolean findAVideoMatchConditions = false;
////            String[] keys = searchkey.split(",");
////            for(String key: keys) {
////                int i = 0;
////                while (!findAVideoMatchConditions && i <= max_page) {
////                    List<DouyinAO> douyinDTOS = douyinConnection.getData(key, i * 20, 20);
////                    if (CollectionUtils.isEmpty(douyinDTOS)) {
////                        break;
////                    }
////                    for (DouyinAO douyinDTO : douyinDTOS) {
////                        log.info("start to process data:" + JSON.toJSONString(douyinDTO));
////
////                        douyinService.process(douyinDTO);
////                        log.info("finish to process data:" + JSON.toJSONString(douyinDTO));
////                    }
////                    i++;
////                }
////            }
////            Thread.sleep(sleepDuration);
////        }
//    }
//}
