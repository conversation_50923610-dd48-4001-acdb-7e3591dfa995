//package com.fastclip.service.cut;
//
//import com.alibaba.druid.util.StringUtils;
//import com.fastclip.common.model.dataobject.VideoClipDO;
//import com.fastclip.common.model.dataobject.ChanxuanCutClipDO;
//import com.fastclip.common.model.dataobject.ChanxuanItemDO;
//import com.fastclip.common.utils.FileUtils;
//import com.fastclip.dao.mapper.ChanxuanMapper;
//import com.fastclip.service.FfmpegService;
//import QwenService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.io.File;
//import java.io.FileWriter;
//import java.util.ArrayList;
//import java.util.List;
//
//@Service
//@Slf4j
//public class AutoCutService {
//
//    @Autowired
//    ChanxuanMapper chanxuanMapper;
//
//    @Autowired
//    FfmpegService ffmpegService;
//
//    @Autowired
//    QwenService qwenService;
//
//    public boolean autoCut(String productId){
//        ChanxuanItemDO chanxuanItemDO = chanxuanMapper.getItem(productId);
//        List<VideoClipDO> clipDOS = chanxuanMapper.getClipsByProductId(chanxuanItemDO.getMaterialProductId());
//        for(int i=0; i<clipDOS.size(); i++) {
//            VideoClipDO tmpClipDO = clipDOS.get(i);
//            autoCut(chanxuanItemDO, tmpClipDO);
//        }
//        return true;
//    }
//
//    public boolean autoCutWithMultiVideos(String productId){
//        ChanxuanItemDO chanxuanItemDO = chanxuanMapper.getItem(productId);
//        List<VideoClipDO> clipDOS = chanxuanMapper.getClipsByProductId(chanxuanItemDO.getMaterialProductId());
//        for(int i=0; i<clipDOS.size(); i++) {
//            VideoClipDO tmpClipDO = clipDOS.get(i);
//            autoCut(chanxuanItemDO, tmpClipDO);
//        }
//        return true;
//    }
//
//    public void autoCut(ChanxuanItemDO itemDO, VideoClipDO clipDO) {
//
//        ChanxuanCutClipDO cutClip = chanxuanMapper.getCutClipByTaskId(clipDO.getTaskId());
//        if (cutClip == null && clipDO.getLocalSrtPath() != null && FileUtils.exist(clipDO.getLocalSrtPath())
//                && clipDO.getDuration() > 60) {
//            String videoPath = clipDO.getLocalVideoPath();
//            String srtPath = clipDO.getLocalSrtPath();
//            String videoFolderPath = videoPath.substring(0, videoPath.length() - 4);
//            String localCutVideoPath = videoFolderPath + "_cut_final.mp4";
//            String srtFolderPath = srtPath.substring(0, videoPath.length() - 4);
//            String hflipPath = videoFolderPath + "_hflip.mp4";
//            String cutTopAndBottomPath = videoFolderPath + "_cut_top_bottom.mp4";
//            String cutVideoWithSrtPath = videoFolderPath + "_with_srt.mp4";
//            String cutSRTFilePath = srtFolderPath + "_cut.srt";
//            String answer = qwenService.askWithDefaultSystemQ(clipDO.getLocalSrtPath());
//            if (!writeSrtToFile(answer, cutSRTFilePath)) {
//                return;
//            }
//            String itemDesc = qwenService.askItemDesc(itemDO.getProductName());
//            ffmpegService.crop(videoPath, cutTopAndBottomPath);
//            ffmpegService.hflip(cutTopAndBottomPath, hflipPath);
//            ffmpegService.insertSrt(hflipPath, srtPath, cutVideoWithSrtPath);
//            ffmpegService.cutAndMerageByPeriodList(cutSRTFilePath, cutVideoWithSrtPath, localCutVideoPath);
//            ChanxuanCutClipDO cutClipDO = new ChanxuanCutClipDO();
//            BeanUtils.copyProperties(clipDO, cutClipDO);
//            cutClipDO.setCutVideoPath(localCutVideoPath);
//            List<ChanxuanCutClipDO> cutClipDOS = new ArrayList<>();
//            cutClipDOS.add(cutClipDO);
//            cutClipDO.setDescription(itemDesc + "#2024早秋时髦主场");
//            cutClipDO.setProductId(itemDO.getProductId());
//            cutClipDO.setProductUrl(itemDO.getProductUrl());
//            cutClipDO.setProductName(itemDO.getProductName());
//            cutClipDO.setMaterialProductId(itemDO.getMaterialProductId());
//            cutClipDO.setStarId(itemDO.getStarId());
//            chanxuanMapper.insertCxCutClip(cutClipDOS);
//        }
//    }
//
//    public void autoCutByAccount(String cxAccount){
//        while(true) {
//            List<VideoClipDO> chanxuanClipDOS = chanxuanMapper.getAllClipsNoCut(cxAccount);
//            if(CollectionUtils.isEmpty(chanxuanClipDOS)) {
//                try {
//                    Thread.sleep(300000);
//                }catch (Exception e) {
//
//                }
//            }
//            for(VideoClipDO clipDO: chanxuanClipDOS) {
//                autoCut(chanxuanMapper.getItemByMaterialId(clipDO.getProductId()), clipDO);
//            }
//        }
//    }
//
//    public boolean writeSrtToFile(String srtContent, String srtPath) {
//        if(StringUtils.isEmpty(srtContent)) {
//            return false;
//        }
//        try {
//            File file = new File(srtPath);
//            FileWriter fileWriter = new FileWriter(file);
//            fileWriter.write(srtContent);
//            fileWriter.close();
//            return true;
//        }catch(Exception e) {
//            log.error("write srt to file error", e);
//        }
//        return false;
//    }
//
//
//
//}
