package com.fastclip.service.schedule;

import com.fastclip.common.constant.ComposeStatusEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.GetWorksReq;
import com.fastclip.dao.mapper.FUserMapper;
import com.fastclip.dao.mapper.ProjectMapper;
import com.fastclip.dao.mapper.SellerMapper;
import com.fastclip.dao.mapper.WorksMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.UserUtils;
import com.fastclip.dao.utils.WorksUtils;
import com.fastclip.llm.ask.QwenAsk;
import com.fastclip.service.baseCover.BaseCoverService;
import com.fastclip.service.project.ProjectService;
import com.fastclip.service.works.WorksService;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import static com.fastclip.opencv.utils.FrameUtils.createWorksCover;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class WorksCombineTasks {

    @Autowired
    WorksMapper worksMapper;

    @Autowired
    WorksService worksService;

    @Value("${schedule.compile_enabled}")
    Boolean scheduleEnabled;

    @Value("${ffmpeg.coverBasePath}")
    String coverBasePath;

    @Autowired
    QwenAsk qwenAsk;

    @Autowired
    LoadingCache<Integer, FilterEffect> filterCache;

    @Autowired
    SellerMapper sellerMapper;


    @Autowired
    ProjectMapper projectMapper;

    @Autowired
    BaseCoverService baseCoverService;

    @Autowired
    FUserMapper fUserMapper;

    @Scheduled(fixedDelay = 1000)
    public void exportWorks() {
        try {
            FUser user = fUserMapper.selectByPrimaryKey(1L);
            SsoUserContext.setUser(UserUtils.do2DTO(user));
            if (!scheduleEnabled) {
                return;
            }
            //随机睡眠0-1s，防止冲突
            try {
                Thread.sleep((int) (1000 * Math.random()));
            } catch (Exception e) {

            }
            WorksExample example = new WorksExample();
            example.createCriteria().andComposeStatusEqualTo(ComposeStatusEnum.WAITING.getCode());
            example.setOrderByClause("create_time");
            RowBounds rowBounds = new RowBounds(0, 1);
            List<Works> worksList = worksMapper.selectByExampleWithRowbounds(example, rowBounds);
            if (CollectionUtils.isEmpty(worksList)) {
                return;
            }
            WorksDTO worksDTO = worksService.getWorksById(worksList.get(0).getId());
            log.info("start to combine worksDTO={}", worksDTO);
            worksDTO.setComposeStatus(ComposeStatusEnum.PROCESSING.getCode());
            //乐观锁
            if (isWorksComposeStatusWaiting(worksDTO.getId())) {
                worksMapper.updateByPrimaryKeySelective(WorksUtils.dto2DO(worksDTO));
            } else {
                log.info("乐观锁：已经有任务在处理，直接返回。");
                return;
            }
            worksService.createWorksDesc(worksDTO.getId());
            Project project = projectMapper.selectByPrimaryKey(worksDTO.getProjectId());
            if(project.getCover() != null) {
                String cover = worksService.createWorksCover(baseCoverService.getRandomBaseCoverDTO().getPath(),
                        coverBasePath, project.getCover(), worksDTO);
                worksDTO.setWorksCover(cover);
                worksService.updateWorks(worksDTO);
            }
            worksService.combine(worksDTO.getId());
            log.info("finished combine worksDTO={}", worksDTO);
        }catch (Exception e) {
            log.error("combine error", e);
        }
    }

    private boolean isWorksComposeStatusWaiting(Long id) {
        Works works = worksMapper.selectByPrimaryKey(id);
        if(ComposeStatusEnum.WAITING.getCode().equals(works.getComposeStatus())) {
            return true;
        }
        return false;
    }

}
