package com.fastclip.service.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class SubtitlesHttpUtils {

    @Autowired
    private HttpUtils httpUtils;

    public String getSubtitlesData(String inputPath, String outputPath) {
        String URL = "http://***********:8090/api/fetchSubtitles";
        //1、构建body参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("input", inputPath);
        jsonObject.put("output", outputPath);

        //2、添加请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        return httpUtils.post(URL, headers, jsonObject);

    }
}
