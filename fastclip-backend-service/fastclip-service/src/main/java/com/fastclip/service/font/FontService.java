package com.fastclip.service.font;

import com.fastclip.common.model.dto.FontEffectDTO;
import com.fastclip.dao.mapper.FontEffectMapper;
import com.fastclip.dao.model.dataobject.FontEffect;
import com.fastclip.dao.model.dataobject.FontEffectExample;
import com.fastclip.dao.utils.FontEffectUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FontService {

    @Autowired
    FontEffectMapper fontEffectMapper;

    public List<FontEffectDTO> getFontEffectDTOs() {
        FontEffectExample fontEffectExample = new FontEffectExample();
        List<FontEffect> fontEffects = fontEffectMapper.selectByExample(fontEffectExample);
        return FontEffectUtils.do2DTOs(fontEffects);
    }

    public FontEffectDTO getRandomEffectDTO() {
        List<FontEffectDTO> fontEffectDTOS = getFontEffectDTOs();
        int index = (int) (Math.random() * fontEffectDTOS.size());
        return fontEffectDTOS.get(index);
    }
}
