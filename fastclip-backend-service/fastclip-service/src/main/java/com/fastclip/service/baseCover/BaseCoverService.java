package com.fastclip.service.baseCover;

import com.fastclip.common.model.dto.BaseCoverDTO;
import com.fastclip.common.model.dto.StickerDTO;
import com.fastclip.dao.mapper.BaseCoverMapper;
import com.fastclip.dao.mapper.StickerMapper;
import com.fastclip.dao.model.dataobject.BaseCover;
import com.fastclip.dao.model.dataobject.BaseCoverExample;
import com.fastclip.dao.model.dataobject.Sticker;
import com.fastclip.dao.model.dataobject.StickerExample;
import com.fastclip.dao.utils.BaseCoverUtils;
import com.fastclip.dao.utils.StickerUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BaseCoverService {

    @Autowired
    BaseCoverMapper baseCoverMapper;

    public List<BaseCoverDTO> getBaseCoverDTOs() {
        BaseCoverExample baseCoverExample = new BaseCoverExample();
        baseCoverExample.createCriteria();
        List<BaseCover> stickers = baseCoverMapper.selectByExample(baseCoverExample);
        return BaseCoverUtils.do2DTOs(stickers);
    }


    public BaseCoverDTO getRandomBaseCoverDTO() {
        List<BaseCoverDTO> baseCoverDTOS = getBaseCoverDTOs();
        int index = (int) (Math.random() * baseCoverDTOS.size());
        return baseCoverDTOS.get(index);
    }
}
