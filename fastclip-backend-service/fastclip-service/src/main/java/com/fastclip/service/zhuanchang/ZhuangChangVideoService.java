package com.fastclip.service.zhuanchang;

import com.fastclip.common.model.dto.VideoClipToCut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ZhuangChangVideoService {

    @Value("${zhuanchang.folderPath}")
    String zhuanchangFolderPath;

    public List<VideoClipToCut> getZhuanchangVideos(List<VideoClipToCut> oldClips) {
        List<VideoClipToCut> newClips = new ArrayList<>();
        for(int i=0; i < oldClips.size(); i++) {
            newClips.add(oldClips.get(i));
            if(i == 0) {
                newClips.add(newNiceVideo());
            }
            if(i == 1) {
                newClips.add(newGouxiongVideo());
            }
        }
        return newClips;
    }

    private VideoClipToCut newGouxiongVideo() {
        VideoClipToCut videoClip = new VideoClipToCut();
        videoClip.setDuration(3250);
        videoClip.setPath(zhuanchangFolderPath + "gouxiong.mp4");
        videoClip.setSpecialEffects(new ArrayList<>());
        return videoClip;
    }

    private VideoClipToCut newNiceVideo() {
        VideoClipToCut videoClip = new VideoClipToCut();
        videoClip.setDuration(2510);
        videoClip.setPath(zhuanchangFolderPath + "nice.mp4");
        videoClip.setSpecialEffects(new ArrayList<>());
        return videoClip;
    }
}
