package com.fastclip.service.works;

import com.fastclip.common.constant.ComposeStatusEnum;
import com.fastclip.common.constant.DownloadTypeEnum;
import com.fastclip.common.constant.ProcessorEnum;
import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dataobject.AccountWorksDO;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.dto.specialEffect.FilterEffectDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.utils.NumberUtils;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.MusicMapper;
import com.fastclip.dao.mapper.WorksDetailMapper;
import com.fastclip.dao.mapper.WorksMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.FilterEffectUtils;
import com.fastclip.dao.utils.MusicUtils;
import com.fastclip.dao.utils.WorksUtils;
import com.fastclip.llm.ask.QwenAsk;
import com.fastclip.opencv.FFmpegService;
import com.fastclip.opencv.grabber.WorksGrabber;
import com.fastclip.opencv.utils.FrameUtils;
import com.fastclip.service.auth.TokenService;
import com.fastclip.service.baseCover.BaseCoverService;
import com.fastclip.service.douyin.AccountService;
import com.fastclip.service.font.FontService;
import com.fastclip.service.project.ProjectService;
import com.fastclip.service.video.VideoClipService;
import com.fastclip.service.video.VideoMaterialService;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacpp.Loader;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.fastclip.opencv.utils.FrameUtils.createWorksCover;

@Service
@Slf4j
public class WorksService {

    @Autowired
    WorksProduceWithOutTagStrategy worksProduceStrategy;

    @Autowired
    ProjectService projectService;

    @Autowired
    LoadingCache<Long, SellerDTO> sellerCache;

    @Autowired
    WorksMapper worksMapper;

    @Autowired
    WorksDetailMapper worksDetailMapper;

    @Autowired
    VideoClipService videoClipService;

    @Autowired
    LoadingCache<Long, ItemDTO> itemCache;

    @Autowired
    LoadingCache<Integer, FilterEffect> filterCache;

    @Autowired
    FFmpegService fFmpegService;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Value("${ffmpeg.workBasePath}")
    String workBasePath;

    @Value("${ffmpeg.worksTmpPath}")
    String worksTmpPath;

    @Value("${ffmpeg.coverBasePath}")
    String coverBasePath;

    @Autowired
    LoadingCache<Long, UserDTO> userCache;

    @Autowired
    LoadingCache<Integer, FontEffectDTO> fontEffectCache;

    @Autowired
    MusicMapper musicMapper;

    @Value("${ffmpeg.endingVideoPath}")
    String endingVideoPath;

    @Value("${ffmpeg.processor}")
    String processor;


    @Autowired
    LoadingCache<Integer, StickerDTO> stickerCache;

    @Autowired
    QwenAsk qwenAsk;

    @Autowired
    AccountService accountService;

    @Autowired
    DownloadService downloadService;

    @Autowired
    FontService fontService;

    @Autowired
    BaseCoverService baseCoverService;

    @Autowired
    TokenService tokenService;

    @Value("${download.ip}")
    String downloadIP;

    @Value("${download.localIp}")
    String downloadLocalIP;

    /**
     * 删除作品接口
     * @param req
     * @return
     */
    @Transactional
    public boolean deleteWorks(DeleteWorksReq req) {
        log.info("start to delete works. req:{}", req);
        WorksExample worksExample = new WorksExample();
        worksExample.createCriteria().andIdIn(req.getWorksIds());
        int res1 = worksMapper.deleteByExample(worksExample);
        WorksDetailExample worksDetailExample = new WorksDetailExample();
        worksDetailExample.createCriteria().andWorksIdIn(req.getWorksIds());
        int res2 = worksDetailMapper.deleteByExample(worksDetailExample);
        if(res1 > 0 && res2 > 0) {
            return true;
        }
        log.info("finish delete works. req:{}", req);
        return false;
    }
    public PagebleRes<WorksDTO> getPageableWorks(GetWorksReq req) {
        log.info("start to get pageable works. req:{}", req);
        UserDTO userDTO = SsoUserContext.getUser();
        if(userDTO != null) {
            req.setIsAdmin(userDTO.getIsAdmin());
        }
        WorksExample worksExample = createWorksExample(req);
        if (req.getPageNum() == null) {
            req.setPageNum(1);
        }
        if (req.getPageSize() == null) {
            req.setPageSize(20);
        }
        RowBounds rowBounds = new RowBounds((req.getPageNum() - 1) * req.getPageSize(), req.getPageSize());
        List<Works> works = this.worksMapper.selectByExampleWithRowbounds(worksExample, rowBounds);
        List<WorksDTO> worksDTOs = WorksUtils.do2DTOs(works);
        List<Long> allVideoClipsIds = new ArrayList<>();
        for (WorksDTO worksDTO : worksDTOs) {
            worksDTO.setSeller(this.sellerCache.get(worksDTO.getSellerId()));
            worksDTO.setItem(this.itemCache.get(worksDTO.getItemId()));
            worksDTO.setCreator(this.userCache.get(worksDTO.getCreatorId()));
            worksDTO.setStickerDTO(this.stickerCache.get(worksDTO.getStickerId()));
            worksDTO.setLeftTopStickerDTO(this.stickerCache.get(worksDTO.getLeftTopStickerId()));
            worksDTO.setLeftBottomStickerDTO(this.stickerCache.get(worksDTO.getLeftBottomStickerId()));
            worksDTO.setRightTopStickerDTO(this.stickerCache.get(worksDTO.getRightTopStickerId()));
            worksDTO.setRightBottomStickerDTO(this.stickerCache.get(worksDTO.getRightBottomStickerId()));
            worksDTO.setFontEffectDTO(getFontDTO(worksDTO.getFontId()));
            WorksDetailExample worksDetailExample = new WorksDetailExample();
            worksDetailExample.createCriteria().andWorksIdEqualTo(worksDTO.getId());
            List<WorksDetail> worksDetails = this.worksDetailMapper.selectByExample(worksDetailExample);
            List<WorksDetailDTO> worksDetailDTOS = WorksUtils.detailsDo2DTOs(worksDetails);
            List<Long> videoClipsIds = worksDetails.stream().map(WorksDetail::getVideoClipId).collect(Collectors.toList());
            allVideoClipsIds.addAll(videoClipsIds);
            worksDTO.setDetails(worksDetailDTOS);
            if(worksDTO.getWorksCover() != null) {
                worksDTO.setDownloadCoverUrl(downloadIP + "?token=" + userDTO.getToken() + "&type=" +
                        DownloadTypeEnum.COVER.getCode() + "&worksId=" + worksDTO.getId());
                worksDTO.setLocalDownloadCoverUrl(downloadLocalIP + "?token=" + userDTO.getToken() + "&type=" +
                        DownloadTypeEnum.COVER.getCode() + "&worksId=" + worksDTO.getId());
            }
            if(worksDTO.getVideoPath() != null) {
                worksDTO.setDownloadWorksUrl(downloadIP + "?token=" + userDTO.getToken() + "&type=" +
                        DownloadTypeEnum.VIDEO.getCode() + "&worksId=" + worksDTO.getId());
                worksDTO.setDownloadWorksWithoutAssUrl(downloadIP + "?token=" + userDTO.getToken() + "&type=" +
                        DownloadTypeEnum.VIDEOWITHOUTASS.getCode() + "&worksId=" + worksDTO.getId());
                worksDTO.setLocalDownloadWorksUrl(downloadLocalIP + "?token=" + userDTO.getToken() + "&type=" +
                        DownloadTypeEnum.VIDEO.getCode() + "&worksId=" + worksDTO.getId());
                worksDTO.setLcoalDownloadWorksWithoutAssUrl(downloadLocalIP + "?token=" + userDTO.getToken() + "&type=" +
                        DownloadTypeEnum.VIDEOWITHOUTASS.getCode() + "&worksId=" + worksDTO.getId());
            }
        }
        List<VideoClipDTO> videoClipDTOS = this.videoClipService.getVideoClipDTOs(allVideoClipsIds);
        Map<Long, VideoClipDTO> videoClipDTOMap = new HashMap<>();
        videoClipDTOS.forEach(videoClipDTO -> videoClipDTOMap.put(videoClipDTO.getId(),videoClipDTO));
        for (WorksDTO worksDTO : worksDTOs) {
            for (WorksDetailDTO worksDetailDTO : worksDTO.getDetails()) {
                worksDetailDTO.setVideoClipDTO(videoClipDTOMap.get(worksDetailDTO.getVideoClipId()));
            }
        }
        long count = this.worksMapper.countByExample(worksExample);
        PagebleRes<WorksDTO> pagebleRes = new PagebleRes();
        pagebleRes.setTotal(Integer.valueOf((int)count));
        pagebleRes.setData(worksDTOs);
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setPageNum(req.getPageNum());
        log.info("finish get pageable works. req:{}", req);
        return pagebleRes;
    }

    private FontEffectDTO getFontDTO(Integer fontId) {
        FontEffectDTO fontEffectDTO = this.fontEffectCache.get(fontId);
        if(fontEffectDTO == null) {
            return fontService.getRandomEffectDTO();
        }
        return fontEffectDTO;
    }

    public List<WorksDTO> getWorks(GetWorksReq req) {
        log.info("start to get works. req:{}", req);
        UserDTO userDTO = SsoUserContext.getUser();
        if(userDTO != null) {
            req.setIsAdmin(userDTO.getIsAdmin());
        }
        WorksExample worksExample = createWorksExample(req);
        List<Works> worksList = this.worksMapper.selectByExample(worksExample);
        List<WorksDTO> worksDTOList = WorksUtils.do2DTOs(worksList);
        for (WorksDTO worksDTO : worksDTOList) {
            WorksDetailExample worksDetailExample = new WorksDetailExample();
            worksDetailExample.createCriteria().andWorksIdEqualTo(worksDTO.getId());
            worksDetailExample.setOrderByClause("sort");
            List<WorksDetail> worksDetails = this.worksDetailMapper.selectByExample(worksDetailExample);
            List<WorksDetailDTO> worksDetailDTOS = WorksUtils.detailsDo2DTOs(worksDetails);
            for (WorksDetailDTO worksDetailDTO : worksDetailDTOS) {
                VideoClipDTO videoClipDTO = this.videoClipService.getVedioClipById(worksDetailDTO.getVideoClipId());
                worksDetailDTO.setVideoClipDTO(videoClipDTO);
            }
            worksDTO.setCreator(this.userCache.get(worksDTO.getCreatorId()));
            worksDTO.setDetails(worksDetailDTOS);
            worksDTO.setEndingVideoPath(this.endingVideoPath);
            worksDTO.setStickerDTO(this.stickerCache.get(worksDTO.getStickerId()));
            worksDTO.setLeftTopStickerDTO(this.stickerCache.get(worksDTO.getLeftTopStickerId()));
            worksDTO.setLeftBottomStickerDTO(this.stickerCache.get(worksDTO.getLeftBottomStickerId()));
            worksDTO.setRightTopStickerDTO(this.stickerCache.get(worksDTO.getRightTopStickerId()));
            worksDTO.setRightBottomStickerDTO(this.stickerCache.get(worksDTO.getRightBottomStickerId()));
            worksDTO.setProjectDTO(this.projectService.getProject(worksDTO.getProjectId()));
            worksDTO.setFontEffectDTO(getFontDTO(worksDTO.getFontId()));
            worksDTO.setSeller(sellerCache.get(worksDTO.getSellerId()));
            worksDTO.setFilterEffectDTO(FilterEffectUtils.do2DTO((FilterEffect)this.filterCache.get(worksDTO.getFilterId())));
            Music music = this.musicMapper.selectByPrimaryKey(worksDTO.getMusicId());
            worksDTO.setMusicDTO(MusicUtils.do2DTO(music));
        }
        log.info("finish get works. req:{}", req);
        return worksDTOList;
    }


    public WorksDTO getWorksById(Long worksId) {
        log.info("start to get works by id. id:{}", worksId);
        if(worksId == null) {
            return null;
        }
        GetWorksReq req = new GetWorksReq();
        req.setWorksId(worksId);
        List<WorksDTO> worksDTOS = getWorks(req);
        if(CollectionUtils.isEmpty(worksDTOS)) {
            return null;
        }
        return worksDTOS.get(0);
    }

    public Boolean addToTaskList(Long worksId) {
        Works record = new Works();
        record.setId(worksId);
        record.setIsComposed(false);
        record.setComposeStatus(ComposeStatusEnum.WAITING.getCode());
        int res = worksMapper.updateByPrimaryKeySelective(record);
        if(res > 0) {
            return true;
        }
        return false;
    }

    public Boolean createWorksDesc(Long worksId) {
        GetWorksReq req = new GetWorksReq();
        req.setWorksId(worksId);
        WorksDTO worksDTO = getWorks(req).get(0);
        worksDTO.setItem(itemCache.get(worksDTO.getItemId()));
        createWorksDesc(worksDTO);
        return true;
    }

    private void createWorksDesc(WorksDTO worksDTO) {
        String itemName = worksDTO.getItem().getItemName();
        String simpleItemName = qwenAsk.askItemName(itemName);
        worksDTO.setSimpleItemName(simpleItemName);
        worksDTO.setWorksName("【"+ sellerCache.get(worksDTO.getSellerId()).getSellerName() + "】" + qwenAsk.askWorksName(itemName));
        worksDTO.setWorksDesc(qwenAsk.askWorksDesc(simpleItemName  + "穿搭"));
        String tagOfItemName = qwenAsk.askWorksTag(simpleItemName);
        worksDTO.setWorksTag(getWorksTags(tagOfItemName));
        String itemFeatures = qwenAsk.askItemFeature(itemName);
        worksDTO.setItemFeatures(itemFeatures);
        String itemTags = qwenAsk.askItemTags(itemName);
        worksDTO.setItemTags(itemTags);
        String content = getSubtitlesContent(worksDTO);
        String keywords = qwenAsk.askKeywords(content);
        worksDTO.setKeywords(keywords);
        worksDTO.setUpdateTime(new Date());
        worksDTO.setIsDescCompleted(true);
        worksDTO.setComposeStatus(null);
        worksMapper.updateByPrimaryKeySelective(WorksUtils.dto2DO(worksDTO));
    }

    private String getSubtitlesContent(WorksDTO worksDTO) {
        List<WorksDetailDTO> worksDetailDTOS = worksDTO.getDetails();
        StringBuilder content = new StringBuilder();
        for(WorksDetailDTO worksDetailDTO: worksDetailDTOS) {
            content.append(worksDetailDTO.getVideoClipDTO().getSubtitles()).append("\n");
        }
        return content.toString();
    }

    /**
     * 将作品合成视频
     * @param worksId
     * @return
     */
    public Boolean combine(Long worksId){
        log.info("start to compose worksId = {}", worksId);
        Works works =new Works();
        works.setId(worksId);
        works.setComposeStatus(ComposeStatusEnum.PROCESSING.getCode());
        worksMapper.updateByPrimaryKeySelective(works);
        WorksGrabber worksGrabber = new WorksGrabber();
        GetWorksReq req = new GetWorksReq();
        req.setWorksId(worksId);
        req.setIsAdmin(true);
        List<WorksDTO> worksDTOList = getWorks(req);
        WorksDTO worksDTO = worksDTOList.get(0);
        worksGrabber.setWorksDTO(worksDTO);
        worksGrabber.setVideoOrAudio(VideoOrAudioTypeEnum.VIDEO.getCode());
        worksGrabber.start();
        String videoPath = workBasePath + "/" + worksDTO.getCreator().getUserName() + "_" +
                worksDTO.getSeller().getSellerName() + "_" + worksDTO.getProjectId() + "_" + worksDTO.getId() + ".mp4";
        String videoPathWithOutAss = workBasePath + "/" + worksDTO.getCreator().getUserName() + "_" +
                worksDTO.getSeller().getSellerName() + "_" + worksDTO.getProjectId() + "_" + worksDTO.getId() + "_no_ass.mp4";
        String videoTmpPath = worksTmpPath + "/" + worksDTO.getProjectId() + "_" + worksDTO.getId() + ".mp4";
        FFmpegFrameRecorder ffmpegFrameRecorder = new FFmpegFrameRecorder(videoTmpPath, worksGrabber.getImageWidth(),
                worksGrabber.getImageHeight(), 2);
        int i=0;
        double fps =  worksGrabber.getFrameRate();
        try {
            if(fps <= 0) {
                fps = 22;
            }
            ffmpegFrameRecorder.setFormat("mp4");
            ffmpegFrameRecorder.setFrameRate(fps);
            long timePerFrame = (long)(1000000L/fps);
            ffmpegFrameRecorder.setVideoBitrate(worksGrabber.getVideoBitrate());
            if(ProcessorEnum.GPU.getValue().equals(processor)) {
                ffmpegFrameRecorder.setVideoCodecName("h264_nvenc");
            }
            else {
                ffmpegFrameRecorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            }
            ffmpegFrameRecorder.setVideoOption("sws_flags", "bilinear");
            ffmpegFrameRecorder.setSampleRate(worksGrabber.getSampleRate());
            ffmpegFrameRecorder.setInterleaved(true);
            ffmpegFrameRecorder.start();
            Frame imageFrame = worksGrabber.grabImage();
            long preTimestamp = 0L;
            long preWorksDetailIndex = 0;
            //帧数*每帧所得的时间
            long totalTimes = 0L;
            //帧与帧之间时间搓相减再累加所得时间
            long totalFrameTimes = 0L;
            while (imageFrame != null) {
                System.out.println(worksGrabber.getCurSubtitlesId() + "-----imageFrame time:" + TimeUtils.getTimeStr(imageFrame.timestamp));
                long gap = imageFrame.timestamp - preTimestamp;
                //补帧
                while(totalFrameTimes - totalTimes >= timePerFrame) {
                    ffmpegFrameRecorder.record(imageFrame);
                    totalTimes += timePerFrame;
                    System.out.println("totalTimes:" + totalTimes + ",totalFrameTimes:" + totalFrameTimes);
                    System.out.println("补帧：imageFrame time:" + TimeUtils.getTimeStr(imageFrame.timestamp));
                }
                ffmpegFrameRecorder.record(imageFrame);
                totalTimes += timePerFrame;
                preTimestamp = imageFrame.timestamp;
//                preWorksDetailIndex = worksGrabber.getCurSubtitlesId();
                imageFrame = worksGrabber.grabImage();
                if(worksGrabber.isCurFrameStartOfClip()) {
                    totalFrameTimes+= timePerFrame;
                }else if(imageFrame != null){
                    totalFrameTimes += imageFrame.timestamp - preTimestamp;
                }
                System.out.println("totalTimes:" + totalTimes + ",totalFrameTimes:" + totalFrameTimes);
            }
            worksGrabber.setVideoOrAudio(VideoOrAudioTypeEnum.AUDIO.getCode());
            worksGrabber.start();
            Frame sampleFrame = worksGrabber.grabSamples();
            while (sampleFrame != null) {
                System.out.println("sampleFrame time:" + TimeUtils.getTimeStr(sampleFrame.timestamp));
                ffmpegFrameRecorder.record(sampleFrame);
                sampleFrame = worksGrabber.grabSamples();
            }
            ffmpegFrameRecorder.stop();
            ffmpegFrameRecorder.release();
        }catch (Exception e) {
            log.error("combine error", e);
            try {
                worksGrabber.close();
                ffmpegFrameRecorder.close();
            }catch (Exception er) {
                log.error("close grabber error", er);
            }
            return false;
        }

        DecimalFormat df = new DecimalFormat("#.00");
        FilterEffectDTO filterEffect = worksDTO.getFilterEffectDTO();
        String contrastStr = df.format(filterEffect.getContrast()/(double)100 + 1);
        String brightnessStr = df.format(filterEffect.getLightness()/(double)100 );
        String saturationStr = df.format(filterEffect.getSaturate()/(double)100 + 1);
        SpecialEffectReq effectReq = new SpecialEffectReq();
        effectReq.setInputVideoPath(videoTmpPath);
        effectReq.setMusicPath(worksDTO.getMusicDTO().getPath());
        effectReq.setOutputVideoPath(videoPath);
        effectReq.setOutputVideoPathWithOutAss(videoPathWithOutAss);
        double speed = (100d + worksDTO.getSpeedUp())/100;
        effectReq.setVideoPts(NumberUtils.doubleToStr(1/speed));
        effectReq.setAudioAtempo(NumberUtils.doubleToStr(speed));
        effectReq.setSaturationStr(saturationStr);
        effectReq.setContrastStr(contrastStr);
        effectReq.setBrightnessStr(brightnessStr);
        effectReq.setEndingVideoPath(endingVideoPath);
        effectReq.setVideoClipSpeicalEffectDTOList(createVideoClipSpecialEffects(worksDTO, worksGrabber.getImageWidth(), worksGrabber.getImageHeight()));
        effectReq.setStickerPath(worksDTO.getStickerDTO().getPath());
        effectReq.setLeftTopStickerPath(worksDTO.getLeftTopStickerDTO().getPath());
        effectReq.setLeftBottomStickerPath(worksDTO.getLeftBottomStickerDTO().getPath());
        effectReq.setRightTopStickerPath(worksDTO.getRightTopStickerDTO().getPath());
        effectReq.setRightBottomStickerPath(worksDTO.getRightBottomStickerDTO().getPath());
        effectReq.setWidth(worksGrabber.getImageWidth());
        effectReq.setHeight(worksGrabber.getImageHeight());
        effectReq.setFontEffectDTO(worksDTO.getFontEffectDTO());
        effectReq.setKeywords(worksDTO.getKeywords());
        fFmpegService.specialEffect(effectReq);
        worksDTO.setVideoPath(videoPath);
        worksDTO.setVideoPathWithoutAss(videoPathWithOutAss);
        worksDTO.setIsComposed(true);
        worksDTO.setUpdateTime(new Date());
        worksDTO.setWorksName(null);
        worksDTO.setWorksDesc(null);
        worksDTO.setSimpleItemName(null);
        worksDTO.setWorksTag(null);
        worksDTO.setWorksCover(null);
        worksDTO.setComposeStatus(ComposeStatusEnum.FINISH.getCode());
        worksMapper.updateByPrimaryKeySelective(WorksUtils.dto2DO(worksDTO));
        log.info("finish composed worksId = {}", worksId);
        return true;
    }



    private List<VideoClipSpeicalEffectDTO> createVideoClipSpecialEffects(WorksDTO worksDTO, Integer width, Integer height) {
        List<VideoClipSpeicalEffectDTO> effectDTOS = new ArrayList<>();
        for(WorksDetailDTO worksDetailDTO: worksDTO.getDetails()) {
            VideoClipSpeicalEffectDTO effectDTO = new VideoClipSpeicalEffectDTO();
            effectDTO.setVideoClipDTO(worksDetailDTO.getVideoClipDTO());
            effectDTO.setXfadeTransitionCode(worksDetailDTO.getTranCode());
            effectDTO.setFlipFlag(worksDetailDTO.getFlipFlag());
            effectDTO.setMaxCutPercent(worksDetailDTO.getCutPercent());
            effectDTOS.add(effectDTO);
            effectDTO.setWidth(width);
            effectDTO.setHeight(height);
        }
        return effectDTOS;
    }

    private WorksExample createWorksExample(GetWorksReq req) {
        UserDTO userDTO = SsoUserContext.getUser();
        WorksExample worksExample = new WorksExample();
        WorksExample.Criteria criteria = worksExample.createCriteria();
        if(req.getWorksId() != null) {
            criteria.andIdEqualTo(req.getWorksId());
        }
        if(!CollectionUtils.isEmpty(req.getItemIds())) {
            criteria.andItemIdIn(req.getItemIds());
        }
        if(!CollectionUtils.isEmpty(req.getSellerIds())) {
            criteria.andSellerIdIn(req.getSellerIds());
        }
        if(!CollectionUtils.isEmpty(req.getProjectIds())) {
            criteria.andProjectIdIn(req.getProjectIds());
        }
        if(!req.getIsAdmin()) {
            criteria.andCreatorIdEqualTo(userDTO.getId());
        }
        if(req.getIsDescCompleted() != null) {
            criteria.andIsDescCompletedEqualTo(req.getIsDescCompleted());
        }
        worksExample.setOrderByClause("create_time desc");
        return worksExample;
    }


    @Transactional
    public boolean createWorks(CreateWorksReq req) {
        try {
            log.info("start to create works. req:{}", req);
            if (req.getWorksNum() == 0) {
                req.setWorksNum(1);
            } else if(req.getWorksNum() > 20) {
                throw new RuntimeException("最大不能超过20");
            }
            if(req.getSpeedUp() == null || req.getSpeedUp() <0) {
                req.setSpeedUp(0);
            }else if(req.getSpeedUp() >=20) {
                req.setSpeedUp(20);
            }
            ProjectDTO projectDTO = projectService.getProject(req.getProjectId());
//            if (projectDTO.getCover() == null) {
//                throw new RuntimeException("请设置封面");
//            }
            GetWorksReq getWorksReq = new GetWorksReq();
            List<Long> projectIds = new ArrayList<>();
            projectIds.add(req.getProjectId());
            getWorksReq.setProjectIds(projectIds);
            GetVideoClipsReq getVideoClips = new GetVideoClipsReq();
            getVideoClips.setProjectId(req.getProjectId());
            List<VideoClipDTO> videoClipDTOS = videoClipService.getVideoClips(getVideoClips).getVideoClipList();
            if(CollectionUtils.isEmpty(videoClipDTOS) || videoClipDTOS.size() <=2) {
                throw new RuntimeException("视频片段数量至少大于3，");
            }
            int duration = 0;
            for(VideoClipDTO videoClipDTO: videoClipDTOS) {
                duration += videoClipDTO.getDuration();
            }
            if(duration<=10000) {
                throw new RuntimeException("视频片段时间总长要大于10s");
            }
            List<WorksDTO> worksDTOList = new ArrayList<>();
            for (int i = 0; i < req.getWorksNum(); i++) {
                List<WorksDTO> curWorks = getWorks(getWorksReq);
                List<Long> usedAccountIds = curWorks.stream().map(WorksDTO::getAccountId).collect(Collectors.toList());
                List<DouyinAccountDTO> allAccounts = accountService.getDouyinAccountsBySeller(projectDTO.getSellerId());
                DouyinAccountDTO douyinAccountDTO = getRandomDouyinAccount(allAccounts, usedAccountIds);
                WorksDTO worksDTO = worksProduceStrategy.produce(projectDTO, curWorks, videoClipDTOS);
                worksDTO.setAccountId(douyinAccountDTO.getId());
                worksDTO.setPhone(douyinAccountDTO.getPhone());
                worksDTO.setSeller(sellerCache.get(worksDTO.getSellerId()));
                worksDTO.setCreator(userCache.get(worksDTO.getCreatorId()));
                worksDTO.setItem(itemCache.get(projectDTO.getItemId()));
                worksDTO.setIsDescCompleted(false);
                worksDTO.setSpeedUp(req.getSpeedUp());
                insertWorks(worksDTO);
                worksDTO.setDouyinAccountDTO(douyinAccountDTO);
                worksDTOList.add(worksDTO);
            }
//            //异步提交执行合成视频
//            Runnable task = () -> {
//                for (WorksDTO worksDTO : worksDTOList) {
//                    createWorksDesc(worksDTO);
//                    if(projectDTO.getCover() != null) {
//                        worksDTO.setWorksCover(createWorksCover(baseCoverService.getRandomBaseCoverDTO().getPath(),
//                                coverBasePath, projectDTO.getCover(), worksDTO));
//                    }
//                    updateWorks(worksDTO);
//                }
//            };
//            Thread thread = new Thread(task);
//            thread.start();
            log.info("finish create works. req:{}", req);
            return true;
        }catch (Exception e) {
            log.error("create works error", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    public String createWorksCover(String backgroundPath, String coverBasePath, String projectCover,
                                        WorksDTO worksDTO) {
        try {
            if (worksDTO.getSeller() == null) {
                worksDTO.setSeller(sellerCache.get(worksDTO.getSellerId()));
            }
            if (worksDTO.getCreator() == null) {
                worksDTO.setCreator(userCache.get(worksDTO.getCreatorId()));
            }
            if (worksDTO.getItem() == null) {
                worksDTO.setItem(itemCache.get(worksDTO.getItemId()));
            }
            return FrameUtils.createWorksCover(backgroundPath, coverBasePath, projectCover, worksDTO);
        }catch (Exception e) {
            log.error("create works cover error", e);
            return null;
        }
    }

    private DouyinAccountDTO getRandomDouyinAccount(List<DouyinAccountDTO> allAccounts,List<Long> usedIds) {
        for(DouyinAccountDTO douyinAccountDTO: allAccounts) {
            if(!usedIds.contains(douyinAccountDTO.getId())) {
                return douyinAccountDTO;
            }
        }
        if(CollectionUtils.isEmpty(allAccounts)) {
            throw new RuntimeException("没有可用的抖音账号！");
        }
        return allAccounts.get(0);
    }

    private String getWorksTags(String simpleItemName) {
        return  "#" + simpleItemName + " " +
                "#" + simpleItemName + "穿搭 " +
                "#" + simpleItemName + "推荐 " +
                "#百搭" + simpleItemName + " " +
                "#" + simpleItemName + "搭配 ";
    }


    private boolean insertWorks(WorksDTO worksDTO) {
        Works works = WorksUtils.dto2DO(worksDTO);
        worksMapper.insert(works);
        worksDTO.setId(works.getId());
        for (WorksDetailDTO worksDetailDTO : worksDTO.getDetails()) {
            worksDetailDTO.setWorksId(works.getId());
            WorksDetail worksDetail = WorksUtils.detailsDto2DO(worksDetailDTO);
            worksDetailMapper.insert(worksDetail);
        }
        return true;

    }

    public boolean updateWorks(WorksDTO worksDTO) {
        Works works = WorksUtils.dto2DO(worksDTO);
        int res = worksMapper.updateByPrimaryKeySelective(works);
        return res > 0;
    }

    /**
     * 发布作品
     * @param req
     * @return
     */
    public Boolean publishWorks(PublishWorksReq req) {
        log.info("start to get pageable works. req:{}", req);
        Works works = new Works();
        works.setId(req.getId());
        works.setIsPublished(true);
        works.setPublishedTime(new Date());
        worksMapper.updateByPrimaryKeySelective(works);
        return true;
    }

    public ResponseEntity<Resource> download(Long worksId) {

        try {
            WorksDTO worksDTO = getWorksById(worksId);
            if(worksDTO == null || worksDTO.getVideoPath() == null) {
                throw new RuntimeException("作品无法下载，请重新生成视频");
            }
            File file = new File(worksDTO.getVideoPath());
            String filename = URLEncoder.encode(file.getName(), "UTF-8");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", filename);
            headers.add("filename",  filename);
            Path filePath = Paths.get(worksDTO.getVideoPath());
            Resource resource = new InputStreamResource(Files.newInputStream(filePath));
            downloadService.log(DownloadTypeEnum.VIDEO, worksId);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(Files.size(filePath))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public ResponseEntity<Resource> downloadWithoutAss(Long worksId) {

        try {
            WorksDTO worksDTO = getWorksById(worksId);
            if(worksDTO == null || worksDTO.getVideoPathWithoutAss() == null) {
                throw new RuntimeException("作品无法下载，请重新生成视频");
            }
            File file = new File(worksDTO.getVideoPathWithoutAss());
            String filename = URLEncoder.encode(file.getName(), "UTF-8");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", filename);
            headers.add("filename",  filename);
            Path filePath = Paths.get(worksDTO.getVideoPathWithoutAss());
            Resource resource = new InputStreamResource(Files.newInputStream(filePath));
            downloadService.log(DownloadTypeEnum.VIDEOWITHOUTASS, worksId);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(Files.size(filePath))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return null;
    }


    public ResponseEntity<Resource> downloadCover(Long worksId) {

        try {
            WorksDTO worksDTO = getWorksById(worksId);
            if(worksDTO == null || worksDTO.getWorksCover() == null) {
                throw new RuntimeException("作品封面无法下载");
            }
            File file = new File(worksDTO.getWorksCover());
            String filename = URLEncoder.encode(file.getName(), "UTF-8");
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-disposition","attachment;filename=" + filename);
            headers.add("filename", filename);
            Path filePath = Paths.get(worksDTO.getWorksCover());
            Resource resource = new InputStreamResource(Files.newInputStream(filePath));
            downloadService.log(DownloadTypeEnum.COVER, worksId);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(Files.size(filePath))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public void deleteWorksByClipId(Long clipId) {
        WorksDetailExample worksDetailExample = new WorksDetailExample();
        worksDetailExample.createCriteria().andVideoClipIdEqualTo(clipId);
        List<WorksDetail> worksDetails = worksDetailMapper.selectByExample(worksDetailExample);
        List<Long> worksId = worksDetails.stream().map(WorksDetail::getWorksId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(worksId)) {
            return;
        }
        WorksExample worksExample = new WorksExample();
        worksExample.createCriteria().andIdIn(worksId);
        worksMapper.deleteByExample(worksExample);
        WorksDetailExample deleteExample = new WorksDetailExample();
        deleteExample.createCriteria().andWorksIdIn(worksId);
        worksDetailMapper.deleteByExample(deleteExample);
    }

    public void deleteWorksByClipIds(List<Long> clipIds) {
        WorksDetailExample worksDetailExample = new WorksDetailExample();
        worksDetailExample.createCriteria().andVideoClipIdIn(clipIds);
        List<WorksDetail> worksDetails = worksDetailMapper.selectByExample(worksDetailExample);
        List<Long> worksIds = worksDetails.stream().map(WorksDetail::getWorksId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(worksIds)) {
            return;
        }
        WorksExample worksExample = new WorksExample();
        worksExample.createCriteria().andIdIn(worksIds);
        worksMapper.deleteByExample(worksExample);
        WorksDetailExample deleteExample = new WorksDetailExample();
        deleteExample.createCriteria().andWorksIdIn(worksIds);
        worksDetailMapper.deleteByExample(deleteExample);
    }


    public ResponseEntity<Resource> downloadWithToken(String token, Integer type, Long worksId) {
        if(!tokenService.justify(token)) {
            throw new RuntimeException("token已经失效");
        }
        if(DownloadTypeEnum.VIDEO.getCode().equals(type)) {
            return download(worksId);
        }else if(DownloadTypeEnum.COVER.getCode().equals(type)) {
            return downloadCover(worksId);
        }else if(DownloadTypeEnum.VIDEOWITHOUTASS.getCode().equals(type)) {
            return downloadWithoutAss(worksId);
        }
        return null;
    }
}
