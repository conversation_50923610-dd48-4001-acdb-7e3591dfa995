package com.fastclip.service.workbench;

import com.fastclip.common.model.dto.MusicDTO;
import com.fastclip.common.model.request.InsertMusicReq;
import com.fastclip.dao.mapper.MusicMapper;
import com.fastclip.dao.model.dataobject.Music;
import com.fastclip.dao.model.dataobject.MusicExample;
import com.fastclip.dao.utils.MusicUtils;
import org.apache.ibatis.session.RowBounds;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.List;

@Service
public class MusicService {

    @Autowired
    MusicMapper musicMapper;

    @Value("${music.path}")
    String musicPath;


    public List<MusicDTO> getMusics(){
        MusicExample musicExample = new MusicExample();
        RowBounds rowBounds = new RowBounds(1, 100);
        List<Music> musics = musicMapper.selectByExampleWithRowbounds(musicExample, rowBounds);
        return MusicUtils.do2DTOs(musics);
    }

    public Boolean insertMusics(InsertMusicReq req) {
        File file = new File(req.getPath());
        int i=0;
        for(File subFile: file.listFiles()) {
            String path = subFile.getAbsolutePath();
            if(path.contains("ogg") || path.contains("flac")) {
                i++;
                try {
                    FFmpegFrameGrabber fFmpegFrameGrabber = new FFmpegFrameGrabber(path);
                    fFmpegFrameGrabber.start();
                    long duration = fFmpegFrameGrabber.getLengthInTime();
                    if(duration <= 0) {
                        continue;
                    }
                    String name = subFile.getName();
                    Music music = new Music();
                    music.setDuration((int)(duration/1000));
                    music.setName(name);
                    music.setCreateTime(new Date());
                    music.setUpdateTime(new Date());
                    musicMapper.insert(music);
                    String filePath = musicPath + "/" + music.getId()+ ".mp3";
                    FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(filePath, 0, 0, 1);
                    recorder.setFormat("mp3");
                    recorder.setSampleRate(48000);
                    recorder.start();
                    Frame frame = fFmpegFrameGrabber.grabFrame();
                    while(frame != null) {
                        recorder.record(frame);
                        frame = fFmpegFrameGrabber.grabFrame();
                    }
                    fFmpegFrameGrabber.release();
                    fFmpegFrameGrabber.close();
                    recorder.release();
                    recorder.stop();
                    music.setPath(filePath);
                    musicMapper.updateByPrimaryKey(music);
                }catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
            }
        }
        return true;
    }
}
