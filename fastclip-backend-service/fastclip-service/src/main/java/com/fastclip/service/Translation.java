package com.fastclip.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class Translation {
    private final String url = "http://api.fanyi.baidu.com/api/trans/vip/translate";
    private String appId = "20240811002121082";
    private String appKey = "26JpIJFuamjBGfkg3fWa";

    public void translateSrtFile(String inputPath,String outputPath, String targetLang) {
        try {
            FileReader fileIn = new FileReader(inputPath);
            FileWriter fileOut =  new FileWriter(outputPath);
            BufferedReader bufferedReader = new BufferedReader(fileIn);
            StringBuffer stringBuffer = new StringBuffer();
            StringBuffer stringBufferToTrans = new StringBuffer();
            BufferedWriter bufferedWriter = new BufferedWriter(fileOut);
            Map<Integer, String> lineMap = new HashMap<>();
            Map<Integer, Integer> transNumToLineMap = new HashMap<>();
            String line = null;
            int i = 0;
            int num = 0;
            while((line = bufferedReader.readLine()) != null) {
                i++;
                lineMap.put(i, line);
                if(!StringUtils.isEmpty(line) && containsChinese(line)) {
                    num++;
                    stringBuffer.append(line +"。");
                    transNumToLineMap.put(num, i);
                }
            }
            String trans = translate(stringBuffer.toString(), targetLang);
            String[] transStrs = trans.split("\\.");
            for(int t=0; t<transStrs.length; t++) {
                lineMap.put(transNumToLineMap.get(t + 1), transStrs[t]);
            }

            for(String lineStr: lineMap.values()) {
                bufferedWriter.write(lineStr);
                bufferedWriter.newLine();
            }

            bufferedReader.close();
            bufferedWriter.close();
        }catch (Exception e ) {
            log.error("translate srt error", e);
        }
    }


    public String translate(String text, String targetLang) {
        try{
//            String salt = String.valueOf((int)(100000 * Math.random()));
            String salt = "12345678";
            String q = URLEncoder.encode(text, "UTF-8");
            String fullUrl = url + "?q=" + q + "&from=auto&to=" + targetLang + "&appid=" + appId + "&salt=" +  salt +
                    "&sign=" + generateSign(text, salt);
            URL apiUrl = new URL(fullUrl);
            HttpURLConnection connection = (HttpURLConnection) apiUrl.openConnection();
            connection.setRequestMethod("GET");

            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            while((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            JSONObject jsonResponse = (JSONObject)JSON.parse(response.toString());
            JSONArray transResult = jsonResponse.getJSONArray("trans_result");
            if(transResult == null) {
                return "";
            }
            JSONObject result = transResult.getJSONObject(0);
            String translatedText = result.getString("dst");
            return translatedText;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private String generateSign(String q, String salt) throws Exception{
        String text  = appId + q + salt + appKey;
        MessageDigest messageDigest = null;

        try {
            messageDigest = MessageDigest.getInstance("MD5");

            messageDigest.reset();

            messageDigest.update(text.getBytes("UTF-8"));
        } catch (NoSuchAlgorithmException e) {
            System.out.println("NoSuchAlgorithmException caught!");
            System.exit(-1);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        byte[] byteArray = messageDigest.digest();

        StringBuffer md5StrBuff = new StringBuffer();

        for (int i = 0; i < byteArray.length; i++) {
            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1)
                md5StrBuff.append("0").append(Integer.toHexString(0xFF & byteArray[i]));
            else
                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
        }
        return md5StrBuff.toString();
    }

    public boolean containsChinese(String str) {
        Pattern pattern = Pattern.compile("[\u4E00-\u9FA5]");
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    public static void main(String[] args) {
        new Translation().translateSrtFile("/Volumes/共享文件夹/douyin/haowufenxiang/7128241092511730980.wav.srt",
                "/Volumes/共享文件夹/douyin/haowufenxiang/7128241092511730980.kor.srt", "kor");
    }
}
