//package com.fastclip.service.chanxuan;
//
//import com.alibaba.druid.util.StringUtils;
//import com.fastclip.common.model.dataobject.VideoClipDO;
//import com.fastclip.common.utils.FileUtils;
//import com.fastclip.dao.mapper.ChanxuanMapper;
//import com.fastclip.service.FfmpegService;
//import com.fastclip.service.WhisperService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Service
//@Slf4j
//public class ChanxuanGetAudioSrtService {
//
//    @Autowired
//    ChanxuanMapper chanxuanMapper;
//
//    @Autowired
//    FfmpegService ffmpegService;
//
//    @Autowired
//    WhisperService whisperService;
//
//    public void fetchAudioSrt(){
//        while(true) {
//            List<VideoClipDO> clipDOs = chanxuanMapper.getClipWithoutSrt(10);
//            autoCutByClipDos(clipDOs);
//        }
//    }
//
//    public void fetchAudioSrtByProductIds(List<String> productIds) {
//        for (String productId : productIds) {
//            List<VideoClipDO> clipDOs = chanxuanMapper.getClipWithoutSrtByProductId(productId);
//            autoCutByClipDos(clipDOs);
//        }
//    }
//
//    private void autoCutByClipDos(List<VideoClipDO> clipDOS){
//        if(CollectionUtils.isEmpty(clipDOS)) {
//            return;
//        }
//        for(VideoClipDO clipDO: clipDOS) {
//            try {
//                String localVideoPath = clipDO.getLocalVideoPath();
//                String filePath = localVideoPath.substring(0, localVideoPath.length() - 4);
//                String audioPath = filePath + ".wav";
//                String srtPath = audioPath + ".srt";
//
//                List<VideoClipDO> tmpDOs = new ArrayList<>();
//                tmpDOs.add(clipDO);
//                if (StringUtils.isEmpty(clipDO.getLocalAudioPath())) {
//                    ffmpegService.acodec(localVideoPath, audioPath);
//                    clipDO.setLocalAudioPath(audioPath);
//                    if(!FileUtils.exist(audioPath)){
//                        continue;
//                    }
//                    chanxuanMapper.insertOrUpdateCxClip(tmpDOs);
//                }
//                if(StringUtils.isEmpty(clipDO.getLocalSrtPath())) {
//                    whisperService.fetchSrt(clipDO.getLocalAudioPath());
//                    clipDO.setLocalSrtPath(srtPath);
//                    if(!FileUtils.exist(srtPath)){
//                        continue;
//                    }
//                    chanxuanMapper.insertOrUpdateCxClip(tmpDOs);
//                }
//            } catch (Exception e) {
//                log.error("process error", e);
//            }
//        }
//    }
//
//
//}
