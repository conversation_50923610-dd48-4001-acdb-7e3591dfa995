package com.fastclip.service.schedule;

import com.alibaba.fastjson.JSON;
import com.fastclip.common.model.dto.DouyinAccountDTO;
import com.fastclip.common.model.dto.DouyinQRCheckResult;
import com.fastclip.common.model.dto.DouyinUserInfo;
import com.fastclip.common.model.dto.DouyinUserToken;
import com.fastclip.dao.mapper.DouyinAccountMapper;
import com.fastclip.dao.mapper.ItemMapper;
import com.fastclip.dao.mapper.QRCodeMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.service.douyin.AccountService;
import com.fastclip.service.douyin.OrderService;
import com.fastclip.service.http.DouyinHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class QRCodeTask {

    @Autowired
    QRCodeMapper qrCodeMapper;

    @Autowired
    DouyinHttpUtils douyinHttpUtils;

    @Autowired
    DouyinAccountMapper douyinAccountMapper;

    @Autowired
    AccountService accountService;

    @Autowired
    OrderService orderService;

    @Value("${schedule.qr_code_enabled}")
    Boolean scheduleEnabled;

    @Scheduled(fixedDelay = 1000)
    public void checkQRCode() {
        try {
            if (!scheduleEnabled) {
                return;
            }
            QRCodeExample example = new QRCodeExample();
            Long curtimestamp = System.currentTimeMillis();
            Long newTimestamp = curtimestamp - 120000;
            Date date = new Date(newTimestamp);
            example.createCriteria().andStatusEqualTo(0)
                    .andCreateTimeGreaterThan(date);
            List<QRCode> qrCodes = qrCodeMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(qrCodes)) {
                log.info("get 0  qrcodes!");
                return;
            }else{
                log.info("get {}  qrcodes!", qrCodes.size());
            }
            for(QRCode qrCode: qrCodes) {
                String jsonStr = douyinHttpUtils.checkQRCode(qrCode.getToken());
                DouyinQRCheckResult douyinQRCheckResult = JSON.parseObject(jsonStr, DouyinQRCheckResult.class);
                DouyinUserInfo douyinUserInfo = douyinQRCheckResult.getUser_info();
                if(douyinUserInfo != null) {
                    qrCode.setStatus(1);
                    qrCodeMapper.updateByPrimaryKeySelective(qrCode);
                    DouyinAccount douyinAccount = douyinAccountMapper.selectByPrimaryKey(qrCode.getDouyinAccountId());
                    if (douyinAccount != null) {
                        updateAccount(douyinAccount, douyinQRCheckResult);
                        orderService.syncByAccount(douyinAccount);
                    }
                }
            }
        }catch (Exception e) {
            log.error("insert pic error", e);
        }
    }

    private void updateAccount(DouyinAccount douyinAccount, DouyinQRCheckResult douyinQRCheckResult) {
        DouyinUserInfo douyinUserInfo = douyinQRCheckResult.getUser_info();
        DouyinUserToken douyinUserToken = douyinQRCheckResult.getUser_token();
        douyinAccount.setAccessToken(douyinUserToken.getAccess_token());
        douyinAccount.setExpiresIn(new Date(System.currentTimeMillis() + douyinUserToken.getExpires_in() * 1000));
        douyinAccount.setRefreshToken(douyinUserToken.getRefresh_token());
        douyinAccount.setRefreshExpiresIn(new Date(System.currentTimeMillis() + douyinUserToken.getRefresh_expires_in() * 1000));
        douyinAccount.setDouyinName(douyinUserInfo.getNickname());
        douyinAccount.setOpenId(douyinUserInfo.getOpen_id());
        douyinAccount.setInviteDone(true);
        douyinAccountMapper.updateByPrimaryKey(douyinAccount);
    }

}
