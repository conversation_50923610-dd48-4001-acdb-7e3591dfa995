package com.fastclip.service.schedule;

import com.fastclip.common.constant.ComposeStatusEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.UserUtils;
import com.fastclip.dao.utils.WorksUtils;
import com.fastclip.llm.ask.QwenAsk;
import com.fastclip.service.baseCover.BaseCoverService;
import com.fastclip.service.works.WorksService;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@Slf4j
public class ItemPicInsertTask {

    @Autowired
    ItemMapper itemMapper;

    @Value("${schedule.item_pic_insert_enabled}")
    Boolean scheduleEnabled;


    @Scheduled(fixedDelay = 500)
    public void insertPic() {
        try {
            if (!scheduleEnabled) {
                return;
            }
            ItemExample example = new ItemExample();
            example.createCriteria().andIsPicDownloadedEqualTo(false);
            example.setOrderByClause("create_time");
            RowBounds rowBounds = new RowBounds(0, 1);
            List<Item> items = itemMapper.selectByExampleWithRowbounds(example, rowBounds);
            if (CollectionUtils.isEmpty(items)) {
                return;
            }
            Item item = items.get(0);
            log.info("start to insert pic, itemId={}", item.getOutItemId());
            //获取图片
            //....
            //插入图片
            //....
            item.setIsPicDownloaded(true);
            itemMapper.updateByPrimaryKeySelective(item);
            log.info("finished insert pic, itemId={}", item.getOutItemId());
        }catch (Exception e) {
            log.error("insert pic error", e);
        }
    }

}
