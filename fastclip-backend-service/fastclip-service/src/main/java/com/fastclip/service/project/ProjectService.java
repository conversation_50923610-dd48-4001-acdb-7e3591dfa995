package com.fastclip.service.project;

import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.ProjectDTO;
import com.fastclip.common.model.dto.SellerDTO;
import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.model.dto.VideoMaterialSliceDTO;
import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.GetCoverRes;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.dao.mapper.ItemOnLiveMapper;
import com.fastclip.dao.mapper.ProjectMapper;
import com.fastclip.dao.mapper.ProjectMapperExt;
import com.fastclip.dao.mapper.SubtitlesCutMapper;
import com.fastclip.dao.mapper.SubtitlesMapper;
import com.fastclip.dao.mapper.VideoClipDetailsMapper;
import com.fastclip.dao.mapper.VideoClipMapper;
import com.fastclip.dao.model.dataobject.ItemOnLive;
import com.fastclip.dao.model.dataobject.Project;
import com.fastclip.dao.model.dataobject.ProjectExt;
import com.fastclip.dao.model.dataobject.Subtitles;
import com.fastclip.dao.model.dataobject.SubtitlesCut;
import com.fastclip.dao.model.dataobject.VideoClip;
import com.fastclip.dao.model.dataobject.VideoClipDetails;
import com.fastclip.dao.utils.ProjectUtils;
import com.fastclip.opencv.FFmpegService;
import com.fastclip.opencv.grabber.LiveVideoSubtitlesGrabber;
import com.fastclip.opencv.utils.FrameUtils;
import com.fastclip.service.item.ItemService;
import com.fastclip.service.seller.SellerService;
import com.fastclip.service.video.VideoClipService;
import com.fastclip.service.video.VideoMaterialService;
import com.fastclip.service.video.VideoMaterialSliceService;
import com.fastclip.service.video.VideoPlayService;
import com.fastclip.service.works.WorksService;
import com.github.benmanes.caffeine.cache.LoadingCache;

import java.util.*;
import java.util.stream.Collectors;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Rect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
public class ProjectService {
    @Autowired
    ProjectMapper projectMapper;

    @Autowired
    ProjectMapperExt projectMapperExt;

    @Autowired
    ItemService itemService;

    @Autowired
    SellerService sellerService;

    @Autowired
    SubtitlesCutMapper subtitlesCutMapper;

    @Autowired
    SubtitlesMapper subtitlesMapper;

    @Autowired
    VideoClipMapper videoClipMapper;

    @Autowired
    VideoClipDetailsMapper videoClipDetailsMapper;

    @Autowired
    VideoClipService videoClipService;

    @Autowired
    WorksService worksService;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    VideoMaterialSliceService videoMaterialSliceService;

    @Value("${ffmpeg.coverBasePath}")
    String coverBasePath;

    @Autowired
    VideoPlayService videoPlayService;

    @Autowired
    FFmpegService fFmpegService;

    @Autowired
    ItemOnLiveMapper itemOnLiveMapper;

    @Autowired
    LoadingCache<Long, UserDTO> userCache;

    public PagebleRes<ProjectDTO> getProjectList(ProjectReq req) {
        UserDTO userDTO = SsoUserContext.getUser();
        req.setCreatorId(userDTO.getId());
        req.setIsAdmin(userDTO.getIsAdmin());
        PagebleRes<ProjectDTO> pagebleRes = new PagebleRes();
        if (req.getPageNum() == null)
            req.setPageNum(1);
        if (req.getPageSize() == null)
            req.setPageSize(15);
        req.setOffset((req.getPageNum() - 1) * req.getPageSize());
        List<ProjectExt> projects = this.projectMapperExt.getProjects(req);
        Integer count = this.projectMapperExt.countProjects(req);
        List<ProjectDTO> projectDTOS = ProjectUtils.extDo2DTOs(projects);
        getProjectExtInfos(projectDTOS);
        pagebleRes.setData(projectDTOS);
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal(count);
        return pagebleRes;
    }

    private void setLiveItemInfo(List<ProjectDTO> projectDTOS) {
        for (ProjectDTO projectDTO : projectDTOS) {
            if (ItemTypeEnum.Live.getValue().equals(projectDTO.getItemType())) {
                ItemOnLive item = this.itemOnLiveMapper.selectByPrimaryKey(projectDTO.getItemId());
                projectDTO.setItemName(item.getItemName());
            }
        }
    }

    public ProjectDTO getProject(Long projectId) {
        Project project = this.projectMapper.selectByPrimaryKey(projectId);
        return ProjectUtils.do2DTO(project);
    }

    public void setProjectWorksCount(List<ProjectDTO> projectDTOS) {
        List<Long> projectIds = (List<Long>)projectDTOS.stream().map(ProjectDTO::getId).collect(Collectors.toList());
        GetWorksReq req = new GetWorksReq();
        req.setProjectIds(projectIds);
        List<WorksDTO> worksDTOS = this.worksService.getWorks(req);
        Map<Long, Integer> countOfProjectMap = new HashMap<>();
        for (WorksDTO worksDTO : worksDTOS) {
            Integer count = countOfProjectMap.get(worksDTO.getProjectId());
            if (count == null) {
                count = Integer.valueOf(1);
            } else {
                Integer integer1 = count, integer2 = count = Integer.valueOf(count.intValue() + 1);
            }
            countOfProjectMap.put(worksDTO.getProjectId(), count);
        }
        for (ProjectDTO projectDTO : projectDTOS)
            projectDTO.setWorksCount(countOfProjectMap.get(projectDTO.getId()));
    }

    @Transactional
    public Boolean addSubtitlesClipsToProject(AddSubtitlesReq req) {
        List<Long> videoClipIds = new ArrayList<>();
        if(req.getSubtitlesId() != null) {
            return addSubtitleToProject(req.getSubtitlesId(), req.getProjectId()) > 0;
        }
        if(CollectionUtils.isEmpty(req.getSubtitlesIds())) {
            return true;
        }
        for(Long subtitlesId: req.getSubtitlesIds()) {
            videoClipIds.add(addSubtitleToProject(subtitlesId, req.getProjectId()));
        }
        MergeVideoClipReq mergeVideoClipReq = new MergeVideoClipReq();
        mergeVideoClipReq.setVideoClipIds(videoClipIds);
        mergeVideoClipReq.setProjectId(req.getProjectId());
        videoClipService.mergeVideoClips(mergeVideoClipReq);
        return true;
    }

    private Long addSubtitleToProject(Long subtitlesId, Long projectId) {
        Subtitles subtitles = this.subtitlesMapper.selectByPrimaryKey(subtitlesId);
        SubtitlesCut subtitlesCut = new SubtitlesCut();
        subtitlesCut.setSubtitlesId(subtitlesId);
        subtitlesCut.setVideoId(subtitles.getVideoId());
        subtitlesCut.setProjectId(projectId);
        subtitlesCut.setContent(subtitles.getContent());
        subtitlesCut.setCutStartTs(subtitles.getStartTs());
        subtitlesCut.setCutEndTs(subtitles.getEndTs());
        subtitlesCut.setDuration(subtitles.getDuration());
        subtitlesCut.setLength(subtitles.getLength());
        subtitlesCut.setCreateTime(new Date());
        subtitlesCut.setUpdateTime(new Date());
        this.subtitlesCutMapper.insert(subtitlesCut);
        VideoClip videoClip = new VideoClip();
        videoClip.setProjectId(projectId);
        videoClip.setCreateTime(new Date());
        videoClip.setUpdateTime(new Date());
        videoClip.setSubtitles(subtitles.getContent());
        videoClip.setStartSubtitlesId(subtitlesCut.getSubtitlesId());
        videoClip.setEndSubtitlesId(subtitlesCut.getSubtitlesId());
        videoClip.setStartSubtitlesCutId(subtitlesCut.getId());
        videoClip.setEndSubtitlesCutId(subtitlesCut.getId());
        videoClip.setDuration(subtitles.getDuration());
        videoClip.setSort(this.videoClipService.getNextMaxSortValue(projectId));
        videoClip.setSubtitlesCutCount(1);
        this.videoClipMapper.insert(videoClip);
        VideoClipDetails videoClipDetails = new VideoClipDetails();
        videoClipDetails.setVideoClipId(videoClip.getId());
        videoClipDetails.setSubtitlesCutId(subtitlesCut.getId());
        videoClipDetails.setProjectId(projectId);
        videoClipDetails.setUpdateTime(new Date());
        videoClipDetails.setCreateTime(new Date());
        videoClipDetails.setSort(1);
        this.videoClipDetailsMapper.insert(videoClipDetails);
        return videoClip.getId();
    }

    public Boolean createProject(ProjectDTO projectDTO) {
        UserDTO userDTO = SsoUserContext.getUser();
        Project project = ProjectUtils.dto2DO(projectDTO);
        project.setStatus(Integer.valueOf(0));
        project.setCreateTime(new Date());
        project.setUpdateTime(new Date());
        project.setCreatorId(userDTO.getId());
        int i = this.projectMapper.insert(project);
        if (i == 1)
            return true;
        return false;
    }

    public Boolean deleteProject(Long id) {
        if(id == null) {
            return false;
        }
        int i = this.projectMapper.deleteByPrimaryKey(id);
        if (i >= 1)
            return true;
        return false;
    }

    private void getProjectExtInfos(List<ProjectDTO> projectDTOS) {
        List<Long> sellerIds = (List<Long>)projectDTOS.stream().map(ProjectDTO::getSellerId).collect(Collectors.toList());
        SellerReq sellerReq = new SellerReq();
        sellerReq.setSellerIds(sellerIds);
        PagebleRes<SellerDTO> sellersRes = this.sellerService.getSellerList(sellerReq);
        Map<Long, String> sellerMap = (Map<Long, String>)sellersRes.getData().stream().collect(Collectors.toMap(SellerDTO::getSellerId, SellerDTO::getSellerName));
        for (ProjectDTO projectDTO : projectDTOS) {
            projectDTO.setSellerName(sellerMap.get(projectDTO.getSellerId()));
            projectDTO.setCreator((UserDTO)this.userCache.get(projectDTO.getCreatorId()));
        }
    }

    public Boolean setCover(SetCoverReq req) {
        GetCoverReq getReq = new GetCoverReq();
        getReq.setHeight(req.getHeight());
        getReq.setStartX(req.getStartX());
        getReq.setStartY(req.getStartY());
        getReq.setWidth(req.getWidth());
        getReq.setTimestamp(req.getTimestamp());
        getReq.setVideoId(req.getVideoId());
        GetCoverRes res = getCover(getReq);
        String coverPath = FrameUtils.convertBase64ToFile(res.getData(), this.coverBasePath, req.getProjectId() + ".jpg");
        Project project = new Project();
        project.setId(req.getProjectId());
        project.setCover(coverPath);
        this.projectMapper.updateByPrimaryKeySelective(project);
        return Boolean.valueOf(true);
    }

    public GetCoverRes getCover(GetCoverReq req) {
        VideoMaterialDTO videoMaterialDTO = this.videoMaterialService.getVideoMaterialById(req.getVideoId());
        List<VideoMaterialSliceDTO> videoMaterialSliceDTOS = this.videoMaterialSliceService.getVideoMaterialSlicesByVideoId(videoMaterialDTO.getId());
        videoMaterialDTO.setVideoMaterialSliceDTOS(videoMaterialSliceDTOS);
        Frame frame = null;
        if (VideoMaterialTypeEnum.LIVE.getValue().equals(videoMaterialDTO.getVideoType()) && VideoMaterialCombineStatusEnum.PROCESSING
                .getValue().equals(videoMaterialDTO.getStatus())) {
            frame = getCoverFrameFromLiveVideo(req, videoMaterialDTO);
        } else {
            frame = getCoverFrameFromVideo(req, videoMaterialDTO);
        }
        GetCoverRes res = new GetCoverRes();
        OpenCVFrameConverter.ToMat frameConverter = new OpenCVFrameConverter.ToMat();
        Mat img = frameConverter.convert(frame);
        int width = frame.imageWidth;
        int height = frame.imageHeight;
        int cropWidth = (int)(width * req.getWidth());
        int cropHeight = (int)(height * req.getHeight());
        int x = (int)(width * req.getStartX());
        int y = (int)(height * req.getStartY());
        Mat cropMat = new Mat(img, new Rect(x, y, cropWidth, cropHeight));
        Frame newFrame = frameConverter.convert(cropMat);
        res.setData(this.videoPlayService.encodeImageFrame(newFrame));
        res.setWidth(cropWidth);
        res.setHeight(cropHeight);
        return res;
    }

    private Frame getCoverFrameFromLiveVideo(GetCoverReq req, VideoMaterialDTO videoMaterialDTO) {
        List<VideoMaterialSliceDTO> sliceDTOS = videoMaterialDTO.getVideoMaterialSliceDTOS();
        VideoMaterialSliceDTO videoMaterialSliceDTO = null;
        for (int i = 0; i < sliceDTOS.size() - 1; i++) {
            if (sliceDTOS.get(i).getStartTs() * 1000000L + videoMaterialDTO.getStartTime() * 1000L < req.getTimestamp() && ((VideoMaterialSliceDTO) sliceDTOS
                    .get(i + 1)).getStartTs() * 1000000L + videoMaterialDTO.getStartTime() * 1000L > req.getTimestamp())
                videoMaterialSliceDTO = sliceDTOS.get(i);
        }
        if (videoMaterialSliceDTO == null)
            return null;
        Long coverTimestamp = req.getTimestamp() - videoMaterialSliceDTO.getStartTs() * 1000000L - videoMaterialDTO.getStartTime() * 1000L;
        LiveVideoSubtitlesGrabber grabber = new LiveVideoSubtitlesGrabber();
        grabber.setVideoOrAudio(VideoOrAudioTypeEnum.VIDEO.getCode());
        grabber.setVideoMaterialDTO(videoMaterialDTO);
        grabber.setVideoMaterialSlice(videoMaterialSliceDTO);
        grabber.setVideoTimestamp(coverTimestamp);
        Frame frame = grabber.grabImage();
        grabber.close();
        return frame;
    }

    private Frame getCoverFrameFromVideo(GetCoverReq req, VideoMaterialDTO videoMaterialDTO) {
        try {
            Long coverTimestamp = req.getTimestamp() - videoMaterialDTO.getStartTime() * 1000L;
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoMaterialDTO.getPath());
            grabber.start();
            grabber.setTimestamp(coverTimestamp);
            int i = 0;
            Frame frame = grabber.grabImage();
            while (frame.timestamp > coverTimestamp) {
                i++;
                long timestamp = coverTimestamp - i * 1000000L;
                if(timestamp < 0){
                    break;
                }
                grabber.setTimestamp(timestamp);
                frame = grabber.grabImage();
            }
            while (frame.timestamp < coverTimestamp)
                frame = grabber.grabImage();
            return frame;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
