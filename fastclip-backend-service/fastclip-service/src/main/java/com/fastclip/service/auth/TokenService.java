package com.fastclip.service.auth;

import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.dao.mapper.TokenMapper;
import com.fastclip.dao.model.dataobject.Token;
import com.fastclip.dao.model.dataobject.TokenExample;
import com.fastclip.service.user.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Service
public class TokenService {

    @Value("${login.expireTime}")
    long loginExpireTime;

    @Autowired
    TokenMapper tokenMapper;

    @Autowired
    UserService userService;

    public boolean justify(String token) {
        if (token == null) {
            return false;
        }
        Date date = new Date();
        TokenExample example = new TokenExample();
        example.createCriteria().andTokenEqualTo(token);
        List<Token> tokenList = tokenMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(tokenList)) {
            return false;
        }
        Token tokenDO = tokenList.get(0);
        if (date.getTime() - tokenDO.getUpdateTime().getTime() >= loginExpireTime * 1000000L) {
            return false;
        }
        tokenDO.setUpdateTime(new Date());
        tokenMapper.updateByPrimaryKey(tokenDO);
        UserDTO userDTO = userService.getUserById(tokenDO.getUserId());
        userDTO.setToken(token);
        SsoUserContext.setUser(userDTO);
        return true;
    }

    public void insertToken(String token, UserDTO userDTO) {
        Token tokenDO = new Token();
        tokenDO.setToken(token);
        tokenDO.setUserId(userDTO.getId());
        tokenDO.setCreateTime(new Date());
        tokenDO.setUpdateTime(new Date());
        tokenMapper.insert(tokenDO);
    }

    public void deleteToken(UserDTO userDTO) {
        TokenExample tokenExample = new TokenExample();
        tokenExample.createCriteria().andUserIdEqualTo(userDTO.getId());
        tokenMapper.deleteByExample(tokenExample);
    }
}
