package com.fastclip.service.douyin;

import ch.qos.logback.classic.Level;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.SSLHandshakeException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
public class HttpClientUtil {

    private static final PoolingHttpClientConnectionManager connectionManager;
    private static final RequestConfig requestConfig;
    private static final HttpRequestRetryHandler retryHandler;

    static {
        /*
         * 禁止apache http的debug日志
         * */
        Set<String> loggers = new HashSet<>(Arrays.asList("org.apache.http"));
//        for (String log : loggers) {
//            ch.qos.logback.classic.Logger logger = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger(log);
//            logger.setLevel(Level.INFO);
//            logger.setAdditive(false);
//        }

        // 配置连接池
        connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200); // 连接池最大连接数
        connectionManager.setDefaultMaxPerRoute(20); // 每个路由的默认最大连接数
        // 配置请求参数
        requestConfig = RequestConfig.custom()
                .setConnectTimeout(2000) // 连接超时时间
                .setSocketTimeout(10000) // 读取超时时间
                .setConnectionRequestTimeout(20000) // 从连接池获取连接的超时时间
                .build();

        // 创建重试处理器
        retryHandler = new HttpRequestRetryHandler() {
            private static final int MAX_RETRIES = 3;
            private int socketTimeoutRetryCount = 0;

            @Override
            public boolean retryRequest(IOException exception, int executionCount, HttpContext context) {
                if (executionCount > MAX_RETRIES) {
                    return false;
                }
                if (exception instanceof NoHttpResponseException) {
                    return true;
                }
                if (exception instanceof SSLHandshakeException || exception instanceof UnknownHostException) {
                    return false;
                }
                if (exception instanceof ConnectTimeoutException) {
                    return true;
                }
                if (exception instanceof SocketTimeoutException) {
                    if (socketTimeoutRetryCount < MAX_RETRIES) {
                        socketTimeoutRetryCount++;
                        return true;
                    } else {
                        return false;
                    }
                }
                return !(exception instanceof InterruptedIOException);
            }
        };
    }

    private HttpClientUtil() {
    }

    private static CloseableHttpClient getHttpClient() {
        return HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setRetryHandler(retryHandler)
                .build();
    }


    public static String doGet(String url, Map<String, String> headers) throws IOException {
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(new HttpGet(url), headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doGetCookie(String url, Map<String, String> headerMap) throws IOException {
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(new HttpGet(url), headerMap, httpClient)) {
            Header[] respHeaders = response.getHeaders("Set-Cookie");
            List<String> cookiesList = Arrays.stream(respHeaders).map(Header::getValue).collect(Collectors.toList());
            String result = handleResponse(response);
            return String.join("; ", cookiesList);
        }
    }

    public static String doGet(String url, Map<String, String> headers, boolean redirect) throws IOException {
        CloseableHttpClient httpClient = getHttpClient();
        HttpGet request = new HttpGet(url);

        RequestConfig requestConfig = RequestConfig.custom()
                .setRedirectsEnabled(redirect)
                .build();
        request.setConfig(requestConfig);

        try (CloseableHttpResponse response = executeRequest(request, headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doGet(String url, Map<String, String> parameters, Map<String, String> headers) throws Exception {
        CloseableHttpClient httpClient = getHttpClient();

        URIBuilder uriBuilder = new URIBuilder(url);
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            uriBuilder.addParameter(entry.getKey(), entry.getValue());
        }
        URI uri = uriBuilder.build();

        try (CloseableHttpResponse response = executeRequest(new HttpGet(uri), headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doPost(String url, RequestBody formBody, String cookie, String referer) throws IOException {
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(url)
                    .header("Cookie", cookie)
                    .header("referer", referer)
                    .head()
                    .post(formBody)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                return response.body().string();
            }

    }

    public static String doPost(String url, byte[] data, Map<String, String> headers) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        HttpEntity entity = new ByteArrayEntity(data);
        httpPost.setEntity(entity);
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(httpPost, headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doPost(String url, Map<String, String> parameters, byte[] data, Map<String, String> headers) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(url);
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            uriBuilder.addParameter(entry.getKey(), entry.getValue());
        }
        URI uri = uriBuilder.build();

        HttpPost httpPost = new HttpPost(uri);
        HttpEntity entity = new ByteArrayEntity(data);
        httpPost.setEntity(entity);
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(httpPost, headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doPost(String url, Map<String, String> parameters, String plainTextData, Map<String, String> headers) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(url);
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            uriBuilder.addParameter(entry.getKey(), entry.getValue());
        }
        URI uri = uriBuilder.build();

        HttpPost httpPost = new HttpPost(uri);
        StringEntity entity = new StringEntity(plainTextData, ContentType.TEXT_PLAIN);
        httpPost.setEntity(entity);

        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(httpPost, headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doPost(String url, Map<String, String> parameters, Map<String, String> formData, Map<String, String> headers) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(url);
        if (parameters != null && !parameters.isEmpty()) {
            for (Map.Entry<String, String> entry : parameters.entrySet()) {
                uriBuilder.addParameter(entry.getKey(), entry.getValue());
            }
        }
        URI uri = uriBuilder.build();

        HttpPost httpPost = new HttpPost(uri);
        List<BasicNameValuePair> paramList = formData.entrySet().stream().map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue())).collect(Collectors.toList());
        httpPost.setEntity(new UrlEncodedFormEntity(paramList, "UTF-8"));

        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(httpPost, headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doPost(String url, String json, Map<String, String> headers) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(httpPost, headers, httpClient)) {
            String result = handleResponse(response);
            return result;
        }
    }

    public static String doPost(String url, List<BasicNameValuePair> formParams, Map<String, String> headers) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new UrlEncodedFormEntity(formParams, StandardCharsets.UTF_8));
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(httpPost, headers, httpClient)) {
            return handleResponse(response);
        }
    }

    /**
     * 文件下载
     *
     * @param url        下载地址
     * @param targetPath 保存路径
     * @param headers    请求头
     */
    public static boolean downLoad(String url, String targetPath, Map<String, String> headers) throws IOException {
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(new HttpGet(url), headers, httpClient)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                byte[] content = EntityUtils.toByteArray(entity);
                writeToFile(content, targetPath);
            }
            return true;
        }
    }

    /**
     * 上传文件
     *
     * @param file       文件
     * @param url        上传系统地址
     * @param formParams form表单其他类型参数参数
     */
    public static String upload(MultipartFile file, String url, Map<String, Object> formParams, Map<String, String> header) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        ContentType contentType = ContentType.getByMimeType(file.getContentType());
        if (contentType == null) {
            contentType = ContentType.create(file.getContentType());
        }
        builder.addBinaryBody("file", file.getInputStream(), contentType, file.getOriginalFilename());
        for (Map.Entry<String, Object> entry : formParams.entrySet()) {
            builder.addTextBody(entry.getKey(), String.valueOf(entry.getValue()));
        }
        HttpEntity httpEntity = builder.build();
        httpPost.setEntity(httpEntity);
        CloseableHttpClient httpClient = getHttpClient();
        try (CloseableHttpResponse response = executeRequest(httpPost, header, httpClient)) {
            String result = handleResponse(response);
            log.info("文件上传地址:{}，上传文件名：{}，上传结果：{}", url, file.getOriginalFilename(), result);
            return result;
        }
    }

    private static CloseableHttpResponse executeRequest(HttpRequestBase request, Map<String, String> headers, CloseableHttpClient httpClient) throws IOException {
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.setHeader(entry.getKey(), entry.getValue());
            }
        }
        return httpClient.execute(request);
    }

    /**
     * 将字节数组写入到文件。
     *
     * @param data           字节数组
     * @param outputFilePath 输出文件路径
     */
    private static void writeToFile(byte[] data, String outputFilePath) {
        File outputFile = new File(outputFilePath);
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(data);
        } catch (IOException e) {
            log.error("写入文件异常", e);
        }
    }

    private static String handleResponse(HttpResponse response) throws IOException {
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            return EntityUtils.toString(entity, StandardCharsets.UTF_8);
        }
        return null;
    }
}
