//package com.fastclip.service.rtmp;
//
//import com.fastclip.common.constant.RtmpConstant;
////import com.fastclip.dao.redis.RedisClient;
//import com.fastclip.dao.redis.RedisLock;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.api.RLock;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.util.HashSet;
//import java.util.Set;
//import java.util.concurrent.TimeUnit;
//
///**
// * rtmp流媒体服务连接地址管理
// */
//@Service
//@Slf4j
//public class RtmpConnManager {
//
////    @Autowired
////    RedisClient redisClient;
//
//    @Value("${rtmp.ip}")
//    private String ip;
//
//    @Value("${rtmp.port}")
//    private String port;
//
//    @Value("${rtmp.path}")
//    private String path;
//
//    @Value("${rtmp.maxConnections}")
//    private Integer maxConns;
//
//    @Value("${rtmp.fastClipUsedRtmpRoomIds}")
//    private String usedRtmpRoomIds;
//
//    @Value("${rtmp.fastClipUnusedRtmpRoomIds}")
//    private String unusedRtmpRoomIds;
//
//    @Autowired
//    RedisLock redisLock;
//
//    @PostConstruct
//    public void init() {
//        Set<Object> connSet = new HashSet();
//        for(int i=1; i<maxConns; i++) {
//            connSet.add(i);
//        }
////        redisClient.addAll(usedRtmpRoomIds, connSet);
//    }
//
//    public boolean closeConnection(RtmpConnection conn) {
////        try {
////            Integer roomId = conn.getRoomId();
////            redisClient.remove(usedRtmpRoomIds, roomId);
////            redisClient.add(usedRtmpRoomIds, roomId);
////        }catch(Exception e) {
////            return false;
////        }
//        return true;
//    }
//
//    /**
//     * 获取流媒体服务连接
//     * @return
//     */
//    public RtmpConnection getConnection() {
//        RLock lock = redisLock.getLock(RtmpConstant.conLockKey);
//        try {
////            // 尝试获取锁，最多等待10秒，锁定之后最多持有锁3秒
////            boolean isLocked = lock.tryLock(10, 3, TimeUnit.SECONDS);
////            if (isLocked) {
////                Integer roomId = (Integer)redisClient.pop(unusedRtmpRoomIds);
////                String connUrl = "http://" + ip  + ":" + port + "/" + path + "/" + roomId;
////                redisClient.add(usedRtmpRoomIds, roomId);
////                return new RtmpConnection(connUrl, roomId, this);
////            } else {
////                // 无法获得锁
////                throw new RuntimeException("get lock error");
////            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            // 释放锁
//            if (lock.isHeldByCurrentThread()) {
//                lock.unlock();
//            }
//        }
//        return null;
//    }
//}
