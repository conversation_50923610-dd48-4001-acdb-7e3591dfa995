package com.fastclip.service.video;

import com.alibaba.excel.util.FileUtils;
import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.common.model.dto.SubtitlesDTO;
import com.fastclip.common.model.dto.SubtitlesFetchResultDTO;
import com.fastclip.common.model.request.SearchSubtitlesCutReq;
import com.fastclip.common.model.request.SearchSubtitlesReq;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.SubtitlesMapper;
import com.fastclip.dao.mapper.SubtitlesMapperExt;
import com.fastclip.dao.mapper.VideoMaterialMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.SubtitlesUtils;
import com.fastclip.opencv.FFmpegService;
import com.fastclip.opencv.grabber.FrameWithSpecialEffectParams;
import com.fastclip.service.WhisperService;
import com.fastclip.service.project.ProjectService;
import com.fastclip.service.subtitles.SubtitlesService;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VideoSubtitlesFetchService {

    @Autowired
    SubtitlesMapper subtitlesMapper;

    @Autowired
    SubtitlesMapperExt subtitlesMapperExt;

    @Autowired
    VideoMaterialMapper videoMaterialMapper;

    @Autowired
    ProjectService projectService;

    @Autowired
    WhisperService whisperService;

    @Autowired
    FFmpegService fFmpegService;

    @Autowired
    SubtitlesService subtitlesService;

    @Value("${ffmpeg.audioTmpPath}")
    String audioTmpPath;

    public void fetchSubtitles(Long videoId) {
        log.info("start to fetch subtitles of videoId:" + videoId);
        VideoMaterial videoMaterial = videoMaterialMapper.selectByPrimaryKey(videoId);
        //防止更新开始时间。
        videoMaterial.setStartTime(null);
        try {

            Integer subtitlesBpTs = videoMaterial.getSubtitlesBpTs();
            Integer startTs = subtitlesBpTs;
            while(startTs!=null && startTs < videoMaterial.getDuration()) {
                log.info("start to fetch subtitles of videoId:" + videoId +" startTs:" + startTs );

                String audioFile = audioTmpPath + "/" + videoId + "_" + System.currentTimeMillis() + "_tmp.wav";
                String srtPath = audioFile  + ".srt";
                SubtitlesFetchResultDTO res = fFmpegService.getAudio(videoMaterial.getPath(), startTs, audioFile);
                if(res.getStartTs() == null || res.getStartTs().equals(res.getEndTs()) || (res.getEndTs() - res.getStartTs()) <=500) {
                    break;
                }
                whisperService.fetchSrt(audioFile, srtPath);
                insertSubtitles(videoMaterial, srtPath, res.getStartTs());
                startTs = res.getEndTs();
                videoMaterial.setUpdateTime(new Date());
                videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
                FileUtils.delete(new File(audioFile));
                FileUtils.delete(new File(srtPath));
            }
            videoMaterial.setIsSubtitlesDone(true);
            videoMaterial.setUpdateTime(new Date());
            videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
        }catch (Exception e) {
            log.error("fetch subtitles error", e);
            e.printStackTrace();
        }
    }

    public void refetchSubtitles(Long videoId) {
        log.info("start to refetch subtitles of videoId:" + videoId);
        VideoMaterial videoMaterial = videoMaterialMapper.selectByPrimaryKey(videoId);
        videoMaterial.setIsSubtitlesDone(false);
        videoMaterial.setSubtitlesBpTs(0);
        videoMaterialMapper.updateByPrimaryKey(videoMaterial);
        SubtitlesExample subtitlesExample = new SubtitlesExample();
        subtitlesExample.createCriteria().andVideoIdEqualTo(videoId);
        subtitlesMapper.deleteByExample(subtitlesExample);
    }

    /**
     * 将字幕插入数据库，返回结束时间
     * 注意：最后一行字幕不要插入，因为担心截取的不全
     * @param srtPath
     * @return
     */
    public void insertSubtitles(VideoMaterial videoMaterial, String srtPath, Integer preBpTs) {
        try {
            File file = new File(srtPath);
            FileReader reader = new FileReader(file);
            BufferedReader bufferedReader = new BufferedReader(reader);
            Integer bpTs = 0;
            List<Subtitles> list = new ArrayList<>();
            while(bufferedReader.ready()) {
                Integer startTs = null;
                Integer endTs = null;
                String content = null;
                String line = bufferedReader.readLine();
                if(line.contains("-->")) {
                    String[] timeStr = line.split("-->");
                    startTs = TimeUtils.getTimes(timeStr[0]);
                    endTs = TimeUtils.getTimes(timeStr[1]);

                    content = bufferedReader.readLine();
                }
                if(startTs == null || endTs == null || content == null) {
                    continue;
                }
                Subtitles subtitles = new Subtitles();
                subtitles.setVideoId(videoMaterial.getId());
                subtitles.setContent(content);
                subtitles.setStartTs(startTs + preBpTs);
                subtitles.setEndTs(endTs + preBpTs);
                subtitles.setDuration(endTs - startTs);
                subtitles.setLength(content.length());
                subtitles.setSellerId(videoMaterial.getSellerId());
                subtitles.setCreateTime(new Date());
                subtitles.setUpdateTime(new Date());
                list.add(subtitles);
                videoMaterial.setSubtitlesBpTs(endTs+preBpTs);
            }
            subtitlesMapperExt.batchInsert(list);
        }catch (Exception e) {
            log.error("insert subtitles error" ,e);
        }
    }
}
