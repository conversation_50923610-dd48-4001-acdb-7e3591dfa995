package com.fastclip.service.auth;

import org.apache.catalina.connector.RequestFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.annotation.WebInitParam;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@WebFilter(urlPatterns = "/*", filterName = "authFilter",
        initParams={@WebInitParam(name ="EXCLUDED_PAGES" , value = "login")})
@Component
public class AuthFilter implements Filter {

    @Autowired
    TokenService tokenService;

    @Value("${auth.ignoreList}")
    List<String> ignoreList;


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 可以在这里进行初始化操作
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 从Cookie中获取token
        if(!skip(((RequestFacade) request).getRequestURL().toString())) {
            String token = ((RequestFacade) request).getHeader("token");
            if (!tokenService.justify(token)) {
                ((HttpServletResponse) response).setStatus(HttpStatus.UNAUTHORIZED.value());

                return;
            }
            // 如果找到token，可以将其存储在请求属性中
            if (token != null) {
                httpRequest.setAttribute("token", token);
            }
        }
        // 继续过滤链
        chain.doFilter(request, response);
    }

    private Boolean skip(String url) {
        for(String ignore: ignoreList) {
            if(url.contains(ignore)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void destroy() {
        // 可以在这里进行销毁操作
    }
}