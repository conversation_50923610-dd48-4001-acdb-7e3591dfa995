package com.fastclip.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;

@Component
@Slf4j
public class ProcessService {
    public void printProcessExecResult(Process process) {
        try {
            // 读取FFmpeg进程的错误流
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            while ((line = errorReader.readLine()) != null) {
                log.info(line);
            }

            // 等待FFmpeg进程结束
            process.waitFor();

            log.info("Conversion completed successfully.");
        }catch (Exception e) {
            log.error("print process exec result error .........", e);

        }
    }
}
