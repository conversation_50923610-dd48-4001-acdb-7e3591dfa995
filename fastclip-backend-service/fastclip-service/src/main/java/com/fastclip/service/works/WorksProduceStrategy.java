package com.fastclip.service.works;

import com.fastclip.common.model.dto.ProjectDTO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.dao.model.dataobject.Works;
import org.springframework.stereotype.Service;

import java.util.List;

public interface WorksProduceStrategy {
    /**
     * 作品加工策略。
     * @param curWorks 当前已经生成的作品
     * @param videoClipDTOList 用户加工作品的所有视频片段
     * @return
     */
    public WorksDTO produce(ProjectDTO projectDTO, List<WorksDTO> curWorks, List<VideoClipDTO> videoClipDTOList);
}
