package com.fastclip.service.douyin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.model.request.CreateVideoReq;
import com.fastclip.service.video.VideoMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class LiveVideoService {

    @Value("${ffmpeg.hlsTmpPath}")
    String hlsTmpPath;

    @Value("${ffmpeg.livePath}")
    String livePath;

    @Value("${douyin.liveSliceDuration}")
    String liveSliceDuration;


    @Autowired
    VideoMaterialService videoMaterialService;

    private String getCookieValue(String cookieHeader, String cookieName) {
        String[] cookies = cookieHeader.split(";");
        for (String cookie : cookies) {
            String[] keyValue = cookie.trim().split("=");
            if (keyValue.length == 2 && keyValue[0].trim().equalsIgnoreCase(cookieName)) {
                return keyValue[1];
            }
        }
        return null;
    }

    public String getAcNonce(String userAgent, String url) throws IOException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("User-Agent", userAgent);

        String cookieHeader = HttpClientUtil.doGetCookie(url, headerMap);
        String acNonce = getCookieValue(cookieHeader, "__ac_nonce");
        return acNonce;
    }

    public long bigCountOperation(String str, long finalNum) {
        for (char ch : str.toCharArray()) {
            int charCodeCount = (int) ch;
            finalNum = ((finalNum ^ charCodeCount) * 65599) & 0xFFFFFFFFL;
        }
        return finalNum;
    }

    public String countToText(long deciNum, String acSignature) {
        int[] offList = {24, 18, 12, 6, 0};
        for (int value : offList) {
            long keyNum = (deciNum >> value) & 63;
            int valNum;
            if (keyNum < 26) {
                valNum = 65;
            } else if (keyNum < 52) {
                valNum = 71;
            } else if (keyNum < 62) {
                valNum = -4;
            } else {
                valNum = -17;
            }
            long asciiCode = keyNum + valNum;
            acSignature += (char) asciiCode;
        }
        return acSignature;
    }

    public String loadAcSignature(String url, String acNonce, String ua) {
        long finalNum = 0;
        long temp = 0;
        String acSignature = "_02B4Z6wo00f01";

        // Get the current timestamp
        long timeStamp = System.currentTimeMillis();

        // Perform big count operation on timestamp
        finalNum = bigCountOperation(String.valueOf(timeStamp), finalNum);

        // Perform big count operation on the URL
        long urlNum = bigCountOperation(url, finalNum);
        finalNum = urlNum;

        // Create a 32-bit binary string from a combination of operations
        long longStrValue = ((65521 * (finalNum % 65521) ^ timeStamp) & 0xFFFFFFFF);
        String longStr = Integer.toBinaryString((int) longStrValue);
        while (longStr.length() < 32) {
            longStr = "0" + longStr;
        }

        // Create a binary number and parse it into decimal
        String binaryNum = "10000000110000" + longStr;
        long deciNum = Long.parseLong(binaryNum, 2);

        // Perform countToText operations
        acSignature = countToText(deciNum >> 2, acSignature);
        acSignature = countToText((deciNum << 28) | 515, acSignature);
        acSignature = countToText((deciNum ^ 1489154074) >> 6, acSignature);

        // Perform operation for the 'aloneNum'
        long aloneNum = (deciNum ^ 1489154074) & 63;
        int aloneVal = (aloneNum < 26) ? 65 : (aloneNum < 52) ? 71 : (aloneNum < 62) ? -4 : -17;
        acSignature += (char) (aloneNum + aloneVal);

        // Reset finalNum and perform additional operations
        finalNum = 0;
        long deciOperaNum = bigCountOperation(String.valueOf(deciNum), finalNum);
        finalNum = deciOperaNum;
        long nonceNum = bigCountOperation(acNonce, finalNum);
        finalNum = deciOperaNum;
        bigCountOperation(ua, finalNum);

        // More countToText operations
        acSignature = countToText((nonceNum % 65521 | ((finalNum % 65521) << 16)) >> 2, acSignature);
        acSignature = countToText((((finalNum % 65521 << 16) ^ (nonceNum % 65521)) << 28) |
                (((deciNum << 524576) ^ 524576) >> 4), acSignature);
        acSignature = countToText(urlNum % 65521, acSignature);

        // Final temp operations and appending to acSignature
        for (char i : acSignature.toCharArray()) {
            temp = ((temp * 65599) + (int) i) & 0xFFFFFFFF;
        }

        String lastStr = Long.toHexString(temp);
        acSignature += lastStr.substring(lastStr.length() - 2);

        return acSignature;
    }

    public JSONObject getDouyinLiveDataFromPC(String userAgent, String pcLiveUrl, String cookieContent) throws Exception {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Referer", "https://live.douyin.com/");
        headerMap.put("User-Agent", userAgent);
        headerMap.put("Cookie", cookieContent);

        String htmlStr = HttpClientUtil.doGet(pcLiveUrl, headerMap);

        Document document = Jsoup.parse(htmlStr);
        Elements scripts = document.getElementsByTag("script");

        JSONObject jsonData = new JSONObject();
        for (Element script : scripts) {
            String targetStr = script.html();
            if (targetStr.contains("roomStore")) {
                String newString = targetStr.replace("\\\"", "\"").replace("\\\"", "\"");
                Pattern pattern = Pattern.compile("self\\.\\__pace_f\\.push\\(\\[.*?null,");
                Matcher matcher = pattern.matcher(newString);
                newString = matcher.replaceAll("");

                newString = newString.replace("]\\n\"])", "").trim();

                JSONObject jsonObj = JSONObject.parseObject(newString);
                if (!jsonObj.containsKey("state") ||
                        !jsonObj.getJSONObject("state").containsKey("roomStore") ||
                        !jsonObj.getJSONObject("state").getJSONObject("roomStore").containsKey("roomInfo") ||
                        !jsonObj.getJSONObject("state").getJSONObject("roomStore").getJSONObject("roomInfo").containsKey("room") ||
                        !jsonObj.getJSONObject("state").getJSONObject("roomStore").getJSONObject("roomInfo").getJSONObject("room").containsKey("status")) {
                    continue;
                }

                int liveStatus = jsonObj.getJSONObject("state")
                        .getJSONObject("roomStore")
                        .getJSONObject("roomInfo")
                        .getJSONObject("room")
                        .getIntValue("status");

                if (liveStatus == 4) {
                    jsonData.put("status", liveStatus);
                    System.out.println("直播已结束");
                    break;
                }

                JSONObject originUrlList = jsonObj.getJSONObject("state")
                        .getJSONObject("streamStore")
                        .getJSONObject("streamData")
                        .getJSONObject("H264_streamData")
                        .getJSONObject("stream")
                        .getJSONObject("origin")
                        .getJSONObject("main");

                JSONObject originM3u8 = new JSONObject();
                originM3u8.put("ORIGIN", originUrlList.getString("hls"));

                JSONObject originFlv = new JSONObject();
                originFlv.put("ORIGIN", originUrlList.getString("flv"));

                JSONObject hlsPullUrlMap = jsonObj.getJSONObject("state")
                        .getJSONObject("roomStore")
                        .getJSONObject("roomInfo")
                        .getJSONObject("room")
                        .getJSONObject("stream_url")
                        .getJSONObject("hls_pull_url_map");

                JSONObject flvPullUrlMap = jsonObj.getJSONObject("state")
                        .getJSONObject("roomStore")
                        .getJSONObject("roomInfo")
                        .getJSONObject("room")
                        .getJSONObject("stream_url")
                        .getJSONObject("flv_pull_url");

                hlsPullUrlMap.putAll(originM3u8);
                flvPullUrlMap.putAll(originFlv);

                jsonData.put("hls_pull_url_map", hlsPullUrlMap);
                jsonData.put("flv_pull_url_map", flvPullUrlMap);

                System.out.println("jsonObj.toJSONString() = " + jsonObj.toJSONString());

                jsonData.put("status", liveStatus);
                jsonData.put("user_count", jsonObj.getJSONObject("state")
                        .getJSONObject("roomStore")
                        .getJSONObject("roomInfo")
                        .getJSONObject("room")
                        .getString("user_count_str"));
                jsonData.put("nick_name", jsonObj.getJSONObject("state")
                        .getJSONObject("roomStore")
                        .getJSONObject("roomInfo")
                        .getJSONObject("room")
                        .getJSONObject("owner")
                        .getString("nickname"));
                jsonData.put("room_title", jsonObj.getJSONObject("state")
                        .getJSONObject("roomStore")
                        .getJSONObject("roomInfo")
                        .getJSONObject("room")
                        .getString("title"));
                jsonData.put("nick_avatar", jsonObj.getJSONObject("state")
                        .getJSONObject("roomStore")
                        .getJSONObject("roomInfo")
                        .getJSONObject("room")
                        .getJSONObject("cover")
                        .getJSONArray("url_list").get(0));
            }
        }

        return jsonData;
    }

    public void saveVideoSlice(String userAgent, String recordUrl, VideoMaterialDTO videoMaterialDTO) throws Exception {
        if (recordUrl == null || recordUrl.equals("")) {
            System.out.println("直播已结束");
            return;
        }

        String analyzeduration = "20000000";
        String probesize = "10000000";
        String bufsize = "8000k";
        String maxMuxingQueueSize = "1024";

        List<String> ffmpegCommand = new ArrayList<>();
        ffmpegCommand.add("ffmpeg");
        ffmpegCommand.add("-y");
        ffmpegCommand.add("-v");
        ffmpegCommand.add("verbose");
        ffmpegCommand.add("-rw_timeout");
        ffmpegCommand.add("30000000");
        ffmpegCommand.add("-loglevel");
        ffmpegCommand.add("error");
        ffmpegCommand.add("-hide_banner");
        ffmpegCommand.add("-user_agent");
        ffmpegCommand.add(userAgent);
        ffmpegCommand.add("-protocol_whitelist");
        ffmpegCommand.add("rtmp,crypto,file,http,https,tcp,tls,udp,rtp");
        ffmpegCommand.add("-thread_queue_size");
        ffmpegCommand.add("1024");
        ffmpegCommand.add("-analyzeduration");
        ffmpegCommand.add(analyzeduration);
        ffmpegCommand.add("-probesize");
        ffmpegCommand.add(probesize);
        ffmpegCommand.add("-fflags");
        ffmpegCommand.add("+discardcorrupt");
        ffmpegCommand.add("-i");
        ffmpegCommand.add(recordUrl);
        ffmpegCommand.add("-bufsize");
        ffmpegCommand.add(bufsize);
        ffmpegCommand.add("-sn");
        ffmpegCommand.add("-dn");
        ffmpegCommand.add("-reconnect_delay_max");
        ffmpegCommand.add("60");
        ffmpegCommand.add("-reconnect_streamed");
        ffmpegCommand.add("-reconnect_at_eof");
        ffmpegCommand.add("-max_muxing_queue_size");
        ffmpegCommand.add(maxMuxingQueueSize);
        ffmpegCommand.add("-correct_ts_overflow");
        ffmpegCommand.add("1");
        String saveFilePath = getVideoMaterialSliceBasePath(videoMaterialDTO) + "_%04d.mp4";

        ffmpegCommand.add("-c:v");
        ffmpegCommand.add("copy");
        ffmpegCommand.add("-c:a");
        ffmpegCommand.add("aac");
        ffmpegCommand.add("-map");
        ffmpegCommand.add("0");
        ffmpegCommand.add("-f");
        ffmpegCommand.add("segment");
        ffmpegCommand.add("-segment_time");
        ffmpegCommand.add(liveSliceDuration);
        ffmpegCommand.add("-segment_time_delta");
        ffmpegCommand.add("0.01");
        ffmpegCommand.add("-segment_format");
        ffmpegCommand.add("mp4");
        ffmpegCommand.add("-reset_timestamps");
        ffmpegCommand.add("1");
        ffmpegCommand.add("-pix_fmt");
        ffmpegCommand.add("yuv420p");
        ffmpegCommand.add(saveFilePath);

        System.out.println("开始拉取直播视频流...");

        ProcessBuilder processBuilder = new ProcessBuilder(ffmpegCommand);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();
        int exitCode = process.waitFor();

        // 理论上这行不会执行
        System.out.println("FFmpeg exited with code: " + exitCode);
    }

    public String getVideoMaterialSliceBasePath(VideoMaterialDTO videoMaterialDTO) {
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateTimeStr = dateFormat.format(videoMaterialDTO.getStartDate());
        return hlsTmpPath + "/" + videoMaterialDTO.getSellerId() + "_" + dateTimeStr + videoMaterialDTO.getStartTime();
    }

    public String getVideoMaterialSliceBasePath(CreateVideoReq req) {
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateTimeStr = dateFormat.format(req.getStartDate());
        return hlsTmpPath + "/" + req.getSellerId() + "_" + dateTimeStr + req.getStartTime();
    }


    public String[] getSecUserId(String userAgent, String url) throws Exception {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("User-Agent", userAgent);

        String responseContent = HttpClientUtil.doGet(url, headerMap, false);
        if (responseContent.endsWith(".")) {
            responseContent = responseContent.substring(0, responseContent.length() - 1);
        }

        Document doc = Jsoup.parse(responseContent);
        Element aTag = doc.select("a[href]").first();

        String href = aTag.attr("href");
        String[] parts = href.split("\\?");
        String baseUrl = parts[0];

        URI uri = new URI(baseUrl);
        Path path = Paths.get(uri.getPath());
        String reflowId = path.getFileName().toString();

        String queryParams = parts.length > 1 ? parts[1] : "";
        String[] paramsArray = queryParams.split("&");

        Map<String, List<String>> params = new HashMap<>();
        for (String param : paramsArray) {
            String[] keyValue = param.split("=");
            String key = keyValue[0];
            String value = keyValue.length > 1 ? keyValue[1] : "";
            params.computeIfAbsent(key, k -> new java.util.ArrayList<>()).add(value);
        }

        String secUserId = params.getOrDefault("iid", Arrays.asList("")).get(0);
        return new String[]{reflowId, secUserId};
    }

    public String getTtwid(String userAgent) throws IOException {
        String requestUrl = "https://ttwid.bytedance.com/ttwid/union/register/";

        JSONObject data = new JSONObject();
        data.put("aid", 2906);
        data.put("service", "douyin.com");
        data.put("unionHost", "https://ttwid.bytedance.com");
        data.put("needFid", "false");
        data.put("union", "true");
        data.put("fid", "");


        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("User-Agent", userAgent);
        headerMap.put("Content-Type", "application/json");

        String responseContent = HttpClientUtil.doPost(requestUrl, data.toJSONString(), headerMap);
        JSONObject jsonObj = JSONObject.parseObject(responseContent);
        String callbackUrl = jsonObj.getString("redirect_url");

        String cookieHeader = HttpClientUtil.doGetCookie(callbackUrl, headerMap);
        String ttwid = getCookieValue(cookieHeader, "ttwid");
        return ttwid;
    }

    public String getWebId(String userAgent) throws IOException {
        int appId = 6383;
        JSONObject body = new JSONObject();
        body.put("app_id", appId);
        body.put("referer", "https://www.douyin.com/");
        body.put("url", "https://www.douyin.com/");
        body.put("user_agent", userAgent);
        body.put("user_unique_id", "");

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json; charset=UTF-8");
        headerMap.put("Referer", "https://www.douyin.com/");
        headerMap.put("User-Agent", userAgent);

        String requestUrl = String.format("https://mcs.zijieapi.com/webid?aid=%d&sdk_version=5.1.18_zip&device_platform=web", appId);
        String responseContent = HttpClientUtil.doPost(requestUrl, body.toJSONString(), headerMap);

        JSONObject jsonObj = JSONObject.parseObject(responseContent);
        return jsonObj.getString("web_id");
    }


    public String getDouyinLiveRoomId(String userAgent, String mobileLiveUrl) throws Exception {
        String[] secUserInfo = getSecUserId(userAgent, mobileLiveUrl);
        String reflowId = secUserInfo[0];
        String secUserId = secUserInfo[1];

        String ttwid = getTtwid(userAgent);
        String webId = getWebId(userAgent);

        String cookieContent = String.format("ttwid=%s;webid=%s", ttwid, webId);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("User-Agent", userAgent);
        headerMap.put("Host", "webcast.amemv.com");
        headerMap.put("Cookie", cookieContent);

        String requestUrl = "https://webcast.amemv.com/douyin/webcast/reflow/" + reflowId;
        String htmlStr = HttpClientUtil.doGet(requestUrl, headerMap);

        Document document = Jsoup.parse(htmlStr);
        Elements scripts = document.getElementsByTag("script");

        for (Element script : scripts) {
            String targetStr = script.html();
            if (targetStr.contains("webRid")) {
                String newString = targetStr.replace("\\\"", "\"").replace("\\\"", "\"");
                Pattern pattern = Pattern.compile("self\\.\\__rsc_f\\.push\\(\\[.*?null,");
                Matcher matcher = pattern.matcher(newString);
                newString = matcher.replaceAll("");

                newString = newString.replace("]\\n\"])", "").trim();

                JSONObject jsonObj = JSONObject.parseObject(newString);

                return jsonObj.getJSONObject("data").getJSONObject("room").getJSONObject("owner").getString("webRid");
            }
        }
        return null;
    }

    public String beautyJson(JSONObject object) {
        return JSON.toJSONString(object, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteDateUseDateFormat);
    }

    public void goodSleepSecs(int seconds) {
        try {
            TimeUnit.SECONDS.sleep(seconds);
        } catch (Throwable e) {
        }
    }

    public boolean create(String url, Long sellerId) {
        Long videoId = null;
        try {
            String userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36";

            String[] parts = url.split("\\?");
            String urlWithoutQuery = parts[0];

            System.out.println("移除查询参数后的URL: " + urlWithoutQuery);

            // 如果是移动端直播链接，需要先转成PC端链接
            String roomId = null;
            if (urlWithoutQuery.startsWith("https://v.douyin.com/")) {
                int try_times = 0;
                while (true) {
                    roomId = getDouyinLiveRoomId(userAgent, urlWithoutQuery);
                    if (roomId == null || roomId.equals("")) {
                        break;
                    }
                    goodSleepSecs(1);
                    try_times++;
                    if (try_times > 10) {
                        System.out.println("获取直播间ID失败");
                        break;
                    }
                }
                log.info("roomId = " + roomId);
                urlWithoutQuery = "https://live.douyin.com/" + roomId;
            } else {
                roomId = urlWithoutQuery.substring(24);
            }

            // 下面是正常情况下的pc端直播链接的处理逻辑
            String acNonce = getAcNonce(userAgent, urlWithoutQuery);
            String acSignature = loadAcSignature(urlWithoutQuery, acNonce, userAgent);

            // 此cookie有效期只有30分钟
            String cookieContent = String.format("__ac_nonce=%s; __ac_signature=%s; ; __ac_referer=__ac_blank", acNonce, acSignature);

            System.out.println("cookieContent = " + cookieContent);
            JSONObject live_data = getDouyinLiveDataFromPC(userAgent, urlWithoutQuery, cookieContent);
            System.out.println(beautyJson(live_data));

            if (roomId == null || live_data.getIntValue("status") == 4) {
                System.out.println("直播间已结束");
                return false;
            }

            String recordUrl = live_data.getJSONObject("hls_pull_url_map").getString("ORIGIN");
            System.out.println("recordUrl = " + recordUrl);

            Date datetime = new Date();
            String dateTimeStr = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(datetime);
            String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(datetime);
            Date date = new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
            long startTs = datetime.getTime() - date.getTime();
            CreateVideoReq createVideoReq = new CreateVideoReq();
            createVideoReq.setSellerId(sellerId);
            createVideoReq.setStartDate(datetime);
            createVideoReq.setLiveRoomId(roomId);
            createVideoReq.setStartTime((int) startTs);
            createVideoReq.setStartDateTimeStr(dateTimeStr);
            createVideoReq.setVideoPath(getVideoMaterialSliceBasePath(createVideoReq) + ".mp4");
            VideoMaterialDTO videoMaterialDTO = videoMaterialService.createVideoMaterialFromLiveRoom(createVideoReq);
            Runnable runnable = () -> {
                try {
                    saveVideoSlice(userAgent, recordUrl, videoMaterialDTO);
                } catch (Exception e) {
                    log.error("save video slice error", e);
                } finally {
                    videoMaterialService.setVideoMaterialDone(videoId);
                }
            };
            Thread thread = new Thread(runnable);
            thread.start();
            return true;
        } catch (Exception e) {
            log.error("catch live video error", e);
            return false;
        }
    }
}