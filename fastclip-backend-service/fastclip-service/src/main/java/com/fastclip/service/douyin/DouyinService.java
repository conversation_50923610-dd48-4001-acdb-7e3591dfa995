package com.fastclip.service.douyin;

import com.alibaba.fastjson.JSON;
import com.aliyun.tea.TeaException;
import com.douyin.openapi.client.Client;
import com.douyin.openapi.client.models.OauthAccessTokenRequest;
import com.douyin.openapi.client.models.OauthAccessTokenResponse;
import com.douyin.openapi.client.models.OauthAccessTokenResponseData;
import com.douyin.openapi.credential.models.Config;
import com.fastclip.common.model.ao.DouyinAO;
import com.fastclip.common.model.dataobject.DouyinDO;
import com.fastclip.common.model.dto.DouyinAuthReq;
import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.ItemPromotionJudgeRes;
import com.fastclip.common.model.request.ItemJustifyPromotionReq;
import com.fastclip.common.utils.DouyinUtil;
import com.fastclip.dao.mapper.DouyinAccountMapper;
import com.fastclip.dao.mapper.DouyinMapper;
import com.fastclip.dao.model.dataobject.DouyinAccount;
import com.fastclip.dao.model.dataobject.DouyinAccountExample;
import com.fastclip.service.*;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DouyinService {

    @Value("${douyin.cookie}")
    String cookie;

    @Value("${douyin.referer}")
    String referer;

    @Value("${douyin.itemPromotionJustifyUrl}")
    String itemPromotionJustifyUrl;

    @Value("${douyin.openapi.client_key}")
    String clientKey;

    @Value("${douyin.openapi.client_secret}")
    String clientSecret;

    @Autowired
    LoadingCache<Long, ItemDTO> itemCache;

    @Autowired
    DouyinAccountMapper douyinAccountMapper;


    public Boolean justifyItemPromotion(ItemJustifyPromotionReq req) {
        String url = req.getUrl();
        if(req.getItemId() != null) {
            ItemDTO itemDTO = itemCache.get(req.getItemId());
            if(itemDTO != null && itemDTO.getShareUrl() != null) {
                url = itemDTO.getShareUrl();
            }
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        RequestBody formBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("promotion_link", url)
                .build();
        try {
            String json = HttpClientUtil.doPost(itemPromotionJustifyUrl, formBody, cookie, referer);
            ItemPromotionJudgeRes res = JSON.parseObject(json, ItemPromotionJudgeRes.class);
            return res.getStatus_code() == 0;
        }catch (Exception e) {
            log.error("justify error",e);
            return false;
        }
    }

    public Boolean auth(String code, String phone, String scopes) {
        DouyinAccount douyinAccount = new DouyinAccount();
        douyinAccount.setPhone(phone);
        douyinAccount.setCode(code);
        douyinAccount.setUpdateTime(new Date());
        DouyinAccountExample douyinAccountExample = new DouyinAccountExample();
        douyinAccountExample.createCriteria().andPhoneEqualTo(phone);
        douyinAccountMapper.updateByExampleSelective(douyinAccount, douyinAccountExample);
        updateAccessToken(phone);
        return true;
    }

    public Boolean updateAccessToken(String phone) {
        DouyinAccountExample douyinAccountExample = new DouyinAccountExample();
        douyinAccountExample.createCriteria().andPhoneEqualTo(phone);
        List<DouyinAccount> douyinAccountList = douyinAccountMapper.selectByExample(douyinAccountExample);
        if(CollectionUtils.isEmpty(douyinAccountList)) {
            throw new RuntimeException("手机号不存在");
        }
        DouyinAccount douyinAccount =  douyinAccountList.get(0);
        if(douyinAccount.getCode() == null) {
            throw new RuntimeException("抖音号未认证");
        }
        try {
            Config config = new Config().setClientKey(clientKey).setClientSecret(clientSecret);
            Client client = new Client(config);
            OauthAccessTokenRequest sdkRequest = new OauthAccessTokenRequest();
            sdkRequest.setClientKey(clientKey);
            sdkRequest.setClientSecret(clientSecret);
            sdkRequest.setCode(douyinAccount.getCode());
            sdkRequest.setGrantType("ci9c7azgyi");
            OauthAccessTokenResponse sdkResponse = client.OauthAccessToken(sdkRequest);
            OauthAccessTokenResponseData data = sdkResponse.getData();
            douyinAccount.setUpdateTime(new Date());
            douyinAccount.setAccessToken(data.getAccessToken());
            douyinAccount.setExpireTime(new Date(System.currentTimeMillis() + data.getExpiresIn()));
            douyinAccountMapper.updateByExampleSelective(douyinAccount, douyinAccountExample);
        } catch (Exception e) {
            log.error("update access token error", e);
            throw new RuntimeException("更新抖音token失败！");
        }
        return true;
    }

}
