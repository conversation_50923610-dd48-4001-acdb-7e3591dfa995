package com.fastclip.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

@Service
@Slf4j
public class VideoDownloader {
    public boolean downloadVideo(String videoUrl, String savePath) {
        try {
            URL url = new URL(videoUrl);
            URLConnection conn = url.openConnection();
            InputStream in = conn.getInputStream();
            FileOutputStream out = new FileOutputStream(savePath);

            byte[] buffer =  new byte[1024];
            int bytesRead;
            while((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }

            out.close();
            in.close();
            return true;
        }catch (Exception e) {
            log.error("download vedio error" ,e);
        }
        return false;
    }
}
