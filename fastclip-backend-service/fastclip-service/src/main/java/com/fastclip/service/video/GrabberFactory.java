package com.fastclip.service.video;

import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.constant.VideoPlayTypeEnum;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.GetWorksReq;
import com.fastclip.common.model.request.VideoPlayReq;
import com.fastclip.common.utils.SpringUtils;
import com.fastclip.dao.mapper.VideoMaterialSliceMapper;
import com.fastclip.dao.model.dataobject.VideoMaterialSlice;
import com.fastclip.dao.model.dataobject.VideoMaterialSliceExample;
import com.fastclip.dao.utils.VideoMaterialSliceUtils;
import com.fastclip.opencv.grabber.*;
import com.fastclip.service.subtitles.SubtitlesService;
import com.fastclip.service.works.WorksService;
import org.opencv.video.Video;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GrabberFactory {

    @Autowired
    VideoClipService videoClipService;

    @Autowired
    SubtitlesService subtitlesService;

    @Autowired
    WorksService worksService;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    VideoMaterialSliceService videoMaterialSliceService;

    @Autowired
    VideoMaterialSliceMapper videoMaterialSliceMapper;

    public Grabber createGrabber(VideoPlayReq videoPlayReq) {
        Grabber grabber = null;
        switch (videoPlayReq.getPlayType()) {
            case 1:
                grabber = new VideoClipGrabber();
                VideoClipDTO videoClipDTO = videoClipService.getVedioClipById(videoPlayReq.getVideoClipId());
                ((VideoClipGrabber)grabber).setVideoClipDTO(videoClipDTO);
                break;
            case 2:
                grabber = new WorksGrabber();
                GetWorksReq worksReq = new GetWorksReq();
                worksReq.setWorksId(videoPlayReq.getWorksId());
                WorksDTO worksDTO = worksService.getWorks(worksReq).get(0);
                ((WorksGrabber) grabber).setWorksDTO(worksDTO);
                break;
            case 3:

                SubtitlesDTO subtitlesDTO = subtitlesService.getSubtitlesById(videoPlayReq.getSubtitlesId());
                VideoMaterialDTO videoMaterialDTO = videoMaterialService.getVideoMaterialById(subtitlesDTO.getVideoId());
                //视频类型是直播录制，且未完成合并
                if(videoMaterialDTO.getVideoType().equals(VideoMaterialTypeEnum.LIVE.getValue()) &&
                videoMaterialDTO.getStatus().equals(VideoMaterialCombineStatusEnum.PROCESSING.getValue())) {
                    grabber = new LiveVideoSubtitlesGrabber();
                    List<VideoMaterialSliceDTO> videoMaterialSliceDTOS = videoMaterialSliceService.
                            getVideoMaterialSlicesByVideoId(videoMaterialDTO.getId());
                    videoMaterialDTO.setVideoMaterialSliceDTOS(videoMaterialSliceDTOS);
                }
                //视频是素材下载，或已完成合并的录制视频
                else {
                    grabber = new VideoSubtitlesGrabber();
                }
                ((VideoSubtitlesGrabber)grabber).setVideoMaterialDTO(videoMaterialDTO);
                ((VideoSubtitlesGrabber)grabber).setSubtitlesDTO(subtitlesDTO);
                break;

        }
        grabber.setVideoOrAudio(videoPlayReq.getVideoOrAudio());
        grabber.setIsPlay(true);
        return grabber;
    }
}
