package com.fastclip.service.team;

import au.com.bytecode.opencsv.CSVReader;
import com.fastclip.common.constant.ChineseToNum;
import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.DouyinAccountByTeamRes;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.ItemUtils;
import com.fastclip.dao.utils.TeamUtils;
import com.fastclip.dao.utils.VideoUtils;
import com.fastclip.service.video.VideoMaterialService;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.sun.org.apache.xpath.internal.operations.Bool;
import org.apache.ibatis.session.RowBounds;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TeamService {

    @Autowired
    TeamMapper teamMapper;

    @Autowired
    DouyinAccountMapperExt douyinAccountMapperExt;

    public PagebleRes<TeamDTO> getTeamList(TeamReq req) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        PagebleRes<TeamDTO> pagebleRes = new PagebleRes();
        List<TeamDTO> teamDTOs = new ArrayList<>();
        pagebleRes.setData(teamDTOs);
        if(req.getPageSize() == null) {
            req.setPageSize(10);
        }
        if(req.getPageNum() == null) {
            req.setPageNum(1);
        }
        req.setOffset((req.getPageNum() -1) * req.getPageSize());
        TeamExample example = new TeamExample();
        if(req.getTeamName() != null) {
            example.createCriteria().andNameLike("%" + req.getTeamName() + "%");
        }
        List<Team> teams = teamMapper.selectByExample(example);
        List<Long> teamIds = teams.stream().map(Team::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(teamIds)) {
            return pagebleRes;
        }
        DouyinAccountByTeamReq douyinAccountByTeamReq = new DouyinAccountByTeamReq();
        douyinAccountByTeamReq.setTeamIds(teamIds);
        List<DouyinAccountByTeamRes> res = douyinAccountMapperExt.countByTeamId(douyinAccountByTeamReq);
        Map<Long, Integer> teamCountMap = new HashMap<>();
        for(DouyinAccountByTeamRes r: res) {
            if(r!=null) {
                teamCountMap.put(r.getId(), r.getCount());
            }
        }
        teamDTOs = TeamUtils.do2DTO(teams);
        for(TeamDTO teamDTO: teamDTOs) {
            if(teamCountMap.get(teamDTO.getId()) == null) {
                teamDTO.setNumOfDouyinAccount(0);
            }else{
                teamDTO.setNumOfDouyinAccount(teamCountMap.get(teamDTO.getId()));
            }
        }
        Long count = teamMapper.countByExample(example);
        pagebleRes.setData(teamDTOs);
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal(count.intValue());
        return pagebleRes;
    }

    /**
     * 删除商品
     * @param req
     * @return
     */
    public Boolean deleteTeam(DeleteTeamReq req) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        TeamExample teamExample = new TeamExample();
        teamExample.createCriteria().andIdIn(req.getTeamIds());
        int res = teamMapper.deleteByExample(teamExample);
        if (res > 0) {
            return true;
        }
        return false;
    }

    /**
     * 更新商品
     * @param team
     * @return
     */
    public Boolean updateTeam(Team team) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        team.setUpdateTime(new Date());
        int res = teamMapper.updateByPrimaryKeySelective(team);
        if (res > 0)
            return true;
        return false;
    }

    /**
     *  增加团队
     * @param team
     * @return
     */
    public Integer addTeam(Team team) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        team.setCreateTime(new Date());
        team.setUpdateTime(new Date());
        return teamMapper.insert(team);
    }

}
