package com.fastclip.service.douyin;

import com.alibaba.fastjson.JSON;
import com.fastclip.common.model.dataobject.OrderDO;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.GetDouyinOrdersReq;
import com.fastclip.common.model.request.GetOrderListReq;
import com.fastclip.common.model.request.SyncOrderReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.DouyinAccountMapper;
import com.fastclip.dao.mapper.OrdersMapper;
import com.fastclip.dao.mapper.OrderMapperExt;
import com.fastclip.dao.model.dataobject.DouyinAccount;
import com.fastclip.dao.model.dataobject.DouyinAccountExample;
import com.fastclip.dao.model.dataobject.Orders;
import com.fastclip.dao.model.dataobject.OrderExample;
import com.fastclip.dao.utils.OrderUtils;
import com.fastclip.service.http.DouyinHttpUtils;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class OrderService {

    @Value("${douyin.appAccessToken}")
    String appAccessToken;

    @Autowired
    OrdersMapper ordersMapper;

    @Autowired
    DouyinAccountMapper douyinAccountMapper;

    @Autowired
    DouyinHttpUtils douyinHttpUtils;

    @Autowired
    OrderMapperExt orderMapperExt;

    @Autowired
    LoadingCache<String, DouyinAccountDTO> douyinAccountCache;

    public Boolean insertOrder(OrdersDTO ordersDTO) {
        try {
            OrderExample orderExample = new OrderExample();
            orderExample.createCriteria().andOrderIdEqualTo(ordersDTO.getOrderId());
            List<Orders> orders = ordersMapper.selectByExample(orderExample);
            Orders order = OrderUtils.dto2DO(ordersDTO);
            if (CollectionUtils.isEmpty(orders)) {
                ordersMapper.insert(order);
            } else {
                ordersMapper.updateByExampleSelective(order, orderExample);
            }
        } catch (Exception e) {
            log.error("insert order error, orderId={}", ordersDTO.getOrderId(), e);
            return false;
        }
        return true;
    }

    public Boolean syncOrder(SyncOrderReq syncOrderReq) {
        try {
            if(syncOrderReq.getIsSyncAll()) {
                syncAll();
            }else if(syncOrderReq.getAccountId() != null){
                DouyinAccount douyinAccount = douyinAccountMapper.selectByPrimaryKey(syncOrderReq.getAccountId());
                syncByAccount(douyinAccount);
            }
        } catch (Exception e) {
            log.error("sync order error", e);
            return false;
        }
        return true;
    }

    public void syncAll() {
        DouyinAccountExample douyinAccountExample = new DouyinAccountExample();
        douyinAccountExample.createCriteria().andInviteDoneEqualTo(true);
        List<DouyinAccount> douyinAccounts = douyinAccountMapper.selectByExample(douyinAccountExample);
        for(DouyinAccount douyinAccount: douyinAccounts) {
            syncByAccount(douyinAccount);
        }
    }

    public void syncByAccount(DouyinAccount douyinAccount) {
        GetDouyinOrdersReq req = new GetDouyinOrdersReq();
        req.setOpenId(douyinAccount.getOpenId());
        String nowDate = TimeUtils.getNowDateDiffStr(-1);
        String nowDate90Before = TimeUtils.getNowDateDiffStr(60);
        req.setStartDate(nowDate90Before);
        req.setEndDate(nowDate);
        req.setUserAccessToken(douyinAccount.getAccessToken());
        syn(req);
    }

    public void syn(GetDouyinOrdersReq req) {
        int cursor = 0;
        req.setCursor(String.valueOf(cursor));
        List<OrdersDTO> datas = getDataFromJson(douyinHttpUtils.getOrders(req));
        while(!CollectionUtils.isEmpty(datas)) {
            orderMapperExt.batchInsertOrUpdateOrders(OrderUtils.dto2DO(datas));
            cursor++;
            req.setCursor(String.valueOf(cursor));
            datas = getDataFromJson(douyinHttpUtils.getOrders(req));
        }
    }

    private List<OrdersDTO> getDataFromJson(String data) {
        try{
            DouyinApiOrderResult result = JSON.parseObject(data, DouyinApiOrderResult.class);
            if(result == null || result.getData() == null || CollectionUtils.isEmpty(result.getData().getOrders())) {
                return new ArrayList<>();
            }
            return OrderUtils.apiDTO2DTO(result.getData().getOrders());
        }catch (Exception e) {
            log.error("get data from json error", e);
        }
        return new ArrayList<>();
    }


    public OrderInfoDTO getOrderList(GetOrderListReq req) {
        try{
            if(req.getPageNum() == 0) {
                req.setPageNum(1);
            }
            if(req.getPageSize() == 0) {
                req.setPageSize(10);
            }
            if("".equals(req.getDouyinAccountCode())) {
                req.setDouyinAccountCode(null);
            }
            if("".equals(req.getOrderId())) {
                req.setOrderId(null);
            }
            if(req.getTeamId() == null || req.getTeamId() <=0) {
                req.setTeamId(null);
            }
            if(req.getSelfCutterId() == null || req.getSelfCutterId() <= 0) {
                req.setSelfCutterId(null);
            }
            req.setOffset((req.getPageNum() -1) * req.getPageSize());
            OrderInfoDTO pagebleRes = new OrderInfoDTO();
            List<Orders> orders = orderMapperExt.getOrdersList(req);
            OrderDO orderDO = orderMapperExt.getOrdersCount(req);
            List<OrdersDTO> ordersDTOS = new ArrayList<>();
            for(Orders order:orders) {
                OrdersDTO ordersDTO = OrderUtils.do2DTO(order);
                ordersDTO.setDouyinAccountDTO(douyinAccountCache.get(order.getAuthorShortId()));
                ordersDTOS.add(ordersDTO);
            }
            pagebleRes.setData(ordersDTOS);
            pagebleRes.setTotal(orderDO.getOrderCount().intValue());
            pagebleRes.setTotalPayAmount(orderDO.getTotalPayAmount());
            return pagebleRes;
        }catch (Exception e) {
            log.error("get data from json error", e);
        }
        return new OrderInfoDTO();
    }
}