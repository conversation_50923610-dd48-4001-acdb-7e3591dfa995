package com.fastclip.service.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fastclip.common.model.request.GetDouyinOrdersReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class HttpUtils {

    @Autowired
    private RestTemplate restTemplate;

    public String post(String url, HttpHeaders headers, JSONObject requestBody) {
        //1、组装请求头和参数
        HttpEntity<String> formEntity = null;
        String request = null;
        if(requestBody != null) {
            request = JSON.toJSONString(requestBody);
        }
        formEntity = new HttpEntity<String>(request, headers);
        //2、发起post请求
        ResponseEntity<String> stringResponseEntity = null;
        try {
            stringResponseEntity = restTemplate.postForEntity(url, formEntity, String.class);
            log.info("ResponseEntity----" + stringResponseEntity);
        } catch (RestClientException e) {
            e.printStackTrace();
        }

        //3、获取http状态码
        int statusCodeValue = stringResponseEntity.getStatusCodeValue();
        log.info("httpCode-----" + statusCodeValue);

        //4、获取返回体
        String body = stringResponseEntity.getBody();
        log.info("body-----" + body);

        return body;
    }
}
