package com.fastclip.service.douyin;

import com.alibaba.fastjson.JSON;
import com.fastclip.common.constant.DouyinAccountTypeEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.DouyinAccountReq;
import com.fastclip.common.model.request.GetQRCodeReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.dao.mapper.DouyinAccountMapper;
import com.fastclip.dao.mapper.QRCodeMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.DouyinAccountUtils;
import com.fastclip.dao.utils.QRCodeUtils;
import com.fastclip.service.http.DouyinHttpUtils;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;

@Service
public class AccountService {

    @Autowired
    DouyinAccountMapper douyinAccountMapper;

    @Autowired
    DouyinHttpUtils httpUtils;

    @Autowired
    QRCodeMapper qrCodeMapper;

    @Autowired
    LoadingCache<Long, SellerDTO> sellerCache;

    @Autowired
    LoadingCache<Long, TeamDTO> teamCache;

    @Autowired
    LoadingCache<Long, UserDTO> userCache;

    public List<DouyinAccountDTO> getDouyinAccountsBySeller(Long sellerId) {
        DouyinAccountExample example = new DouyinAccountExample();
        DouyinAccountExample.Criteria criteria = example.createCriteria();
        criteria.andSellerIdEqualTo(sellerId);
        List<DouyinAccount> accounts = douyinAccountMapper.selectByExample(example);
        return DouyinAccountUtils.do2DTOs(accounts);
    }

    public DouyinAccountDTO getDouyinAccountByCode(String code) {
        DouyinAccountExample example = new DouyinAccountExample();
        DouyinAccountExample.Criteria criteria = example.createCriteria();
        criteria.andCodeEqualTo(code);
        List<DouyinAccount> accounts = douyinAccountMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(accounts)) {
            return null;
        }
        DouyinAccountDTO douyinAccountDTO = DouyinAccountUtils.do2DTO(accounts.get(0));
        if(DouyinAccountTypeEnum.TEAM.getValue().equals(douyinAccountDTO.getType())) {
            douyinAccountDTO.setTeamDTO(teamCache.get(douyinAccountDTO.getTeamId()));
        }
        if(DouyinAccountTypeEnum.SELF.getValue().equals(douyinAccountDTO.getType())) {
            douyinAccountDTO.setSelfUserDTO(userCache.get(douyinAccountDTO.getSelfId()));
        }
        return douyinAccountDTO;
    }

    public PagebleRes<DouyinAccountDTO> getDouyinAccountList(DouyinAccountReq req) {
        PagebleRes<DouyinAccountDTO> pagebleRes = new PagebleRes();
        List<DouyinAccountDTO> accountDTOS = new ArrayList<>();
        DouyinAccountExample douyinAccountExample = new DouyinAccountExample();
        douyinAccountExample.setOrderByClause("id desc");
        if (req.getPageNum() == null) {
            req.setPageNum(1);
        }
        if (req.getPageSize() == null) {
            req.setPageSize(20);
        }
        req.setOffset((req.getPageNum() -1) * req.getPageSize());
        RowBounds rowBounds = new RowBounds(req.getOffset(), req.getPageSize());
        List<DouyinAccount> accounts = douyinAccountMapper.selectByExampleWithRowbounds(douyinAccountExample, rowBounds);
        accountDTOS = DouyinAccountUtils.do2DTOs(accounts);
        for(DouyinAccountDTO douyinAccountDTO: accountDTOS) {
            douyinAccountDTO.setSellerDTO(sellerCache.get(douyinAccountDTO.getSellerId()));
            if(douyinAccountDTO.getTeamId()!=null) {
                douyinAccountDTO.setTeamDTO(teamCache.get(douyinAccountDTO.getTeamId()));
            }
        }
        long count = douyinAccountMapper.countByExample(douyinAccountExample);
        pagebleRes.setData(accountDTOS);
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal((int)count);
        return pagebleRes;
    }

    /**
     * 更新账号
     * @param req
     * @return
     */
    public Boolean updateAccount(DouyinAccount req) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        if(DouyinAccountTypeEnum.TEAM.getValue().equals(req.getType())) {
            if(req.getTeamId() == null) {
                throw new RuntimeException("团队id不能为空");
            }
        }
        int res = douyinAccountMapper.updateByPrimaryKeySelective(req);
        if (res > 0)
            return true;
        return false;
    }

    /**
     * 更新账号
     * @param id
     * @return
     */
    public Boolean deleteAccount(Long id) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        if(id == null) {
            throw new RuntimeException("请选择要删除的账号");
        }
        int res = douyinAccountMapper.deleteByPrimaryKey(id);
        if (res > 0)
            return true;
        return false;
    }

    /**
     * 增加账号
     * @param req
     * @return
     */
    public Boolean addAccount(DouyinAccount req) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        req.setCreateTime(new Date());
        req.setUpdateTime(new Date());
        checkParams(req);
        int res = douyinAccountMapper.insert(req);
        if (res > 0)
            return true;
        return false;
    }

    public void checkParams(DouyinAccount req) {
        if(req.getSellerId() == null) {
            throw new RuntimeException("请选择达人");
        }
        if(req.getPhone() == null) {
            throw new RuntimeException("请输入手机号");
        }
        if(req.getCode() == null) {
            throw new RuntimeException("输入抖音账号");
        }
        if(req.getCoverBackground() == null) {
            throw new RuntimeException("请输入封面背景路径");
        }
    }

    public boolean checkFileExist(String filePath) {
        File file = new File(filePath);
        if(file.exists()) {
            return true;
        }
        return false;
    }

    public QRCodeDTO getQRCode(GetQRCodeReq req) {
        String jsonStr = httpUtils.getQRCode();
        QRCodeAPIDTO qrCodeAPIDTO = JSON.parseObject(jsonStr, QRCodeAPIDTO.class);
        QRCodeDTO qrCodeDTO = QRCodeUtils.apiDTO2DTO(qrCodeAPIDTO);
        QRCode qrCode = QRCodeUtils.dto2DO(qrCodeDTO);
        qrCode.setCreateTime(new Date());
        qrCode.setUpdateTime(new Date());
        qrCode.setDouyinAccountId(req.getDouyinAccountId());
        qrCode.setStatus(0);
        qrCodeMapper.insert(qrCode);
        return qrCodeDTO;
    }

    public Boolean unbindInvite(GetQRCodeReq req) {
        DouyinAccount douyinAccount = douyinAccountMapper.selectByPrimaryKey(req.getDouyinAccountId());
        douyinAccount.setInviteDone(false);
        douyinAccountMapper.updateByPrimaryKey(douyinAccount);
        return true;
    }

    public List<DouyinAccountTypeDTO> getTypes() {
        List<DouyinAccountTypeDTO> typeDTOS = new ArrayList<>();
        for(DouyinAccountTypeEnum type: DouyinAccountTypeEnum.values()) {
            DouyinAccountTypeDTO typeDTO = new DouyinAccountTypeDTO();
            typeDTO.setCode(type.getValue());
            typeDTO.setDesc(type.getDesc());
            typeDTOS.add(typeDTO);
        }
        return typeDTOS;
    }

}
