package com.fastclip.service.douyin;

import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialStatusEnum;
import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.VideoClipSpeicalEffectDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.VideoMaterialMapper;
import com.fastclip.dao.mapper.VideoMaterialSliceMapper;
import com.fastclip.dao.model.dataobject.VideoMaterial;
import com.fastclip.dao.model.dataobject.VideoMaterialExample;
import com.fastclip.dao.model.dataobject.VideoMaterialSlice;
import com.fastclip.dao.model.dataobject.VideoMaterialSliceExample;
import com.fastclip.dao.utils.VideoUtils;
import com.fastclip.opencv.FFmpegCmd;
import com.fastclip.service.WhisperService;
import com.fastclip.service.video.VideoSubtitlesFetchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class LiveVideoSliceService {

    @Autowired
    VideoMaterialSliceMapper videoMaterialSliceMapper;

    @Autowired
    LiveVideoService liveVideoService;

    @Autowired
    WhisperService whisperService;

    @Autowired
    VideoSubtitlesFetchService videoSubtitlesFetchService;

    @Value("${douyin.liveSliceDuration}")
    Integer liveSliceDuration;

    @Value("${ffmpeg.livePath}")
    String livePath;

    @Autowired
    VideoMaterialMapper videoMaterialMapper;

    @Autowired
    FFmpegCmd fFmpegCmd;

    /**
     * 合并视频素材的碎片
     * @param videoId
     */
    public void fetchVideoSliceSubtitlesAndMerge(Long videoId) {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                VideoMaterial videoMaterial = videoMaterialMapper.selectByPrimaryKey(videoId);
                try {
                    while (true) {
                        VideoMaterialDTO videoMaterialDTO = VideoUtils.materialDo2DTO(videoMaterial);
                        Integer latestSliceId = videoMaterialDTO.getLatestSliceMergedId();
                        log.info("current video material videoId=" + videoId + " sliceId=" + latestSliceId);
                        latestSliceId++;
                        String curSliceIdStr = String.format("%04d", latestSliceId);
                        Integer nextSliceId = latestSliceId + 1;
                        String nextSliceIdStr = String.format("%04d", nextSliceId);
                        String curSlicePath = liveVideoService.getVideoMaterialSliceBasePath(videoMaterialDTO) + "_" + curSliceIdStr + ".mp4";
                        String nextSlicePath = liveVideoService.getVideoMaterialSliceBasePath(videoMaterialDTO) + "_" + nextSliceIdStr + ".mp4";
                        File fileToMerge = new File(curSlicePath);
                        File nextFileToMerge = new File(nextSlicePath);
                        if (fileToMerge.exists() && nextFileToMerge.exists() && isVideoMaterialProcessing(videoMaterialDTO)) {
                            fetchSubtitles(fileToMerge, latestSliceId, videoMaterial);
                        } else if (!isVideoMaterialProcessing(videoMaterialDTO) && fileToMerge.exists()) {
                            fetchSubtitles(fileToMerge, latestSliceId, videoMaterial);
                        } else if (!isVideoMaterialProcessing(videoMaterialDTO) && !fileToMerge.exists()) {
                            log.info("finish fetch video material videoId=" + videoId + " sliceId=" + latestSliceId);
                            videoMaterial.setIsSubtitlesDone(true);
                            videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
                            mergeSlices(videoMaterial);
                            return;
                        }
                        videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
                        sleep(1000);
                    }
                }catch (Exception e) {
                    log.error("fetch error", e);
                }
            }
        };
        Thread thread = new Thread(runnable);
        thread.start();
    }

    private void fetchSubtitles(File fileToMerge, Integer latestSliceNumber, VideoMaterial videoMaterial) {
        String filePathName = fileToMerge.getAbsolutePath();
        String srtPath = filePathName  + ".srt";
        whisperService.fetchSrt(fileToMerge.getAbsolutePath(), srtPath);
        videoSubtitlesFetchService.insertSubtitles(videoMaterial, srtPath, liveSliceDuration * 1000 * latestSliceNumber);
        videoMaterial.setUpdateTime(new Date());
        videoMaterial.setLatestSliceMergedId(latestSliceNumber);
        VideoMaterialSlice videoMaterialSlice = new VideoMaterialSlice();
        videoMaterialSlice.setVideoId(videoMaterial.getId());
        videoMaterialSlice.setDuration(liveSliceDuration);
        videoMaterialSlice.setCreateTime(new Date());
        videoMaterialSlice.setUpdateTime(new Date());
        videoMaterialSlice.setPath(filePathName);
        videoMaterialSlice.setIsSubtitlesDone(true);
        videoMaterialSlice.setNumber(latestSliceNumber);
        videoMaterialSlice.setStartTs((latestSliceNumber) * liveSliceDuration);
        videoMaterialSlice.setSize(0);
        videoMaterialSliceMapper.insert(videoMaterialSlice);
    }

    private void mergeSlices(VideoMaterial videoMaterial) {
        VideoMaterialSliceExample videoMaterialSliceExample = new VideoMaterialSliceExample();
        videoMaterialSliceExample.createCriteria().andVideoIdEqualTo(videoMaterial.getId());
        videoMaterialSliceExample.setOrderByClause("number");
        List<VideoMaterialSlice> videoMaterialSliceList = videoMaterialSliceMapper.selectByExample(videoMaterialSliceExample);
        String tmpPath = createTmpSliceListFile(videoMaterial.getId(), videoMaterialSliceList);
        ProcessBuilder process = new ProcessBuilder(
                "ffmpeg",
                "-f", "concat",
                "-i", tmpPath,
                "-c", videoMaterial.getPath(),
                "-y");
        try {
            fFmpegCmd.printProcessExecResult(process.start());
            videoMaterial.setCombineStatus(VideoMaterialCombineStatusEnum.DONE.getValue());
            videoMaterial.setUpdateTime(new Date());
            videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
        }catch (Exception e){
            log.error("insertSrt error", e);
        }
    }


    private String createTmpSliceListFile(Long videoId, List<VideoMaterialSlice> videoMaterialSliceList) {
        try {
            String path = livePath + "/" + videoId + "_sliceFileList.txt";
            File file = new File(path);
            BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(file));
            for (VideoMaterialSlice videoMaterialSlice : videoMaterialSliceList) {
                bufferedWriter.write(videoMaterialSlice.getPath() + "\n");
            }
            bufferedWriter.flush();
            bufferedWriter.close();
            return path;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    private void sleep(long timestamp) {
        try{
            Thread.sleep(timestamp);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean isVideoMaterialProcessing(VideoMaterialDTO videoMaterialDTO) {
        if(VideoMaterialStatusEnum.PROCESSING.getValue().equals(videoMaterialDTO.getStatus())) {
            return true;
        }
        return false;
    }
}
