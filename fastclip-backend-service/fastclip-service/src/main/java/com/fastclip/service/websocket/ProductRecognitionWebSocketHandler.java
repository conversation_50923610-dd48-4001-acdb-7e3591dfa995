package com.fastclip.service.websocket;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

@Component
@Slf4j
public class ProductRecognitionWebSocketHandler implements WebSocketHandler {

    // Store active WebSocket sessions
    private final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
    
    // Store session metadata (video processing tasks)
    private final ConcurrentHashMap<String, String> sessionTasks = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        log.info("WebSocket connection established: {}", session.getId());
        
        // Send connection confirmation
        JSONObject message = new JSONObject();
        message.put("type", "connection");
        message.put("status", "connected");
        message.put("sessionId", session.getId());
        
        session.sendMessage(new TextMessage(message.toJSONString()));
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();
            log.info("Received message from {}: {}", session.getId(), payload);
            
            try {
                JSONObject jsonMessage = JSONObject.parseObject(payload);
                String type = jsonMessage.getString("type");
                
                switch (type) {
                    case "start_recognition":
                        handleStartRecognition(session, jsonMessage);
                        break;
                    case "ping":
                        handlePing(session);
                        break;
                    default:
                        log.warn("Unknown message type: {}", type);
                }
            } catch (Exception e) {
                log.error("Error handling message", e);
                sendErrorMessage(session, "Invalid message format");
            }
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket transport error for session {}: {}", session.getId(), exception.getMessage());
        sessions.remove(session);
        sessionTasks.remove(session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        sessions.remove(session);
        sessionTasks.remove(session.getId());
        log.info("WebSocket connection closed: {} with status: {}", session.getId(), closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * Handle start recognition request
     */
    private void handleStartRecognition(WebSocketSession session, JSONObject message) throws Exception {
        String videoPath = message.getString("videoPath");
        String taskId = message.getString("taskId");
        
        sessionTasks.put(session.getId(), taskId);
        
        JSONObject response = new JSONObject();
        response.put("type", "recognition_started");
        response.put("taskId", taskId);
        response.put("videoPath", videoPath);
        response.put("status", "processing");
        
        session.sendMessage(new TextMessage(response.toJSONString()));
        log.info("Started recognition task {} for session {}", taskId, session.getId());
    }

    /**
     * Handle ping message
     */
    private void handlePing(WebSocketSession session) throws Exception {
        JSONObject pong = new JSONObject();
        pong.put("type", "pong");
        pong.put("timestamp", System.currentTimeMillis());
        
        session.sendMessage(new TextMessage(pong.toJSONString()));
    }

    /**
     * Send error message to client
     */
    private void sendErrorMessage(WebSocketSession session, String error) {
        try {
            JSONObject errorMessage = new JSONObject();
            errorMessage.put("type", "error");
            errorMessage.put("message", error);
            errorMessage.put("timestamp", System.currentTimeMillis());
            
            session.sendMessage(new TextMessage(errorMessage.toJSONString()));
        } catch (Exception e) {
            log.error("Failed to send error message", e);
        }
    }

    /**
     * Broadcast progress update to all connected clients
     * @param taskId Task identifier
     * @param progress Progress information
     */
    public void broadcastProgress(String taskId, ProgressUpdate progress) {
        JSONObject message = new JSONObject();
        message.put("type", "progress_update");
        message.put("taskId", taskId);
        message.put("timestamp", progress.timestamp);
        message.put("currentSegment", progress.currentSegment);
        message.put("totalSegments", progress.totalSegments);
        message.put("percentage", progress.percentage);
        
        broadcastMessage(message);
    }

    /**
     * Broadcast recognition result to all connected clients
     * @param taskId Task identifier
     * @param result Recognition result
     */
    public void broadcastResult(String taskId, RecognitionResult result) {
        JSONObject message = new JSONObject();
        message.put("type", "recognition_result");
        message.put("taskId", taskId);
        message.put("timestamp", result.timestamp);
        message.put("segmentIndex", result.segmentIndex);
        message.put("itemType", result.itemType);
        message.put("confidence", result.confidence);
        message.put("itemId", result.itemId);
        message.put("itemName", result.itemName);

        broadcastMessage(message);
    }

    /**
     * Broadcast completion message
     * @param taskId Task identifier
     */
    public void broadcastCompletion(String taskId) {
        JSONObject message = new JSONObject();
        message.put("type", "recognition_complete");
        message.put("taskId", taskId);
        message.put("timestamp", System.currentTimeMillis());

        broadcastMessage(message);
    }

    /**
     * Broadcast error message
     * @param taskId Task identifier
     * @param errorMessage Error message
     */
    public void broadcastError(String taskId, String errorMessage) {
        JSONObject message = new JSONObject();
        message.put("type", "error");
        message.put("taskId", taskId);
        message.put("message", errorMessage);
        message.put("timestamp", System.currentTimeMillis());

        broadcastMessage(message);
    }

    /**
     * Broadcast message to all active sessions
     */
    private void broadcastMessage(JSONObject message) {
        String messageStr = message.toJSONString();
        
        sessions.removeIf(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(messageStr));
                    return false;
                } else {
                    return true; // Remove closed sessions
                }
            } catch (Exception e) {
                log.error("Error sending message to session {}", session.getId(), e);
                return true; // Remove problematic sessions
            }
        });
    }

    /**
     * Data classes for WebSocket messages
     */
    public static class ProgressUpdate {
        public final long timestamp;
        public final int currentSegment;
        public final int totalSegments;
        public final double percentage;
        
        public ProgressUpdate(int currentSegment, int totalSegments) {
            this.timestamp = System.currentTimeMillis();
            this.currentSegment = currentSegment;
            this.totalSegments = totalSegments;
            this.percentage = (double) currentSegment / totalSegments * 100.0;
        }
    }

    public static class RecognitionResult {
        public final long timestamp;
        public final int segmentIndex;
        public final Integer itemType;
        public final double confidence;
        public final String itemId;
        public final String itemName;

        public RecognitionResult(long timestamp, int segmentIndex, Integer itemType, double confidence) {
            this.timestamp = timestamp;
            this.segmentIndex = segmentIndex;
            this.itemType = itemType;
            this.confidence = confidence;
            this.itemId = null;
            this.itemName = null;
        }

        public RecognitionResult(long timestamp, int segmentIndex, Integer itemType, double confidence, String itemId, String itemName) {
            this.timestamp = timestamp;
            this.segmentIndex = segmentIndex;
            this.itemType = itemType;
            this.confidence = confidence;
            this.itemId = itemId;
            this.itemName = itemName;
        }
    }
}
