//package com.fastclip.service.works;
//
//import com.fastclip.common.constant.TransitionEnum;
//import com.fastclip.common.constant.VideoClipTagEnum;
//import com.fastclip.common.model.context.SsoUserContext;
//import com.fastclip.common.model.dto.*;
//import com.fastclip.dao.mapper.MusicMapper;
//import com.fastclip.dao.mapper.StickerMapper;
//import com.fastclip.dao.model.dataobject.Sticker;
//import com.fastclip.dao.model.dataobject.StickerExample;
//import com.fastclip.dao.utils.StickerUtils;
//import com.fastclip.service.filterEffect.FilterEffectService;
//import com.fastclip.service.font.FontService;
//import com.fastclip.service.sticker.StickerService;
//import com.fastclip.service.workbench.MusicService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 根据相似度进行检测作品加工，确保不同作品的相似度最低
// */
//@Component
//public class SimilarityWorksProduceStrategy implements WorksProduceStrategy{
//
//    @Autowired
//    MusicService musicService;
//
//    @Autowired
//    FontService fontService;
//
//    @Autowired
//    StickerService stickerService;
//
//    @Autowired
//    FilterEffectService filterEffectService;
//
//    /**
//     * 作品加工策略。
//     * @param curWorks 当前已经生成的作品
//     * @param videoClipDTOList 用户加工作品的所有视频片段
//     * @return
//     */
//    @Override
//    @Transactional
//    public WorksDTO produce(ProjectDTO projectDTO, List<WorksDTO> curWorks, List<VideoClipDTO> videoClipDTOList) {
//        List<WorksDetailDTO> details = new ArrayList<>();
//        Map<Long, List<WorksDTO>> videoClipToWorksMap = new HashMap<>();
//        Set<Long> usedClips = new HashSet<>();
//        for(WorksDTO worksDTO: curWorks) {
//            List<WorksDetailDTO> worksDetailDTOS = worksDTO.getDetails();
//            for(WorksDetailDTO worksDetailDTO: worksDetailDTOS) {
//                List<WorksDTO> works = videoClipToWorksMap.get(worksDetailDTO.getVideoClipId());
//                if(CollectionUtils.isEmpty(works)) {
//                    works = new ArrayList<>();
//                }
//                works.add(worksDTO);
//                videoClipToWorksMap.put(worksDetailDTO.getVideoClipId(), works);
//                usedClips.add(worksDetailDTO.getVideoClipId());
//            }
//        }
//        List<VideoClipDTO> clipWithStartTags = new ArrayList<>();
//        List<VideoClipDTO> clipWithEndTags = new ArrayList<>();
//        List<VideoClipDTO> clipWithDressTags = new ArrayList<>();
//        List<VideoClipDTO> clipWithMaterialTags =new ArrayList<>();
//        List<VideoClipDTO> clipWithSizeTags = new ArrayList<>();
//        splitVideoClipTags(videoClipDTOList, clipWithStartTags, clipWithEndTags, clipWithDressTags, clipWithMaterialTags,
//                clipWithSizeTags);
//
//        int order = 1;
//        WorksDTO worksDTO = createWorks(projectDTO);
//        WorksDetailDTO startWorksDetailDTO;
//        WorksDetailDTO endWorksDetailDTO;
//        List<WorksDetailDTO> dressDetails = new ArrayList<>();
//        List<WorksDetailDTO> materialDetails = new ArrayList<>();
//        List<WorksDetailDTO> sizeDetails = new ArrayList<>();
//        Set<Long> usedClipIdsOfThisWorks = new HashSet<>();
//        //创建开头
//        if(!CollectionUtils.isEmpty(clipWithStartTags)) {
//            VideoClipDTO startClip = getStartOrEndCip(usedClips, clipWithStartTags, usedClipIdsOfThisWorks);
//            if(startClip == null) {
//                throw new RuntimeException("没有可用的标签为<开头>的视频片段！");
//            }
//            startWorksDetailDTO = createWorksDetail(projectDTO, startClip, VideoClipTagEnum.START.getCode());
//        }else{
//            throw new RuntimeException("没有可用的标签为<开头>的视频片段！");
//        }
//        //创建结尾
//        if(!CollectionUtils.isEmpty(clipWithEndTags)) {
//            VideoClipDTO endCip = getStartOrEndCip(usedClips, clipWithEndTags,usedClipIdsOfThisWorks);
//            if(endCip == null) {
//                throw new RuntimeException("没有可用的标签为<结尾>的视频片段！");
//            }
//            endWorksDetailDTO = createWorksDetail(projectDTO, endCip, VideoClipTagEnum.END.getCode());
//        }else {
//            throw new RuntimeException("没有可用的标签为<结尾>的视频片段！");
//        }
//        //创建穿搭描述
//        if(!CollectionUtils.isEmpty(clipWithDressTags)) {
//            List<VideoClipDTO> dressClips = getClips(usedClips, clipWithDressTags, 20000, usedClipIdsOfThisWorks);
//            for (VideoClipDTO videoClipDTO : dressClips) {
//                WorksDetailDTO dressWorksDetailDTO = createWorksDetail(projectDTO, videoClipDTO, VideoClipTagEnum.DRESS.getCode());
//                dressDetails.add(dressWorksDetailDTO);
//            }
//        }
//        //创建材质描述
//        if(!CollectionUtils.isEmpty(clipWithMaterialTags)) {
//            List<VideoClipDTO> materialClips = getClips(usedClips, clipWithMaterialTags, 20000, usedClipIdsOfThisWorks);
//            for (VideoClipDTO videoClipDTO : materialClips) {
//                WorksDetailDTO materialWorksDetailDTO = createWorksDetail(projectDTO, videoClipDTO, VideoClipTagEnum.MATERIAL.getCode());
//                materialDetails.add(materialWorksDetailDTO);
//            }
//        }
//        //创建尺码报送
//        if(!CollectionUtils.isEmpty(clipWithSizeTags)) {
//            List<VideoClipDTO> sizeClips = getClips(usedClips, clipWithSizeTags, 5, usedClipIdsOfThisWorks);
//            for (VideoClipDTO videoClipDTO : sizeClips) {
//                WorksDetailDTO sizeWorksDetailDTO = createWorksDetail(projectDTO, videoClipDTO, VideoClipTagEnum.SIZE.getCode());
//                sizeDetails.add(sizeWorksDetailDTO);
//            }
//        }
//        //给视频片段排序
//        int sort = 1;
//        startWorksDetailDTO.setSort(sort);
//        details.add(startWorksDetailDTO);
//        for(WorksDetailDTO worksDetailDTO: dressDetails) {
//            sort++;
//            worksDetailDTO.setSort(sort);
//            details.add(worksDetailDTO);
//        }
//        for(WorksDetailDTO worksDetailDTO: materialDetails) {
//            sort++;
//            worksDetailDTO.setSort(sort);
//            details.add(worksDetailDTO);
//        }
//        for(WorksDetailDTO worksDetailDTO: sizeDetails) {
//            sort++;
//            worksDetailDTO.setSort(sort);
//            details.add(worksDetailDTO);
//        }
//        sort++;
//        endWorksDetailDTO.setSort(sort);
//        details.add(endWorksDetailDTO);
//        worksDTO.setDetails(details);
//        //随机获取背景音乐
//        worksDTO.setMusicDTO(getRandomMusicDTO(curWorks));
//        //随机获取滤镜
//        worksDTO.setFilterId(filterEffectService.getRandomEffectDTO().getId());
//        //随机获取贴纸
//        worksDTO.setStickerId(stickerService.getRandomMiddleStcikerDTO().getId());
//        worksDTO.setSpeedUp((int)(Math.random() * 5 ) + 10);
//        worksDTO.setWorksName("");
//        worksDTO.setWorksDesc("");
//        worksDTO.setSimpleItemName("");
//        worksDTO.setWorksTag("");
//        worksDTO.setItemFeatures("");
//        worksDTO.setItemTags("");
//        worksDTO.setWorksCover("");
//        //随机获取字体
//        worksDTO.setFontId(fontService.getRandomEffectDTO().getId());
//
//        return worksDTO;
//    }
//
//    private WorksDTO createWorks(ProjectDTO projectDTO){
//        UserDTO userDTO = SsoUserContext.getUser();
//        WorksDTO works = new WorksDTO();
//        works.setUpdateTime(new Date());
//        works.setCreateTime(new Date());
//        works.setProjectId(projectDTO.getId());
//        works.setSellerId(projectDTO.getSellerId());
//        works.setItemId(projectDTO.getItemId());
//        works.setDuration(Integer.valueOf(0));
//        works.setCreatorId(userDTO.getId());
//        works.setIsPublished(Boolean.valueOf(false));
//        works.setIsComposed(Boolean.valueOf(false));
//        return works;
//    }
//
//    private VideoClipDTO getStartOrEndCip(Set<Long> usedClipIds, List<VideoClipDTO> clips,Set<Long> usedOfThisWorkIds) {
//        //优先取没有使用过的视频拍片段
//        for(VideoClipDTO clipDTO: clips) {
//            if(!usedClipIds.contains(clipDTO.getId())) {
//                usedClipIds.add(clipDTO.getId());
//                usedOfThisWorkIds.add(clipDTO.getId());
//                return clipDTO;
//            }
//        }
//        //如果没有剩余的没有使用过的视频拍片段，则随机取一个
//        VideoClipDTO clipDTO = clips.get((int)(Math.random() * clips.size()));
//        usedOfThisWorkIds.add(clipDTO.getId());
//        return clipDTO;
//    }
//
//    private List<VideoClipDTO> getClips(Set<Long> usedClipIds, List<VideoClipDTO> clips, Integer maxDuration ,Set<Long> usedOfThisWorkIds) {
//        Set<Long> unUsedClipIds = new HashSet<>();
//        Map<Long, VideoClipDTO> videoClipDTOMap = new HashMap<>();
//        for(VideoClipDTO videoClipDTO: clips) {
//            if(!usedOfThisWorkIds.contains(videoClipDTO.getId())) {
//                unUsedClipIds.add(videoClipDTO.getId());
//            }
//            videoClipDTOMap.put(videoClipDTO.getId(), videoClipDTO);
//        }
//        List<VideoClipDTO> videoClipDTOList = new ArrayList<>();
//        Integer sumDuration = 0;
//        //优先取没有使用过的视频拍片段
//        for(VideoClipDTO clipDTO: clips) {
//            if(!usedClipIds.contains(clipDTO.getId())) {
//                usedClipIds.add(clipDTO.getId());
//                usedOfThisWorkIds.add(clipDTO.getId());
//                videoClipDTOList.add(clipDTO);
//                unUsedClipIds.remove(clipDTO.getId());
//                sumDuration+=clipDTO.getDuration();
//                if(sumDuration >= maxDuration) {
//                    return videoClipDTOList;
//                }
//            }
//        }
//        //如果没有剩余的没有使用过的视频拍片段，则随机取一个
//        while(!unUsedClipIds.isEmpty()) {
//            Long randomId = getRandomClipId(unUsedClipIds);
//            VideoClipDTO clipDTO = videoClipDTOMap.get(randomId);
//            if(!usedOfThisWorkIds.contains(clipDTO.getId())) {
//                usedOfThisWorkIds.add(clipDTO.getId());
//                videoClipDTOList.add(clipDTO);
//                unUsedClipIds.remove(clipDTO.getId());
//                sumDuration+=clipDTO.getDuration();
//                if(sumDuration >= maxDuration) {
//                    break;
//                }
//            }
//        }
//        return videoClipDTOList;
//    }
//
//    public Long getRandomClipId(Set<Long> unUsedClipIds) {
//        Object[] ids = unUsedClipIds.toArray();
//        int index = (int)(ids.length * Math.random());
//        return (Long)ids[index];
//    }
//
//    private void splitVideoClipTags(List<VideoClipDTO> videoClipDTOList, List<VideoClipDTO> clipWithStartTags ,
//                                    List<VideoClipDTO> clipWithEndTags , List<VideoClipDTO> clipWithDressTags,
//                                    List<VideoClipDTO> clipWithMaterialTags, List<VideoClipDTO> clipWithSizeTags) {
//        if(CollectionUtils.isEmpty(videoClipDTOList)) {
//            return;
//        }
//        for(VideoClipDTO videoClipDTO: videoClipDTOList) {
//            List<VideoClipTagDTO> videoClipTagDTOS = videoClipDTO.getTags();
//            if(CollectionUtils.isEmpty(videoClipTagDTOS)) {
//                continue;
//            }
//            for(VideoClipTagDTO tag: videoClipTagDTOS) {
//                if(VideoClipTagEnum.START.getCode().equals(tag.getCode())) {
//                    clipWithStartTags.add(videoClipDTO);
//                }else if(VideoClipTagEnum.END.getCode().equals(tag.getCode())) {
//                    clipWithEndTags.add(videoClipDTO);
//                }else if(VideoClipTagEnum.DRESS.getCode().equals(tag.getCode())){
//                    clipWithDressTags.add(videoClipDTO);
//                }else if(VideoClipTagEnum.MATERIAL.getCode().equals(tag.getCode())) {
//                    clipWithMaterialTags.add(videoClipDTO);
//                }else if(VideoClipTagEnum.SIZE.getCode().equals(tag.getCode())) {
//                    clipWithSizeTags.add(videoClipDTO);
//                }
//            }
//        }
//    }
//
//    private float computeSimilarity(WorksDTO works1, WorksDTO works2){
//        List<WorksDetailDTO> details1 = works1.getDetails();
//        List<WorksDetailDTO> details2 = works2.getDetails();
//        int count = 0;
//        for(WorksDetailDTO detial1: details1) {
//            for(WorksDetailDTO detail2: details2) {
//                if(detial1.getVideoClipId().equals(detail2.getVideoClipId())) {
//                    count++;
//                }
//            }
//        }
//        return count/(float)Math.min(details1.size(), details2.size());
//    }
//
//    private void createWorksDetails(ProjectDTO projectDTO, List<VideoClipDTO> videoClipDTOS, List<WorksDetailDTO> worksDetailDTOS, String tagCode) {
//        for(VideoClipDTO videoClipDTO: videoClipDTOS) {
//            WorksDetailDTO worksDetailDTO = createWorksDetail(projectDTO, videoClipDTO, tagCode);
//            worksDetailDTOS.add(worksDetailDTO);
//        }
//    }
//
//    private WorksDetailDTO createWorksDetail(ProjectDTO projectDTO, VideoClipDTO videoClipDTO, String tagCode) {
//        WorksDetailDTO worksDetailDTO = new WorksDetailDTO();
//        worksDetailDTO.setCreateTime(new Date());
//        worksDetailDTO.setUpdateTime(new Date());
//        worksDetailDTO.setProjectId(projectDTO.getId());
//        worksDetailDTO.setSellerId(projectDTO.getSellerId());
//        worksDetailDTO.setTagCode(tagCode);
//        worksDetailDTO.setDuration(videoClipDTO.getDuration());
//        worksDetailDTO.setVideoClipId(videoClipDTO.getId());
//        worksDetailDTO.setVideoClipDTO(videoClipDTO);
//        worksDetailDTO.setTranCode(getRandowmTransition().getCode());
//        worksDetailDTO.setFlipFlag(Math.random() >= 0.5);
//        worksDetailDTO.setTransId(1);
//        //获取随机裁剪的比例，10-20之间
//        worksDetailDTO.setCutPercent((int)(Math.random()*10) +10);
//        return worksDetailDTO;
//    }
//
//    /**
//     * 随机获取背景音乐
//     * @param curWorksDTOs
//     * @return
//     */
//    private MusicDTO getRandomMusicDTO(List<WorksDTO> curWorksDTOs) {
//        List<MusicDTO> musicDTOS = musicService.getMusics();
//
//        List<Integer> musicIds;
//        if(CollectionUtils.isEmpty(curWorksDTOs)) {
//            musicIds = new ArrayList<>();
//        }else{
//            musicIds = curWorksDTOs.stream().map( worksDTO -> worksDTO.getMusicDTO().getId()).collect(Collectors.toList());
//        }
//        int index = (int)(Math.random()*musicDTOS.size());
//        if(musicIds.size() >= musicDTOS.size()) {
//            return musicDTOS.get(index);
//        }
//        while(musicIds.contains(musicDTOS.get(index).getId())) {
//            index = (int)(Math.random()*musicDTOS.size());
//        }
//        return musicDTOS.get(index);
//    }
//
//    private TransitionEnum getRandowmTransition() {
//        TransitionEnum[] transitionEnums = TransitionEnum.values();
//        int randomIndex= (int) (transitionEnums.length * Math.random());
//        return transitionEnums[randomIndex];
//    }
//}
