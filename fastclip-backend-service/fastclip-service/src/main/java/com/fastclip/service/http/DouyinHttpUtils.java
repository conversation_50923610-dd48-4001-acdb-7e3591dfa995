package com.fastclip.service.http;

import com.alibaba.fastjson.JSONObject;
import com.fastclip.common.model.request.GetDouyinOrdersReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DouyinHttpUtils {

    @Autowired
    private HttpUtils httpUtils;

    @Value("${douyin.apiHost}")
    private String apiHost;

    @Value("${douyin.appAccessToken}")
    private String appToken;

    public String getOrders(GetDouyinOrdersReq req) {
        String URL = apiHost + "/api/v2/order/list";
        //1、构建body参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("start_date", req.getStartDate());
        jsonObject.put("end_date", req.getEndDate());
        jsonObject.put("open_id", req.getOpenId());
        jsonObject.put("access_token", req.getUserAccessToken());
        jsonObject.put("cursor", req.getCursor());

        //2、添加请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Access-Token", appToken);
        if(req.getFrom() != null) {
            headers.add("from", req.getFrom());
        }
        return httpUtils.post(URL, headers, jsonObject);
    }

    public String getQRCode() {
        String URL = apiHost + "/api/v2/qrcode/get";

        //2、添加请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Access-Token", appToken);
        return httpUtils.post(URL, headers, null);
    }

    public String checkQRCode(String qrToken) {
        String URL = apiHost + "/api/v2/qrcode/check";
        //1、构建body参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("token", qrToken);


        //2、添加请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Access-Token", appToken);
        return httpUtils.post(URL, headers, jsonObject);

    }
}
