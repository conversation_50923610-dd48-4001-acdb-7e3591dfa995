package com.fastclip.service.video;

import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.VideoMaterialClipDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.model.request.VideoMaterialClipReq;
import com.fastclip.dao.mapper.ItemOnLiveMapper;
import com.fastclip.dao.mapper.VideoMaterialClipMapper;
import com.fastclip.dao.mapper.VideoMaterialMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.VideoUtils;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class VideoMaterialClipService {
    @Autowired
    VideoMaterialClipMapper videoMaterialClipMapper;

    @Autowired
    ItemOnLiveMapper itemOnLiveMapper;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    LoadingCache<Long, ItemDTO> itemCache;

    public List<VideoMaterialClipDTO> getVideoMaterialClips(VideoMaterialClipReq req) {
        if(ItemTypeEnum.Import.getValue().equals(req.getItemType())) {
            VideoMaterialClipExample videoMaterialClipExample = new VideoMaterialClipExample();
            videoMaterialClipExample.createCriteria()
                    .andVideoIdEqualTo(req.getVideoId())
                    .andItemIdEqualTo(req.getItemId());
            List<VideoMaterialClip> videoMaterialClips = videoMaterialClipMapper.selectByExample(videoMaterialClipExample);
            videoMaterialClipExample.setOrderByClause("start_ts");
            return VideoUtils.do2DTOs(videoMaterialClips);
        }else{
            ItemDTO itemDTO = itemCache.get(req.getItemId());
            VideoMaterialDTO videoMaterialDTO = videoMaterialService.getVideoMaterialById(req.getVideoId());
            VideoMaterialClipDTO videoMaterialClipDTO = new VideoMaterialClipDTO();
            videoMaterialClipDTO.setVideoId(req.getVideoId());
            Date videoStartDate = videoMaterialDTO.getStartDate();
            videoMaterialClipDTO.setVideoDate(videoStartDate);
            Date itemCreateTime = itemDTO.getCreateTime();
            Integer startTs = (int)itemCreateTime.getTime() - (int)videoStartDate.getTime();
            videoMaterialClipDTO.setStartTs(startTs > 0 ? startTs : 0);
            videoMaterialClipDTO.setId(1L);
            videoMaterialClipDTO.setItemId(req.getItemId());
            List<VideoMaterialClipDTO> videoMaterialClipDTOS = new ArrayList<>();
            videoMaterialClipDTOS.add(videoMaterialClipDTO);
            return videoMaterialClipDTOS;
        }
    }
}
