//package com.fastclip.service.rtmp;
//
//import lombok.Data;
//
//@Data
//public class RtmpConnection {
//
//    private String url;
//
//    private Integer roomId;
//
//    private RtmpConnManager rtmpConnManager;
//
//    public RtmpConnection(String url, Integer roomId, RtmpConnManager rtmpConnManager) {
//        this.roomId = roomId;
//        this.url =  url;
//        this.rtmpConnManager = rtmpConnManager;
//    }
//
//    public void close() {
//        rtmpConnManager.closeConnection(this);
//    }
//}
