package com.fastclip.service.user;

import com.alibaba.druid.util.StringUtils;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.common.model.request.UserReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.dao.mapper.FUserMapper;
import com.fastclip.dao.model.dataobject.FUser;
import com.fastclip.dao.model.dataobject.FUserExample;
import com.fastclip.dao.utils.UserUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService {

    @Autowired
    FUserMapper fUserMapper;

    public UserDTO getUserById(Long userId) {
        FUser fUser = fUserMapper.selectByPrimaryKey(userId);
        return UserUtils.do2DTO(fUser);
    }

    public PagebleRes<UserDTO> getUsers(UserReq req) {
        PagebleRes pagebleRes = new PagebleRes();
        FUserExample fUserExample = new FUserExample();
        FUserExample.Criteria criteria = fUserExample.createCriteria();
        if(!StringUtils.isEmpty(req.getUserName())) {
            criteria.andUserNameLike("%" + req.getUserName() + "%");
        }
        if(req.getIsAdmin() != null) {
            criteria.andIsAdminEqualTo(req.getIsAdmin());
        }
        if(req.getIsSelfCutter() != null) {
            criteria.andIsSelfCutterEqualTo(req.getIsSelfCutter());
        }
        RowBounds rowBounds = new RowBounds(0,10);
        List<FUser> fUserList = fUserMapper.selectByExampleWithRowbounds(fUserExample, rowBounds);
        Long count = fUserMapper.countByExample(fUserExample);
        List<UserDTO> userDTOS = UserUtils.do2DTOs(fUserList);
        pagebleRes.setData(userDTOS);
        pagebleRes.setTotal(count.intValue());
        return pagebleRes;
    }


    public UserDTO getCurrentUser() {
        return SsoUserContext.getUser();
    }
}
