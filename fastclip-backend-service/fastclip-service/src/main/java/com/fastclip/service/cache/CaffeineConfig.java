package com.fastclip.service.cache;

import com.fastclip.common.constant.VideoMaterialStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.model.dto.*;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.*;
import com.fastclip.opencv.FFmpegCmd;
import com.fastclip.service.douyin.AccountService;
import com.fastclip.service.douyin.DouyinService;
import com.fastclip.service.video.VideoMaterialService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Configuration
public class CaffeineConfig {

    @Autowired
    VideoMaterialMapper videoMaterialMapper;

    @Autowired
    SellerMapper sellerMapper;

    @Autowired
    ItemMapper itemMapper;

    @Autowired
    FilterEffectMapper filterEffectMapper;

    @Autowired
    StickerMapper stickerMapper;

    @Autowired
    ProjectMapper projectMapper;

    @Autowired
    VideoMaterialSliceMapper videoMaterialSliceMapper;

    @Autowired
    FUserMapper fUserMapper;

    @Autowired
    FontEffectMapper fontEffectMapper;

    @Autowired
    TeamMapper teamMapper;

    @Autowired
    DouyinAccountMapper douyinAccountMapper;

    /**
     * 查询基金缓存
     */
    @Bean("videoMaterialCache")
    public LoadingCache<Long, VideoMaterialDTO> getVideoMaterialCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(1000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(3600, TimeUnit.SECONDS).build(key -> {
                    VideoMaterialDTO videoMaterialDTO = VideoUtils.materialDo2DTO(videoMaterialMapper.selectByPrimaryKey(key));
                    if(videoMaterialDTO ==null) {
                        return null;
                    }
                    if(VideoMaterialTypeEnum.LIVE.getValue().equals(videoMaterialDTO.getVideoType()) &&
                            VideoMaterialStatusEnum.PROCESSING.getValue().equals(videoMaterialDTO.getStatus())) {
                        VideoMaterialSliceExample videoMaterialSliceExample = new VideoMaterialSliceExample();
                        videoMaterialSliceExample.createCriteria().andVideoIdEqualTo(key);
                        videoMaterialSliceExample.setOrderByClause("number");
                        List<VideoMaterialSlice> videoMaterialSlices = videoMaterialSliceMapper.selectByExample(videoMaterialSliceExample);
                        List<VideoMaterialSliceDTO> videoMaterialSliceDTOS = VideoMaterialSliceUtils.do2DTO(videoMaterialSlices);
                        videoMaterialDTO.setVideoMaterialSliceDTOS(videoMaterialSliceDTOS);
                    }
                    return videoMaterialDTO;
                });

    }

    @Bean("sellerCache")
    public LoadingCache<Long, SellerDTO> getSellerCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(1000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(10, TimeUnit.SECONDS).build(key ->
                                SellerUtils.do2DTO(sellerMapper.selectByPrimaryKey(key))
                );

    }

    @Bean("itemCache")
    public LoadingCache<Long, ItemDTO> getItemCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(10000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(10, TimeUnit.SECONDS).build(key ->
                        ItemUtils.do2DTO(itemMapper.selectByPrimaryKey(key))
                );

    }

    @Bean("projectCache")
    public LoadingCache<Long, ProjectDTO> getProjectCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(10000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(20, TimeUnit.SECONDS).build(key ->
                        ProjectUtils.do2DTO(projectMapper.selectByPrimaryKey(key))
                );

    }

    @Bean("filterCache")
    public LoadingCache<Integer, FilterEffect> getFilterCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(10000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(20, TimeUnit.SECONDS).build(key ->
                        filterEffectMapper.selectByPrimaryKey(key)
                );

    }

    @Bean("stickerCache")
    public LoadingCache<Integer, StickerDTO> getStickerCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(10000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(20, TimeUnit.SECONDS).build(key -> StickerUtils.do2DTO(
                        stickerMapper.selectByPrimaryKey(key))
                );

    }

    @Bean({"userCache"})
    public LoadingCache<Long, UserDTO> getUserCache() {
        return Caffeine.newBuilder()

                .initialCapacity(10000)

                .maximumSize(50000)
                .recordStats()

                .refreshAfterWrite(10L, TimeUnit.SECONDS).build(key -> UserUtils.do2DTO(this.fUserMapper.selectByPrimaryKey(key)));
    }

    @Bean("fontEffectCache")
    public LoadingCache<Integer, FontEffectDTO> getFontEffectCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(1000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(20, TimeUnit.SECONDS).build(key ->
                        FontEffectUtils.do2DTO(fontEffectMapper.selectByPrimaryKey(key))
                );

    }

    @Bean("videoTbsCache")
    public LoadingCache<String, Double> getVideoTbsCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(1000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(24*3600, TimeUnit.SECONDS).build(FFmpegCmd::getTBR
                );

    }

    @Bean("videoBitrateCache")
    public LoadingCache<String, Integer> getVideoBitrateCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(1000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(24*3600, TimeUnit.SECONDS).build(FFmpegCmd::getBitrate
                );

    }

    @Bean("teamCache")
    public LoadingCache<Long, TeamDTO> getTeamCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(1000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(10, TimeUnit.SECONDS).build(key ->
                        TeamUtils.do2DTO(teamMapper.selectByPrimaryKey(key))
                );

    }

    @Bean("douyinAccountCache")
    public LoadingCache<String, DouyinAccountDTO> getDouyinAccountCache() {
        return Caffeine.newBuilder()
                //cache的初始容量值
                .initialCapacity(1000)
                //maximumSize用来控制cache的最大缓存数量，maximumSize和maximumWeight(最大权重)不可以同时使用，
                .maximumSize(5000)
                .recordStats()
                //创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(10, TimeUnit.SECONDS).build(key ->  {
                        DouyinAccountExample accountExample = new DouyinAccountExample();
                        accountExample.createCriteria().andCodeEqualTo(key);
                        List<DouyinAccount> accounts = douyinAccountMapper.selectByExample(accountExample);
                        if(CollectionUtils.isEmpty(accounts)) {
                            return null;
                        }
                        return DouyinAccountUtils.do2DTO(accounts.get(0));
                    }
                );
    }
}
