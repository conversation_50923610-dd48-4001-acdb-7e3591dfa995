package com.fastclip.service.workbench;

import com.fastclip.common.model.dataobject.VideoClipDO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.request.VideoClipsReq;
import com.fastclip.dao.mapper.ChanxuanMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.fastclip.common.utils.ChanxuanUtil.clipsDoToDTOs;

@Service
public class WorkbenchService {

    @Autowired
    ChanxuanMapper chanxuanMapper;

    public List<VideoClipDTO> getVideos(VideoClipsReq req) {
        if(req.getPageSize() == null ){
            req.setPageSize(10);
        }
        if(req.getPageNum() == null || req.getPageNum() <=0) {
            req.setPageNum(1);
        }
        req.setStartIndex((req.getPageNum() - 1 )* req.getPageSize());
        req.setEndIndex(req.getPageNum() * req.getPageSize());
        List<VideoClipDO> clipDOS = chanxuanMapper.getClipsByPage(req);
        return clipsDoToDTOs(clipDOS);
    }
}
