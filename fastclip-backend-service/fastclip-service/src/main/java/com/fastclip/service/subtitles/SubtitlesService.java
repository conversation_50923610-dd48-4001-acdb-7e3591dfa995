package com.fastclip.service.subtitles;

import com.fastclip.common.constant.VideoMaterialStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.model.dto.SubtitlesCutDTO;
import com.fastclip.common.model.dto.SubtitlesDTO;
import com.fastclip.common.model.dto.VideoClipDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.SubtitlesCutUtils;
import com.fastclip.dao.utils.SubtitlesUtils;
import com.fastclip.dao.utils.VideoClipUtils;
import com.fastclip.service.video.VideoMaterialService;
import com.fastclip.service.works.WorksService;
import com.sun.corba.se.spi.orbutil.threadpool.Work;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SubtitlesService {

    @Autowired
    SubtitlesCutMapper subtitlesCutMapper;

    @Autowired
    SubtitlesMapper subtitlesMapper;

    @Autowired
    VideoClipDetailsMapper videoClipDetailsMapper;

    @Autowired
    VideoClipMapper videoClipMapper;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    WorksDetailMapper worksDetailMapper;

    @Autowired
    WorksMapper worksMapper;

    @Autowired
    WorksService worksService;

    /**
     * 从项目中删除字幕片段
     * @param req
     * @return
     */
    @Transactional
    public Boolean removeSubtitlesClipsFromProject(RemoveSubtitlesReq req) {
        Long videoClipId = req.getVideoClipId();
        if(req.getSubtitlesId() != null) {
            SubtitlesCutExample  subtitlesCutExample = new SubtitlesCutExample();
            subtitlesCutExample.createCriteria().andProjectIdEqualTo(req.getProjectId())
                    .andSubtitlesIdEqualTo(req.getSubtitlesId());
            List<SubtitlesCut> subtitlesCuts = subtitlesCutMapper.selectByExample(subtitlesCutExample);
            if(CollectionUtils.isEmpty(subtitlesCuts)) {
                return false;
            }
            subtitlesCutMapper.deleteByExample(subtitlesCutExample);
            VideoClipDetailsExample videoClipDetailsExample = new VideoClipDetailsExample();
            videoClipDetailsExample.createCriteria().andSubtitlesCutIdEqualTo(subtitlesCuts.get(0).getId());
            List<VideoClipDetails> videoClipDetails = videoClipDetailsMapper.selectByExample(videoClipDetailsExample);
            videoClipId = videoClipDetails.get(0).getVideoClipId();
        }else if(req.getVideoClipId() != null) {
            VideoClipDetailsExample videoClipDetailsExample = new VideoClipDetailsExample();
            videoClipDetailsExample.createCriteria().andVideoClipIdEqualTo(videoClipId);
            List<VideoClipDetails> videoClipDetails = videoClipDetailsMapper.selectByExample(videoClipDetailsExample);
            videoClipDetailsMapper.deleteByExample(videoClipDetailsExample);
            subtitlesCutMapper.deleteByPrimaryKey(videoClipDetails.get(0).getSubtitlesCutId());
            worksService.deleteWorksByClipId(req.getVideoClipId());
        }
        videoClipMapper.deleteByPrimaryKey(videoClipId);
        return true;
    }

    public List<Long> getWorksIdOfSubtitlesClip(RemoveSubtitlesReq req) {
        WorksDetailExample worksDetailExample = new WorksDetailExample();
        if(req.getVideoClipId() == null && CollectionUtils.isEmpty(req.getVideoClipIds())) {
            return new ArrayList<>();
        }
        if(req.getVideoClipId() != null) {
            worksDetailExample.createCriteria().andVideoClipIdEqualTo(req.getVideoClipId());
        }
        if(!CollectionUtils.isEmpty(req.getVideoClipIds())) {
            worksDetailExample.createCriteria().andVideoClipIdIn(req.getVideoClipIds());
        }
        List<WorksDetail> worksDetails = worksDetailMapper.selectByExample(worksDetailExample);
        if(CollectionUtils.isEmpty(worksDetails)) {
            return new ArrayList<>();
        }
        List<Long> result = new ArrayList<>();
        for(WorksDetail worksDetail: worksDetails) {
            if(!result.contains(worksDetail.getWorksId())) {
                result.add(worksDetail.getWorksId());
            }
        }
        return result;
    }

    /**
     * 获取字幕剪辑片段
     * @param req
     * @return
     */
    public List<SubtitlesCutDTO> getSubtitlesCut(SearchSubtitlesCutReq req) {
        SubtitlesCutExample subtitlesCutExample =  new SubtitlesCutExample();
        SubtitlesCutExample.Criteria criteria = subtitlesCutExample.createCriteria();
        if(req.getProjectId() != null) {
            criteria.andProjectIdEqualTo(req.getProjectId());
        }
        if(!CollectionUtils.isEmpty(req.getIds())) {
            criteria.andSubtitlesIdIn(req.getIds());
        }
        subtitlesCutExample.setOrderByClause("cut_start_ts");
        List<SubtitlesCut> subtitlesCuts = subtitlesCutMapper.selectByExample(subtitlesCutExample);
        return SubtitlesCutUtils.do2DTO(subtitlesCuts);
    }

    /**
     * 获取字幕剪辑片段
     * @param id
     * @return
     */
    public SubtitlesDTO getSubtitlesById(Long id) {
        SubtitlesExample subtitlesExample =  new SubtitlesExample();
        subtitlesExample.createCriteria().andIdEqualTo(id);
        Subtitles subtitles = subtitlesMapper.selectByPrimaryKey(id);
        return SubtitlesUtils.do2DTO(subtitles);
    }

    public List<SubtitlesCutDTO> searchSubtitlesCutByVideoClipId(Long videoClipId) {
        VideoClipDetailsExample videoClipDetailsExample =new VideoClipDetailsExample();
        videoClipDetailsExample.createCriteria().andVideoClipIdEqualTo(videoClipId);
        videoClipDetailsExample.setOrderByClause("sort");
        List<VideoClipDetails> videoClipDetails = videoClipDetailsMapper.selectByExample(videoClipDetailsExample);
        if(CollectionUtils.isEmpty(videoClipDetails)) {
            return new ArrayList<>();
        }
        List<Long> subtitlesCutIds = videoClipDetails.stream().map(VideoClipDetails::getSubtitlesCutId).collect(Collectors.toList());
        SubtitlesCutExample subtitlesCutExample = new SubtitlesCutExample();
        subtitlesCutExample.createCriteria().andIdIn(subtitlesCutIds);
        List<SubtitlesCut> subtitlesCuts = subtitlesCutMapper.selectByExample(subtitlesCutExample);
        Map<Long, SubtitlesCut> cutMap = new HashMap<>();
        subtitlesCuts.forEach(item -> cutMap.put(item.getId(), item));
        List<SubtitlesCutDTO> subtitlesCutDTOs = new ArrayList<>();
        for(Long subtitlesCutId: subtitlesCutIds) {
            subtitlesCutDTOs.add(SubtitlesCutUtils.do2DTO(cutMap.get(subtitlesCutId)));
        }
        return subtitlesCutDTOs;
    }


    public List<SubtitlesDTO> searchSubtitles(SearchSubtitlesReq req) {
        VideoMaterialDTO videoMaterialDTO = videoMaterialService.getVideoMaterialById(req.getVideoId());
        SubtitlesExample subtitlesExample = new SubtitlesExample();

        int startTs;
        int endTs;
        if(VideoMaterialTypeEnum.LIVE.getValue().equals(videoMaterialDTO.getVideoType()) &&
                VideoMaterialStatusEnum.PROCESSING.getValue().equals(videoMaterialDTO.getStatus())) {
            startTs = req.getStartTs();
            endTs = req.getEndTs();
        }else {
            startTs = req.getStartTs() - videoMaterialDTO.getStartTime();
            endTs = req.getEndTs() - videoMaterialDTO.getStartTime();
        }
        if(startTs >= endTs) {
            return new ArrayList<>();
        }
        subtitlesExample.createCriteria().andVideoIdEqualTo(req.getVideoId())
                .andStartTsBetween(startTs, endTs);
        List<Subtitles> subtitles = subtitlesMapper.selectByExample(subtitlesExample);
        List<Long> subtitlesIds = subtitles.stream().map(Subtitles::getId).collect(Collectors.toList());
        SearchSubtitlesCutReq searchSubtitlesCutReq = new SearchSubtitlesCutReq();
        searchSubtitlesCutReq.setIds(subtitlesIds);
        searchSubtitlesCutReq.setProjectId(req.getProjectId());
        List<SubtitlesCutDTO> subtitlesCuts = getSubtitlesCut(searchSubtitlesCutReq);
        Set<Long> addedSubtitlesIds = subtitlesCuts.stream().map(SubtitlesCutDTO::getSubtitlesId).collect(Collectors.toSet());
        List<SubtitlesDTO> subtitlesDTOS= SubtitlesUtils.do2DTO(subtitles);
        subtitlesDTOS.forEach( item -> {
            item.setIsAdded(addedSubtitlesIds.contains(item.getId()));
            item.setStartTs(item.getStartTs() + videoMaterialDTO.getStartTime());
            item.setEndTs(item.getEndTs() + videoMaterialDTO.getStartTime());
        });
        return subtitlesDTOS;
    }

    public Boolean updateSubtitlesContent(SubtitlesDTO subtitlesDTO)  {
        Subtitles subtitles = new Subtitles();
        subtitles.setId(subtitlesDTO.getId());
        subtitles.setContent(subtitlesDTO.getContent());
        int res = subtitlesMapper.updateByPrimaryKeySelective(subtitles);
        return res > 0;
    }
}
