//package com.fastclip.service.chanxuan;
//
//import com.alibaba.druid.util.StringUtils;
//import com.fastclip.common.model.ao.VideoClipAO;
//import com.fastclip.common.model.ao.ChanxuanItemAO;
//import com.fastclip.common.model.dataobject.VideoClipDO;
//import com.fastclip.common.model.dataobject.ChanxuanItemDO;
//import com.fastclip.common.model.request.ChanxuanItemRequest;
//import com.fastclip.common.utils.ChanxuanUtil;
//import com.fastclip.common.utils.FileUtils;
//import com.fastclip.dao.conn.ChanxuanConnection;
//import com.fastclip.dao.mapper.ChanxuanMapper;
//import com.fastclip.service.FfmpegService;
//import QwenService;
//import com.fastclip.service.VideoDownloader;
//import com.fastclip.service.WhisperService;
//import com.fastclip.service.cut.AutoCutService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Service
//public class ChanxuanFindClipsService {
//
//    @Autowired
//    ChanxuanMapper chanxuanMapper;
//
//    @Autowired
//    VideoDownloader downloader;
//
//    @Autowired
//    FfmpegService ffmpegService;
//
//    @Autowired
//    WhisperService whisperService;
//
//    @Autowired
//    QwenService qwenService;
//
//    @Autowired
//    AutoCutService autoCutService;
//
//    @Value("${chanxuan.localPath}")
//    String localPath;
//
//    @Value("${chanxuan.filter_item_name}")
//    String filterItemName;
//
//
//    @Autowired
//    ChanxuanConnection chanxuanConnection;
//
//    public void getClipsFromChanxuan(){
//        ChanxuanItemRequest request = new ChanxuanItemRequest();
//        request.setPage(1);
//        request.setPreSellType(0);
//        request.setPageSize(10);
//        int i = 1;
//        while(true) {
//
//            List<ChanxuanItemAO> list = chanxuanConnection.getItemData(request);
//            if(CollectionUtils.isEmpty(list)) {
//                break;
//            }
//            for(ChanxuanItemAO item: list) {
//                if(item.getProduct_name().contains(filterItemName)) {
//                    continue;
//                }
//                int count = 0;
//                List<VideoClipAO> clipAOs = chanxuanConnection.getClipDataByProductV2(item.getMaterialProductId(), 1, 20);
//                for(VideoClipAO clip: clipAOs) {
//                    if(hasClipDownload(clip) || clip.getDuration() > 300 || clip.getDuration() < 60) {
//                        continue;
//                    }
//                    String clipPath = localPath + "/" + clip.getTask_id() + ".mp4";
//                    String videoUrl = chanxuanConnection.confirmDownload(clip.getTask_id());
//                    if(StringUtils.isEmpty(videoUrl)) {
//                        continue;
//                    }
//                    downloader.downloadVideo(videoUrl, clipPath);
//                    clip.setLocalVideoPath(clipPath);
//                    clip.setProduct_id(item.getMaterialProductId());
//                    if(FileUtils.exist(clipPath)) {
//                        count++;
//                        this.insertClip(clip);
//                    }
//                    int countOfClips = chanxuanMapper.getCountClipsByProductId(item.getMaterialProductId());
//                    if(countOfClips >= 5) {
//                        break;
//                    }
//                }
//                int countOfClips = chanxuanMapper.getCountClipsByProductId(item.getMaterialProductId());
//                if(countOfClips > 0) {
//                    insertItem(item);
//                }
//            }
//            i++;
//            request.setPage(i);
//        }
//    }
//
//
//    public void getLatestClips(String authorId){
//        ChanxuanItemRequest request = new ChanxuanItemRequest();
//        request.setPage(1);
//        request.setPreSellType(0);
//        request.setPageSize(5);
//        request.setAuthorId(authorId);
//        int i = 1;
//        int countOfItems = 0;
//        while(countOfItems<=5) {
//            List<ChanxuanItemAO> list = chanxuanConnection.getItemData(request);
//            if(CollectionUtils.isEmpty(list)) {
//                break;
//            }
//            for(ChanxuanItemAO item: list) {
//                if(item.getProduct_name().contains(filterItemName) || hasItemDownload(item)) {
//                    continue;
//                }
//
//                List<VideoClipAO> clipAOs = chanxuanConnection.getClipDataByProductV2(item.getMaterialProductId(), 1, 20);
//                List<VideoClipAO> clipAOsToGet = new ArrayList<>();
//
//                for(VideoClipAO clip: clipAOs) {
//                    if(hasClipDownload(clip) || clip.getDuration() > 300 || clip.getDuration() < 120) {
//                        continue;
//                    }
//                    clipAOsToGet.add(clip);
//                    if(clipAOsToGet.size() >= 1) {
//                        break;
//                    }
//                }
//                for(VideoClipAO clip: clipAOsToGet){
//                    String clipPath = localPath + "/" + clip.getTask_id() + ".mp4";
//                    String videoUrl = chanxuanConnection.confirmDownload(clip.getTask_id());
//                    if(StringUtils.isEmpty(videoUrl)) {
//                        continue;
//                    }
//                    downloader.downloadVideo(videoUrl, clipPath);
//                    clip.setLocalVideoPath(clipPath);
//                    clip.setProduct_id(item.getMaterialProductId());
//                    if(FileUtils.exist(clipPath)) {
//                        this.insertClip(clip);
//                    }
//                    int countOfClips = chanxuanMapper.getCountClipsByProductId(item.getMaterialProductId());
//                    if(countOfClips >= 4) {
//                        break;
//                    }
//                }
//                int countOfClips = chanxuanMapper.getCountClipsByProductId(item.getMaterialProductId());
//                if(countOfClips > 0) {
//                    insertItem(item);
//                    countOfItems ++;
//                }
//            }
//            i++;
//            request.setPage(i);
//        }
//    }
//
//
//    public void getClipsByProductId(List<String> productIds) {
//        ChanxuanItemRequest request = new ChanxuanItemRequest();
//        request.setPage(1);
//        request.setPreSellType(0);
//        request.setPageSize(10);
//        int i = 1;
//        for (String productId : productIds) {
//            List<VideoClipAO> clipAOs = chanxuanConnection.getClipDataByProductV1(productId, 1, 20);
//            if(CollectionUtils.isEmpty(clipAOs)) {
//                continue;
//            }
//            VideoClipAO tmpClip = clipAOs.get(0);
//
//            for (VideoClipAO clip : clipAOs) {
//                if(tmpClip.getDuration() < clip.getDuration()) {
//                    tmpClip = clip;
//                }
//            }
//            String filePath = localPath + "/" + tmpClip.getTask_id();
//            String clipPath = filePath + ".mp4";
//            downloader.downloadVideo(chanxuanConnection.confirmDownload(tmpClip.getTask_id()), clipPath);
//            String audioPath = filePath + ".wav";
//            String srtPath = audioPath + ".srt";
//            String localCutVideoPath =  filePath + "_cut_final.mp4";
//            String hflipPath = filePath + "_hflip.mp4";
//            String cutVideoWithSrtPath = filePath + "_with_srt.mp4";
//            String cutSRTFilePath = filePath + "_cut.srt";
////
//            ffmpegService.acodec(clipPath, audioPath);
//            whisperService.fetchSrt(audioPath);
//            String answer = qwenService.askWithDefaultSystemQ(srtPath);
//            if(!autoCutService.writeSrtToFile(answer, cutSRTFilePath)) {
//                return;
//            }
//            ffmpegService.hflip(clipPath, hflipPath);
//            ffmpegService.insertSrt(hflipPath, srtPath, cutVideoWithSrtPath);
//            ffmpegService.cutAndMerageByPeriodList(cutSRTFilePath,cutVideoWithSrtPath,localCutVideoPath);
//        }
//    }
//
//    public void autoCutByVidePath(String videoFileName) {
//
//        String filePath = localPath + "/" + videoFileName;
//        String clipPath = filePath + ".mp4";
//        String audioPath = filePath + ".wav";
//        String srtPath = audioPath + ".srt";
//        String localCutVideoPath =  filePath + "_cut_final.mp4";
//        String hflipPath = filePath + "_hflip.mp4";
//        String cutVideoWithSrtPath = filePath + "_with_srt.mp4";
//        String cutSRTFilePath = filePath + "_cut.srt";
////
//        ffmpegService.acodec(clipPath, audioPath);
//        whisperService.fetchSrt(audioPath);
//        String systemQ = "你是一个直播视频剪辑师，";
//        String answer = qwenService.askSrtSummary(systemQ, srtPath);
//        if(!autoCutService.writeSrtToFile(answer, cutSRTFilePath)) {
//            return;
//        }
//        ffmpegService.hflip(clipPath, hflipPath);
//        ffmpegService.insertSrt(hflipPath, srtPath, cutVideoWithSrtPath);
//        ffmpegService.cutAndMerageByPeriodList(cutSRTFilePath,cutVideoWithSrtPath,localCutVideoPath);
//
//    }
//
//
//
//    private void insertItem(ChanxuanItemAO item) {
//        ChanxuanItemDO itemDO = chanxuanMapper.getItem(item.getProduct_id());
//        if(itemDO != null) {
//            return;
//        }
//        List<ChanxuanItemDO> itemList = new ArrayList<>();
//        itemList.add(ChanxuanUtil.itemAoToDO(item));
//        chanxuanMapper.insertOrUpdateCxItem(itemList);
//    }
//
//    private boolean hasClipDownload(VideoClipAO clipAO) {
//        VideoClipDO clipDO = chanxuanMapper.getClipByTaskId(clipAO.getTask_id());
//        if(clipDO == null || clipDO.getLocalVideoPath() == null || !FileUtils.exist(clipDO.getLocalVideoPath())) {
//            return false;
//        }
//        return true;
//    }
//
//    private boolean hasItemDownload(ChanxuanItemAO itemAO) {
//        ChanxuanItemDO item = chanxuanMapper.getItem(itemAO.getProduct_id());
//        if(item == null) {
//            return false;
//        }
//        return true;
//    }
//
//    private void insertClip(VideoClipAO clipAO) {
//        List<VideoClipDO> clipDOS = new ArrayList<>();
//        clipDOS.add(ChanxuanUtil.clipAoToDO(clipAO));
//        chanxuanMapper.insertOrUpdateCxClip(clipDOS);
//    }
//
//
//
//    public void cleanClips(){
//        List<VideoClipDO> clipDOS = chanxuanMapper.getAllClips();
//        for(VideoClipDO clipDO: clipDOS) {
//            String videoPath = clipDO.getLocalVideoPath();
//            if(!FileUtils.exist(videoPath)) {
//                chanxuanMapper.deleteClip(clipDO.getId());
//            }
//        }
//    }
//
//    public void cleanItems(){
//        List<ChanxuanItemDO> itemDOS = chanxuanMapper.getAllItems();
//        for(ChanxuanItemDO itemDO: itemDOS) {
//            List<VideoClipDO> clipDOS = chanxuanMapper.getClipsByProductId(itemDO.getMaterialProductId());
//            if(CollectionUtils.isEmpty(clipDOS)) {
//                chanxuanMapper.deleteItem(itemDO.getId());
//            }
//        }
//    }
//}
