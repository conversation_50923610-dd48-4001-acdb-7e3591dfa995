package com.fastclip.service.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.ItemSearchResultDTO;
import com.fastclip.common.model.dto.SearchItemsByFrameResponseDTO;
import com.fastclip.dao.mapper.ItemMapper;
import com.fastclip.dao.model.dataobject.Item;
import com.fastclip.dao.model.dataobject.ItemExample;
import com.fastclip.dao.utils.ItemUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GroundingHttpUtils {

    @Autowired
    private HttpUtils httpUtils;

    @Autowired
    private ItemMapper itemMapper;

    @Value("${grounding.host}")
    private String groundingHost;

    @Value("${grounding.port}")
    private String groundingPort;

    @Value("${grounding.basePath}")
    private String basePath;

    @Value("${grounding.timeout:30000}")
    private Integer timeout;

    /**
     * Call Python backend searchItemsByFrame API
     * @param picContent Base64 encoded image content
     * @param itemType Item type filter (optional)
     * @param sellerId Seller ID filter (optional)
     * @return Structured response with enriched item information
     */
    public SearchItemsByFrameResponseDTO searchItemsByFrame(String picContent, Integer itemType, Integer sellerId) {
        String url = buildUrl("/searchItemsByFrame");

        JSONObject requestBody = new JSONObject();
        requestBody.put("picContent", picContent);
        if (itemType != null) {
            requestBody.put("itemType", itemType);
        }
        if (sellerId != null) {
            requestBody.put("sellerId", sellerId);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");

        log.info("Calling Python backend searchItemsByFrame API: {}", url);
        String response = httpUtils.post(url, headers, requestBody);

        // Parse the JSON response and create structured response
        SearchItemsByFrameResponseDTO responseDTO = parseJsonResponse(response);

        // Enrich the response with item names from database
        return enrichResponseWithItemNames(responseDTO);
    }

    /**
     * Parse JSON response from Python backend into structured DTO
     * @param jsonResponse Raw JSON response string
     * @return Structured response DTO with basic fields populated
     */
    private SearchItemsByFrameResponseDTO parseJsonResponse(String jsonResponse) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(jsonResponse);
            SearchItemsByFrameResponseDTO responseDTO = new SearchItemsByFrameResponseDTO();

            // Extract status information if available
            if (jsonObject.containsKey("statusCode")) {
                responseDTO.setStatusCode(jsonObject.getInteger("statusCode"));
            }
            if (jsonObject.containsKey("statusMessage")) {
                responseDTO.setStatusMessage(jsonObject.getString("statusMessage"));
            }

            // Parse result array
            if (jsonObject.containsKey("result") && jsonObject.get("result") instanceof JSONArray) {
                JSONArray resultArray = jsonObject.getJSONArray("result");
                List<ItemSearchResultDTO> results = new ArrayList<>();

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject item = resultArray.getJSONObject(i);
                    ItemSearchResultDTO resultItem = new ItemSearchResultDTO();

                    // Extract basic fields from Python backend
                    if (item.containsKey("itemId")) {
                        resultItem.setItemId(item.getString("itemId"));
                    }
                    if (item.containsKey("score")) {
                        resultItem.setScore(item.getDouble("score"));
                    }
                    if (item.containsKey("sort")) {
                        resultItem.setSort(item.getInteger("sort"));
                    }

                    results.add(resultItem);
                }

                responseDTO.setResult(results);
            }

            return responseDTO;
        } catch (Exception e) {
            log.error("Error parsing JSON response: {}", jsonResponse, e);
            // Return empty response on error
            return new SearchItemsByFrameResponseDTO();
        }
    }

    /**
     * Enrich the API response with item names from database
     * @param responseDTO Original API response DTO
     * @return Enriched response DTO with item names
     */
    private SearchItemsByFrameResponseDTO enrichResponseWithItemNames(SearchItemsByFrameResponseDTO responseDTO) {
        try {
            // Check if response has results to enrich
            if (responseDTO.hasResults()) {
                List<ItemSearchResultDTO> enrichedResults = new ArrayList<>();

                // Process each search result item
                for (ItemSearchResultDTO resultItem : responseDTO.getResult()) {
                    if (resultItem.getItemId() != null) {
                        String itemIdStr = resultItem.getItemId();

                        try {
                            // itemId from API is actually the database primary key ID
                            Long itemId = Long.parseLong(itemIdStr);

                            // Find item in database by primary key ID
                            Item dbItem = itemMapper.selectByPrimaryKey(itemId);

                            // Add item information to result if found
                            if (dbItem != null) {
                                resultItem.setItemName(dbItem.getItemName());
                                resultItem.setDatabaseId(dbItem.getId());
                                resultItem.setOutItemId(dbItem.getOutItemId());
                                resultItem.setItemType(dbItem.getItemType());
                            } else {
                                resultItem.setItemName("未知商品");
                                log.warn("Item not found in database with ID: {}", itemId);
                            }
                        } catch (NumberFormatException e) {
                            log.error("Invalid itemId format: {}", itemIdStr, e);
                            resultItem.setItemName("未知商品");
                        }
                    }
                    enrichedResults.add(resultItem);
                }

                // Update the response with enriched results
                responseDTO.setResult(enrichedResults);
                responseDTO.setEnriched(true);
            }

            return responseDTO;
        } catch (Exception e) {
            log.error("Error enriching response with item names", e);
            return responseDTO; // Return original response in case of error
        }
    }

    /**
     * Batch search items by frames with concurrent processing
     * @param frameDataList List of frame data with timestamps
     * @param itemType Item type filter (optional)
     * @param sellerId Seller ID filter (optional)
     * @return Batch processing results
     */
    public CompletableFuture<String> searchItemsByFramesBatch(List<FrameData> frameDataList, Integer itemType, Integer sellerId) {
        return CompletableFuture.supplyAsync(() -> {
            StringBuilder results = new StringBuilder();
            ExecutorService executor = Executors.newFixedThreadPool(5); // Limit concurrent requests

            try {
                List<CompletableFuture<String>> futures = frameDataList.stream()
                    .map(frameData -> CompletableFuture.supplyAsync(() -> {
                        try {
                            SearchItemsByFrameResponseDTO result = searchItemsByFrame(frameData.base64Data, itemType, sellerId);
                            return String.format("Timestamp: %dms, Result: %s", frameData.timestamp, JSON.toJSONString(result));
                        } catch (Exception e) {
                            log.error("Error processing frame at {}ms", frameData.timestamp, e);
                            return String.format("Timestamp: %dms, Error: %s", frameData.timestamp, e.getMessage());
                        }
                    }, executor))
                    .collect(Collectors.toList());

                // Wait for all requests to complete
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                // Collect results
                for (CompletableFuture<String> future : futures) {
                    results.append(future.get()).append("\n");
                }

            } catch (Exception e) {
                log.error("Error in batch processing", e);
                return "Batch processing failed: " + e.getMessage();
            } finally {
                executor.shutdown();
            }

            return results.toString();
        });
    }

    /**
     * Data class for frame information
     */
    public static class FrameData {
        public final long timestamp;
        public final String base64Data;

        public FrameData(long timestamp, String base64Data) {
            this.timestamp = timestamp;
            this.base64Data = base64Data;
        }
    }

    /**
     * Build full URL for Python backend API
     * @param endpoint API endpoint
     * @return Full URL
     */
    private String buildUrl(String endpoint) {
        return String.format("http://%s:%s%s%s", groundingHost, groundingPort, basePath, endpoint);
    }
}
