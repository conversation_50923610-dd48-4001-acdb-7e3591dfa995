package com.fastclip.service.sticker;

import com.fastclip.common.model.dto.StickerDTO;
import com.fastclip.dao.mapper.StickerMapper;
import com.fastclip.dao.model.dataobject.Sticker;
import com.fastclip.dao.model.dataobject.StickerExample;
import com.fastclip.dao.utils.StickerUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StickerService {

    @Autowired
    StickerMapper stickerMapper;

    public List<StickerDTO> getMiddleStickerDTOs() {
        StickerExample stickerExample = new StickerExample();
        stickerExample.createCriteria().andTypeEqualTo(1);
        List<Sticker> stickers = stickerMapper.selectByExample(stickerExample);
        return StickerUtils.do2DTOs(stickers);
    }

    public List<StickerDTO> getTopOrBottomStickerDTOs() {
        StickerExample stickerExample = new StickerExample();
        stickerExample.createCriteria().andTypeEqualTo(2);
        List<Sticker> stickers = stickerMapper.selectByExample(stickerExample);
        return StickerUtils.do2DTOs(stickers);
    }

    public StickerDTO getRandomMiddleStcikerDTO() {
        List<StickerDTO> stickerDTOS = getMiddleStickerDTOs();
        int index = (int) (Math.random() * stickerDTOS.size());
        return stickerDTOS.get(index);
    }

    public StickerDTO getRandomTopOrBottomStcikerDTO() {
        List<StickerDTO> stickerDTOS = getTopOrBottomStickerDTOs();
        int index = (int) (Math.random() * stickerDTOS.size());
        return stickerDTOS.get(index);
    }
}
