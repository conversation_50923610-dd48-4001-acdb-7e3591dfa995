package com.fastclip.service.exception;

/**
 * Custom exception for video processing errors
 */
public class VideoProcessingException extends RuntimeException {
    
    private final String taskId;
    
    public VideoProcessingException(String message) {
        super(message);
        this.taskId = null;
    }
    
    public VideoProcessingException(String message, String taskId) {
        super(message);
        this.taskId = taskId;
    }
    
    public VideoProcessingException(String message, Throwable cause) {
        super(message, cause);
        this.taskId = null;
    }
    
    public VideoProcessingException(String message, String taskId, Throwable cause) {
        super(message, cause);
        this.taskId = taskId;
    }
    
    public String getTaskId() {
        return taskId;
    }
}
