package com.fastclip.service;


import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class VideoParser {
    public String parseVideoUrl(String url) throws IOException {

            Document doc = Jsoup.connect(url).
                    ignoreContentType(true).get();
            Elements videoElements = doc.select("video");
            Element videoElement = videoElements.first();
            String videoUrl = videoElement.attr("src");
            return videoUrl;

    }
}
