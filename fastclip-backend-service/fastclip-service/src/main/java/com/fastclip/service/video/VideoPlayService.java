package com.fastclip.service.video;

import com.alibaba.fastjson.JSON;
import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.request.VideoPlayReq;
import com.fastclip.common.model.response.VideoPlayRes;
import com.fastclip.opencv.grabber.FFmpegUtils;
import com.fastclip.opencv.grabber.Grabber;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import javax.websocket.Session;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Base64;

@Service
public class VideoPlayService {

    @Autowired
    GrabberFactory grabberFactory;

    public void playVideo(Session session, VideoPlayReq req) {
        try {
            Grabber imageGrabber = grabberFactory.createGrabber(req);
            req.setVideoOrAudio("audio");
            Grabber audioGrabber = grabberFactory.createGrabber(req);
            imageGrabber.start();
            audioGrabber.start();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputStream, 1);
            recorder.setFormat("mp3");
            recorder.setSampleRate(16000);
            recorder.setAudioChannels(audioGrabber.getChannels());
            recorder.setAudioBitrate(audioGrabber.getAudioBitrate());
            recorder.setAudioOption("crf", "0");
            recorder.start();
            double frameRate = imageGrabber.getFrameRate();
            int frameDuration = (int)(1000000/frameRate);
            Frame imageFrame = imageGrabber.grabImage();
            Frame audioFrame = audioGrabber.grabSamples();
            recorder.record(audioFrame);
            long startTime = System.currentTimeMillis();
            long firstFrameTime = imageFrame.timestamp;
            byte[] sampleBytes = null;
            while(imageFrame != null) {
                VideoPlayRes videoPlayRes = createVideoPlayRes(imageFrame, audioFrame, req, imageGrabber.getBaseTime());
                if(outputStream.size() > 0) {
                    sampleBytes = outputStream.toByteArray();
                    outputStream.reset();
                    String base64Data = Base64.getEncoder().encodeToString(sampleBytes);
                    videoPlayRes.setSampleData(base64Data);
                }
                if(session.isOpen()) {
                    if(videoPlayRes.getImageData()!=null) {
                        session.getBasicRemote().sendText(JSON.toJSONString(videoPlayRes));
                    }
                }else {
                    return;
                }
                long curTime = System.currentTimeMillis();

                imageFrame = getNextImageFrame(imageGrabber, firstFrameTime, (curTime - startTime)*1000, frameDuration);
                if(imageFrame.timestamp >= audioFrame.timestamp) {
                    while (audioFrame.timestamp < imageFrame.timestamp + 2000000L) {
                        audioFrame = audioGrabber.grabSamples();
                        recorder.record(audioFrame);
                    }
                }
            }
            imageGrabber.stop();
            audioGrabber.stop();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private Frame getNextImageFrame(Grabber grabber, Long firstFrameTime, Long gap, Integer frameDuration) {
        Frame frame = grabber.grabImage();
        //当前后两帧的时间差小于已经过去的时间，需要睡眠等待
        if(frame.timestamp - firstFrameTime > gap) {
            try{
                Thread.sleep((frame.timestamp - firstFrameTime  - gap)/1000);
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        //否则，需要跳过当前帧，直到当前帧和第一帧的时间差，与已经过去的时间gap不超过一个帧的时长即可
        else {
            while (((frame.timestamp - firstFrameTime) - gap) < frameDuration) {
                frame = grabber.grabImage();
            }
        }
        return frame;
    }

    private VideoPlayRes createVideoPlayRes(Frame videoFrame, Frame audioFrame, VideoPlayReq req, Integer baseTime) {
        VideoPlayRes videoPlayRes =  new VideoPlayRes();
//        if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(req.getVideoOrAudio())) {
        videoPlayRes.setType(1);
        videoPlayRes.setImageWidth(480);
        videoPlayRes.setImageHeight((videoFrame.imageHeight * 480)/videoFrame.imageWidth );
        videoPlayRes.setImageData(encodeImageFrame(videoFrame));
        videoPlayRes.setVideoTimestamp(videoFrame.timestamp + baseTime * 1000L);
        videoPlayRes.setAudioTimestamp(audioFrame.timestamp + baseTime * 1000L);
//        }
//        else{
//            videoPlayRes.setType(2);
//            byte[] bytes = FFmpegUtils.getAudios(frame);
//            String base64Data = Base64.getEncoder().encodeToString(bytes);
//            videoPlayRes.setImmage(base64Data);
//            videoPlayRes.setTimestamp(frame.timestamp);
//        }
        return videoPlayRes;
    }

    public void playAudio(Session session, VideoPlayReq req) {
        try {
            long startTime = System.currentTimeMillis();

            Grabber grabber = grabberFactory.createGrabber(req);
            grabber.start();
            Frame frame = grabber.grabSamples();
            int countOfFrame = 0;
            VideoPlayRes videoPlayRes = new VideoPlayRes();
            videoPlayRes.setType(2);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputStream, 1);
            recorder.setFormat("mp3");
            recorder.start();
            int countOfTime = 1000000/frame.sampleRate;
            while(frame != null) {
                countOfFrame++;
                if(countOfFrame<=10) {
                    recorder.record(frame);
                    frame = grabber.grabSamples();
                    countOfTime+=1000000/frame.sampleRate;
                    continue;
                }
                countOfFrame = 0;
                byte[] bytes = outputStream.toByteArray();
                outputStream.reset();
                String base64Data = Base64.getEncoder().encodeToString(bytes);
                videoPlayRes.setVideoTimestamp(frame.timestamp);
                videoPlayRes.setSampleData(base64Data);
                if(session.isOpen()) {
                    if(videoPlayRes.getSampleData()!=null) {
                        session.getBasicRemote().sendText(JSON.toJSONString(videoPlayRes));
                    }
                }else {
                    return;
                }
                long curTime = System.currentTimeMillis();
                long sleepTime = countOfTime - (curTime - startTime);
                if(sleepTime > 0) {
                    try {
                        Thread.sleep(sleepTime);
                    }catch (Exception e) {

                    }
                }
                countOfTime = 0;
                frame = grabber.grabSamples();
                startTime = System.currentTimeMillis();
            }
            grabber.stop();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private byte[] getFrameDatas(Integer startTs, Integer endTs,  Grabber grabber) {
        try {
            grabber.start();
            byte[] bytes = grabber.grabRange(startTs, endTs);
            grabber.stop();
            return bytes;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String encodeImageFrame(Frame frame) {
        // 将byte数组转换为ByteBuffer
        Java2DFrameConverter java2DFrameConverter = new Java2DFrameConverter();
        BufferedImage bufferedImage = java2DFrameConverter.getBufferedImage(frame);
        byte[] bytes = imageToBytes(bufferedImage, "jpg");

        // 将ByteBuffer编码为Base64字符串
        String base64Encoded = Base64.getEncoder().encodeToString(bytes);

        return base64Encoded;
    }

    private byte[] imageToBytes(BufferedImage bImage, String format) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            ImageIO.write(bImage, format, out);
        } catch (IOException e) {
            return null;
        }
        return out.toByteArray();
    }

    public static void main(String[] args) {
    }
}
