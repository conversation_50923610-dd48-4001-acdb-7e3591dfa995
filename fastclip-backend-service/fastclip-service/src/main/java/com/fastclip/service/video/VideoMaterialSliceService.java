package com.fastclip.service.video;

import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialStatusEnum;
import com.fastclip.common.model.dto.SellerDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.model.dto.VideoMaterialSliceDTO;
import com.fastclip.common.model.request.CreateVideoReq;
import com.fastclip.common.model.request.UpdateVideoMaterialsReq;
import com.fastclip.common.model.request.VideoMaterialReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.VideoMaterialSliceUtils;
import com.fastclip.dao.utils.VideoUtils;
import com.fastclip.service.WhisperService;
import com.fastclip.service.douyin.LiveVideoSliceService;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VideoMaterialSliceService {

    @Autowired
    VideoMaterialSliceMapper videoMaterialSliceMapper;

    public List<VideoMaterialSliceDTO> getVideoMaterialSlicesByVideoId(Long videoId) {
        VideoMaterialSliceExample videoMaterialSliceExample = new VideoMaterialSliceExample();
        videoMaterialSliceExample.createCriteria().andVideoIdEqualTo(videoId);
        videoMaterialSliceExample.setOrderByClause("number");
        List<VideoMaterialSlice> videoMaterialSlices = videoMaterialSliceMapper.selectByExample(videoMaterialSliceExample);
        return VideoMaterialSliceUtils.do2DTO(videoMaterialSlices);
    }
}
