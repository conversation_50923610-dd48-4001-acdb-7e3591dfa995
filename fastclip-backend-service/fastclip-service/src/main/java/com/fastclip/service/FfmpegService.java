//package com.fastclip.service;
//
//import com.fastclip.common.model.dto.VideoClipToCut;
//import com.fastclip.common.effect.SpecialEffectAbstract;
//import com.fastclip.common.effect.TransitionInSpecialEffect;
//import com.fastclip.common.utils.SrtUtils;
//import com.fastclip.service.zhuanchang.ZhuangChangVideoService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.io.*;
//import java.util.ArrayList;
//import java.util.List;
//
//@Service
//@Slf4j
//public class FfmpegService {
//
//    @Autowired
//    Translation translation;
//
//    @Autowired
//    ZhuangChangVideoService zhuangChangVideoService;
//
//    public void cutHeadAndTail(Integer duration, String inputFile, String outputFile) {
//        try {
//            ProcessBuilder process = newCutHeadAndTailProcessBuilder(duration, inputFile, outputFile);
//            printProcessExecResult(process.start());
//        }catch (Exception e){
//            log.error("insertSrt error", e);
//        }
//    }
//
//    public void acodec(String inputFile, String outputFile) {
//        try {
//            ProcessBuilder process = newAcodecProcessBuilder(inputFile, outputFile);
//            printProcessExecResult(process.start());
//        } catch (Exception e){
//            log.error("acodec error", e);
//        }
//    }
//
//    public void hflip(String inputFile, String outputFile) {
//        try {
//            ProcessBuilder process = newHflipProcessBuilder(inputFile, outputFile);
//            printProcessExecResult(process.start());
//        }catch (Exception e){
//            log.error("hflip error", e);
//        }
//    }
//
//    public void crop(String inputFile,String outputFile) {
//        try {
//            ProcessBuilder process = newCropProcessBuilder(inputFile, outputFile);
//            printProcessExecResult(process.start());
//        }catch (Exception e){
//            log.error("crop error", e);
//        }
//    }
//
//    /**
//     * 将字幕文件和视频进行合成
//     * @param inputFile
//     * @param srtPath
//     * @param outputFile
//     */
//    public void insertSrt(String inputFile, String srtPath,String outputFile) {
//        try {
//            ProcessBuilder process = newInsertSrtProcessBuilder(inputFile, srtPath, outputFile);
//            printProcessExecResult(process.start());
//        }catch (Exception e){
//            log.error("insertSrt error", e);
//        }
//    }
//
//    /**
//     * 根据精简以后的字幕重新剪辑生成视频，并合成字幕
//     * @param cutSrtPath
//     * @param inputVideoPath
//     * @param outputFile
//     */
//    public void cutAndMerageByPeriodList(String cutSrtPath, String inputVideoPath, String outputFile) {
//        //获取切割后的字幕片段
//        List<VideoClipToCut> videoClips = getVideoClipToProcess(cutSrtPath);
//        cutAndMerageByPeriodList(videoClips, inputVideoPath, outputFile);
//    }
//
//    /**
//     * 从字幕文件中获取时间片段列表
//     * @param cutSrtPath
//     * @return
//     */
//    private List<VideoClipToCut> getVideoClipToProcess(String cutSrtPath){
//        try {
//            File file = new File(cutSrtPath);
//            BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
//            String line;
//            List<VideoClipToCut> periods = new ArrayList<>();
//            while((line = bufferedReader.readLine()) != null) {
//                if(line.contains("-")){
//                    int indexOfLine = line.indexOf("-");
//                    int indexOfLeftS = line.indexOf("[");
//                    int indexOfRightS = line.indexOf("]");
//                    VideoClipToCut period = new VideoClipToCut();
//                    String startTime = line.substring(indexOfLeftS +1, indexOfLine).trim().replace(",", ".");
//                    String endTime = line.substring(indexOfLine + 1 , indexOfRightS).replace(",", ".");;
//                    int  startTimeMM = SrtUtils.getTimeFromTimeStr(startTime);
//                    int endTimeMM = SrtUtils.getTimeFromTimeStr(endTime);
//                    period.setStart_time(startTime);
//                    period.setEnd_time(endTime);
//                    period.setStartTimeMM(startTimeMM);
//                    period.setEndTimeMM(endTimeMM);
//                    period.setDuration(endTimeMM - startTimeMM);
//                    List<SpecialEffectAbstract> specialEffects = new ArrayList<>();
//                    TransitionInSpecialEffect effect = new TransitionInSpecialEffect();
//                    effect.setDuration(1);
//                    effect.setAlpha(1);
//                    effect.setStartTime(0);
//                    specialEffects.add(effect);
//                    period.setSpecialEffects(specialEffects);
//                    periods.add(period);
//                }
//            }
//            return periods;
//        }catch (Exception e) {
//            log.error("get video period from srt file error", e);
//        }
//        return new ArrayList<>();
//    }
//
//    /**
//     * 根据字幕片段列表重新剪切生成视频
//     * @param videoClips
//     * @param inputVideoPath
//     * @param outputFile
//     */
//    private void cutAndMerageByPeriodList(List<VideoClipToCut> videoClips, String inputVideoPath, String outputFile) {
//        try {
//            String filePath = inputVideoPath.substring(0, inputVideoPath.length() - 4);
//            for(int i = 0; i < videoClips.size(); i++) {
//                VideoClipToCut videoClip = videoClips.get(i);
//                String tmpOutputFile =  filePath + "_tmp" + i + ".mp4";
//                ProcessBuilder process = newCutVideoProcessBuilder(videoClip, inputVideoPath, tmpOutputFile);
//                printProcessExecResult(process.start());
//                videoClip.setPath(tmpOutputFile);
//            }
//            List<VideoClipToCut> newClips = zhuangChangVideoService.getZhuanchangVideos(videoClips);
//            ProcessBuilder process = mergeVideosProcessBuilder(newClips, outputFile);
//            printProcessExecResult(process.start());
//
//        } catch (Exception e) {
//            log.error("merge videos error", e);
//            return;
//        }
//    }
//
//    private ProcessBuilder newAcodecProcessBuilder(String inputFile, String outputFile) {
//        return new ProcessBuilder(
//                "ffmpeg",
//                "-i", inputFile,
//                "-ac", "2", "-ar", "16000","-acodec", "pcm_s16le",
//                outputFile, "-y");
//    }
//
//    private ProcessBuilder newHflipProcessBuilder(String inputFile, String outputFile) {
//        return new ProcessBuilder(
//                "ffmpeg",
//                "-i", inputFile,
//                "-vf", "hflip",
//                outputFile, "-y");
//    }
//
//    private ProcessBuilder newDelogoProcessBuilder(String inputFile, String outputFile) {
//        return new ProcessBuilder(
//                "ffmpeg",
//                "-i", inputFile,
//                "-vf", "delogo=x=10:y=10:w=220:h=220",
//                outputFile, "-y");
//    }
//    private ProcessBuilder newInsertSrtProcessBuilder(String inputVideoPath, String srtPath, String outputFile) {
//        return new ProcessBuilder(
//                        "ffmpeg",
//                        "-i", inputVideoPath,
////                        "-i", srtPath,
//                        "-lavfi",
//                        "subtitles="+srtPath+":force_style='Alignment=2,MarginV=50,Fontsize=16,PrimaryColour=&H00FFC057''",
//                        outputFile, "-y");
//    }
//
//    private ProcessBuilder newCropProcessBuilder(String inputVideoPath, String outputFile) {
//        return new ProcessBuilder(
//                "ffmpeg",
//                "-i", inputVideoPath,
//                "-vf",
//                "crop=5/6*iw:6/7*ih:iw/12:ih/12",
//                outputFile, "-y");
//    }
//
//    private ProcessBuilder newCutHeadAndTailProcessBuilder(Integer duration, String inputVideoPath, String outputFile) {
//        String tTime = null;
//        Integer minutes = (duration-6)/60000;
//        Integer seconds = ((duration-6)/1000)%60;
//        String minuteStr = null;
//        String secondStr = null;
//        if(minutes >= 10) {
//            minuteStr = String.valueOf(minutes);
//        }else {
//            minuteStr = "0" + minutes;
//        }
//        if(seconds >= 10) {
//            secondStr = String.valueOf(seconds);
//        }else{
//            secondStr = "0" + seconds;
//        }
//        return new ProcessBuilder(
//                "ffmpeg",
//                "-i", inputVideoPath,
//                "-ss", "00:00:03",
//                "-t", "00:" + minuteStr +":" + secondStr,
//                "-vf", "unsharp=5:5:0.7",
//                outputFile, "-y");
//    }
//
//    private ProcessBuilder newCutVideoProcessBuilder(VideoClipToCut videoPeriod, String inputVideoPath, String outputFile) {
//        return new ProcessBuilder(
//                "ffmpeg",
//                "-ss", videoPeriod.getStart_time(),
//                "-to", videoPeriod.getEnd_time(),
//                "-i", inputVideoPath,
//                "-c:v", "libx264", "-c:a", "aac", "-strict","experimental", "-b:a", "128k", "-s", "720x1280",
//                outputFile, "-y");
//    }
//
//    private ProcessBuilder mergeVideosProcessBuilder(List<VideoClipToCut> videoClips, String outputFile) {
//        String[] inputParam = new String[videoClips.size() * 2 + 11];
//        inputParam[0] = "ffmpeg";
//        Float setpts = 0f;
//        int videoSize = videoClips.size();
//        for(int i = 0; i<videoSize; i++) {
//            VideoClipToCut videoClip = videoClips.get(i);
//            inputParam[i*2 + 1] = "-i";
//            inputParam[i*2 + 2] = videoClip.getPath();
//        }
//        StringBuilder filterParam = new StringBuilder();
//        for(int i = 0; i<videoSize; i++) {
//            VideoClipToCut videoClip = videoClips.get(i);
//            filterParam.append("[").append(i).append(":v]format=yuva420p");
//            List<SpecialEffectAbstract> specialEffects = videoClip.getSpecialEffects();
//            for(int j=0; j<specialEffects.size(); j++) {
//                SpecialEffectAbstract specialEffect = specialEffects.get(j);
//                filterParam.append(",").append(specialEffect.getFFmpegParam());
//            }
//            filterParam.append(",setpts=PTS-STARTPTS+(").append(setpts / 1000).append("/TB)");
//            filterParam.append("[v").append(i + 1).append("]");
//            setpts += videoClip.getDuration();
//            filterParam.append(";");
//        }
//        String leftVideoName = "v1";
//        String resultVideoName = "";
//        for(int i = 0; i < videoSize - 1; i++) {
//            String rightVideoName = "v" + (i + 2);
//            resultVideoName = "v" + (i + 1)  + (i +2);
//            filterParam.append("[").append(leftVideoName).append("]").append("[").append(rightVideoName).append("]").
//                    append("overlay").append("[").append(resultVideoName).append("]").append(";");
//            leftVideoName = resultVideoName;
//        }
//        for(int i = 0; i < videoSize - 1; i++) {
//            String audioName = i+":a";
//            filterParam.append("[").append(audioName).append("]");
//        }
//        filterParam.append("concat=n=").append(videoSize).append(":v=0:a=1[a];");
//        inputParam[videoSize*2 + 1] = "-filter_complex";
//        inputParam[videoSize*2 + 2] = filterParam.toString();
//        inputParam[videoSize*2 + 3] = "-s";
//        inputParam[videoSize*2 + 4] = "720x1280";
//        inputParam[videoSize*2 + 5] = "-map";
//        inputParam[videoSize*2 + 6] = "[" + resultVideoName + "]";
//        inputParam[videoSize*2 + 7] = "-map";
//        inputParam[videoSize*2 + 8] = "[a]";
//        inputParam[videoSize*2 + 9] = outputFile;
//        inputParam[videoSize*2 + 10] = "-y";
//        return new ProcessBuilder(inputParam);
//    }
//
//
//    private void printProcessExecResult(Process process) {
//        try {
//            // 读取FFmpeg进程的错误流
//            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
//            String line;
//            while ((line = errorReader.readLine()) != null) {
//                log.info(line);
//            }
//            // 等待FFmpeg进程结束
//            process.waitFor();
//
//            log.info("Conversion completed successfully.");
//        }catch (Exception e) {
//            log.error("print process exec result error .........", e);
//
//        }
//    }
//
//}
