package com.fastclip.service.seller;

import com.fastclip.common.model.dto.SellerDTO;
import com.fastclip.common.model.request.DouyinAccountReq;
import com.fastclip.common.model.request.SellerReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.dao.mapper.SellerMapper;
import com.fastclip.dao.model.dataobject.Seller;
import com.fastclip.dao.model.dataobject.SellerExample;
import com.fastclip.dao.utils.SellerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class SellerService {

    @Value("${ffmpeg.seller}")
    String sellerPath;

    @Autowired
    SellerMapper sellerMapper;

    public PagebleRes<SellerDTO> getSellerList(SellerReq req) {
        PagebleRes<SellerDTO> pagebleRes = new PagebleRes<>();
        SellerExample sellerExample = new SellerExample();
        SellerExample.Criteria criteria = sellerExample.createCriteria();
        if(req.getSellerName() != null) {
            criteria.andSellerNameLike( "%" + req.getSellerName() + "%");
        }
        if(req.getPageNum() == null) {
            req.setPageNum(1);
        }
        if(req.getPageSize() == null) {
            req.setPageSize(50);
        }
        if(!CollectionUtils.isEmpty(req.getSellerIds())) {
            criteria.andIdIn(req.getSellerIds());
        }
        RowBounds rowBounds = new RowBounds((req.getPageNum() - 1) * req.getPageSize(), req.getPageSize());
        List<Seller> sellers = sellerMapper.selectByExampleWithRowbounds(sellerExample, rowBounds);
        Long count = sellerMapper.countByExample(sellerExample);
        pagebleRes.setData(SellerUtils.do2DTO(sellers));
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal(count.intValue());
        return pagebleRes;
    }


    /**
     * 创建达人接口
     * @param sellerDTO
     * @return
     */
    public Boolean createSeller(SellerDTO sellerDTO) {
        Seller seller = SellerUtils.dto2DO(sellerDTO);
        seller.setCreateTime(new Date());
        seller.setUpdateTime(new Date());
        String basePath = seller.getMaterialBasePath();
        if(basePath == null || !basePath.startsWith(sellerPath)) {
            throw new RuntimeException("目录必须要以" +sellerPath+"开头");
        }
        File directory = new File(basePath);
        boolean result = directory.mkdirs();
        if (result) {
            log.info("创建目录{}成功", seller.getMaterialBasePath());
        } else {
            throw new RuntimeException("创建目录"+sellerPath + "失败");
        }
        int res = sellerMapper.insert(seller);
        if(res > 0) {
            return true;
        }
        return false;
    }

    /**
     * 更新达人
     * @param sellerDTO
     * @return
     */
    public Boolean updateSeller(SellerDTO sellerDTO) {
        Seller seller = SellerUtils.dto2DO(sellerDTO);
        seller.setUpdateTime(new Date());
        int res = sellerMapper.updateByPrimaryKey(seller);
        if(res > 0) {
            return true;
        }
        return false;
    }

    /**
     * 删除达人接口
     * @param sellerDTO
     * @return
     */
    public Boolean deleteSeller(SellerDTO sellerDTO) {
        int res = sellerMapper.deleteByPrimaryKey(sellerDTO.getSellerId());
        if(res > 0) {
            return true;
        }
        return false;
    }
}
