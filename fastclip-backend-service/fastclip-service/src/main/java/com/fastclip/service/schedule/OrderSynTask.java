package com.fastclip.service.schedule;

import com.alibaba.fastjson.JSON;
import com.fastclip.common.model.dto.DouyinQRCheckResult;
import com.fastclip.common.model.dto.DouyinUserInfo;
import com.fastclip.common.model.dto.DouyinUserToken;
import com.fastclip.dao.mapper.DouyinAccountMapper;
import com.fastclip.dao.mapper.OrderMapperExt;
import com.fastclip.dao.mapper.QRCodeMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.service.douyin.AccountService;
import com.fastclip.service.douyin.OrderService;
import com.fastclip.service.http.DouyinHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class OrderSynTask {


    @Autowired
    DouyinHttpUtils douyinHttpUtils;

    @Autowired
    DouyinAccountMapper douyinAccountMapper;

    @Autowired
    AccountService accountService;

    @Autowired
    OrderService orderService;

    @Value("${schedule.order_sync_enabled}")
    Boolean scheduleEnabled;

    @Scheduled(cron = "30 0 0 * * *")
    public void syncOrders() {
        try {
            if (!scheduleEnabled) {
                return;
            }
            DouyinAccountExample example = new DouyinAccountExample();
            example.createCriteria().andInviteDoneEqualTo(true);
            List<DouyinAccount> douyinAccounts = douyinAccountMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(douyinAccounts)) {
                log.info("get 0  douyin accounts!");
                return;
            }else{
                log.info("get {}  douyin accounts!", douyinAccounts.size());
            }
            for(DouyinAccount douyinAccount: douyinAccounts) {
                orderService.syncByAccount(douyinAccount);

            }
        }catch (Exception e) {
            log.error("insert pic error", e);
        }
    }

}
