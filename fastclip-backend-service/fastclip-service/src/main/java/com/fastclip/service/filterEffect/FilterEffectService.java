package com.fastclip.service.filterEffect;

import com.fastclip.common.model.dto.FontEffectDTO;
import com.fastclip.common.model.dto.specialEffect.FilterEffectDTO;
import com.fastclip.dao.mapper.FilterEffectMapper;
import com.fastclip.dao.mapper.FontEffectMapper;
import com.fastclip.dao.model.dataobject.FilterEffect;
import com.fastclip.dao.model.dataobject.FilterEffectExample;
import com.fastclip.dao.model.dataobject.FontEffect;
import com.fastclip.dao.model.dataobject.FontEffectExample;
import com.fastclip.dao.utils.FilterEffectUtils;
import com.fastclip.dao.utils.FontEffectUtils;
import com.fastclip.opencv.filter.Filter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FilterEffectService {

    @Autowired
    FilterEffectMapper filterEffectMapper;

    public List<FilterEffectDTO> getFilterEffectDTOs() {
        FilterEffectExample filterEffectExample = new FilterEffectExample();
        List<FilterEffect> filterEffects = filterEffectMapper.selectByExample(filterEffectExample);
        return FilterEffectUtils.do2DTOs(filterEffects);
    }

    public FilterEffectDTO getRandomEffectDTO() {
        List<FilterEffectDTO> filterEffectDTOS = getFilterEffectDTOs();
        int index = (int) (Math.random() * filterEffectDTOS.size());
        return filterEffectDTOS.get(index);
    }

}
