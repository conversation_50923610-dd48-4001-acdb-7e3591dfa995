package com.fastclip.service.works;

import com.fastclip.common.constant.DownloadTypeEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.DownloadLogMapper;
import com.fastclip.dao.model.dataobject.DownloadLog;
import com.fastclip.dao.model.dataobject.DownloadLogExample;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class DownloadService {

    int maxDownload = 400;

    @Autowired
    DownloadLogMapper downloadLogMapper;

    public void log(DownloadTypeEnum typeEnum, Long id) {
        UserDTO userDTO = SsoUserContext.getUser();
        DownloadLog downloadLog = new DownloadLog();
        downloadLog.setDownloadType(typeEnum.getCode());
        downloadLog.setOutId(id);
        downloadLog.setUserId(userDTO.getId());
        downloadLog.setCreateTime(new Date());
        DownloadLogExample example = new DownloadLogExample();
        example.createCriteria().andUserIdEqualTo(userDTO.getId())
                                .andCreateTimeGreaterThan(TimeUtils.getNowDate());
        Long count = downloadLogMapper.countByExample(example);
        if(count >= maxDownload) {
            throw new RuntimeException("下载次数已超过" + maxDownload + "次！");
        }
        downloadLogMapper.insert(downloadLog);
    }
}
