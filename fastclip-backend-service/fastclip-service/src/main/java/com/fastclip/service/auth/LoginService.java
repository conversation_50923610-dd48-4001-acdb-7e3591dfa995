package com.fastclip.service.auth;

import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.common.model.request.LoginReq;
import com.fastclip.common.model.request.LogoutReq;
import com.fastclip.common.model.response.LoginRes;
import com.fastclip.common.model.response.LogoutRes;
import com.fastclip.common.utils.MD5Utils;
import com.fastclip.dao.mapper.FUserMapper;
import com.fastclip.dao.model.dataobject.FUser;
import com.fastclip.dao.model.dataobject.FUserExample;
import com.fastclip.dao.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class LoginService {

    @Autowired
    FUserMapper fUserMapper;

    @Autowired
    TokenService tokenService;

    public LoginRes login(LoginReq loginReq) {
        LoginRes res = new LoginRes();
        res.setLoginSuccess(false);
        String token;
        FUserExample fUserExample = new FUserExample();
        fUserExample.createCriteria().andUserAccountEqualTo(loginReq.getUserName());
        List<FUser> fUsers = fUserMapper.selectByExample(fUserExample);
        if(CollectionUtils.isEmpty(fUsers)) {
            return res;
        }
        FUser fUser = fUsers.get(0);
        UserDTO userDTO = UserUtils.do2DTO(fUser);
        if(fUser.getPasswd() != null && fUser.getPasswd().equals(loginReq.getPasswd())) {
            token = MD5Utils.getToken(userDTO);
            res.setToken(token);
            res.setLoginSuccess(true);
            tokenService.deleteToken(userDTO);
            tokenService.insertToken(token, userDTO);
            SsoUserContext.setUser(userDTO);
        }
        return res;
    }

    public LogoutRes logout() {
        LogoutRes res = new LogoutRes();
        try {
            UserDTO userDTO = SsoUserContext.getUser();
            tokenService.deleteToken(userDTO);
            res.setLogoutSuccess(true);
        }catch (Exception e) {
            res.setLogoutSuccess(false);
        }
        return res;
    }
}
