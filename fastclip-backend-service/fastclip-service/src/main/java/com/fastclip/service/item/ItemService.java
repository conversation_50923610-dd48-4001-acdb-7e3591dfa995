package com.fastclip.service.item;

import au.com.bytecode.opencsv.CSVReader;
import com.alibaba.excel.util.DateUtils;
import com.fastclip.common.constant.ChineseToNum;
import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.ItemUtils;
import com.fastclip.dao.utils.ProjectUtils;
import com.fastclip.dao.utils.VideoUtils;
import com.fastclip.service.video.VideoMaterialService;
import com.fastclip.service.http.GroundingHttpUtils;
import com.fastclip.service.websocket.ProductRecognitionWebSocketHandler;
import com.fastclip.service.exception.VideoProcessingException;
import com.fastclip.opencv.grabber.FFmpegUtils;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import javax.imageio.ImageIO;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.FileReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static java.sql.Types.BOOLEAN;
import static java.sql.Types.NUMERIC;
import static org.apache.poi.ss.usermodel.DataValidationConstraint.ValidationType.FORMULA;
import static org.apache.xmlbeans.impl.piccolo.xml.Piccolo.STRING;

@Service
@Slf4j
public class ItemService {

    @Autowired
    ItemMapper itemMapper;

    @Autowired
    ItemMapperExt itemMapperExt;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    GroundingHttpUtils groundingHttpUtils;

    @Autowired
    ProductRecognitionWebSocketHandler webSocketHandler;

    @Autowired
    VideoMaterialClipMapper videoMaterialClipMapper;

    @Autowired
    LoadingCache<Long, SellerDTO> sellerCache;

    @Autowired
    ItemOnLiveMapper itemOnLiveMapper;

    @Autowired
    LoadingCache<Long, ProjectDTO> projectCache;



    public PagebleRes<ItemDTO> getItemList(ItemReq req) {
//        if(ItemTypeEnum.Import.getValue().equals(req.getItemType())) {
            return getItemListFromImportItems(req);
//        }else{
//            return getItemListFromLiveItems(req);
//        }
    }

    private PagebleRes<ItemDTO> getItemListFromImportItems(ItemReq req) {
        PagebleRes<ItemDTO> pagebleRes = new PagebleRes();
        List<ItemDTO> itemDTOS = new ArrayList<>();
        if(req.getOffset() == null) {
            req.setTopN(0);
        }
        if(req.getPageSize() == null) {
            req.setPageSize(10);
        }
        if(req.getPageNum() == null) {
            req.setPageNum(1);
        }
        req.setOffset((req.getPageNum() -1) * req.getPageSize());
        List<ItemExt> items = itemMapperExt.getItems(req);
        itemDTOS = ItemUtils.extDo2DTOs(items);
        for (ItemDTO itemDTO : itemDTOS) {
            itemDTO.setSeller(sellerCache.get(itemDTO.getSellerId()));
            if(itemDTO.getProjectId() != null) {
                itemDTO.setProjectDTO(projectCache.get(itemDTO.getProjectId()));
            }
            itemDTO.setMaxMaterialDate(itemDTO.getCreateTime());
        }
        Integer count = itemMapperExt.count(req);
        pagebleRes.setData(itemDTOS);
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal(count);
        return pagebleRes;
    }



    private PagebleRes<ItemDTO> getItemListFromLiveItems(ItemReq req) {
        PagebleRes<ItemDTO> pagebleRes = new PagebleRes();
        ItemOnLiveExample itemOnLiveExample = new ItemOnLiveExample();
        ItemOnLiveExample.Criteria criteria = itemOnLiveExample.createCriteria();
        if (req.getItemName() != null) {
            criteria.andItemNameLike("%" + req.getItemName() + "%");
        }
        if (req.getPageNum() == null) {
            req.setPageNum(1);
        }
        if (req.getPageSize() == null) {
            req.setPageSize(20);
        }
        if (!CollectionUtils.isEmpty(req.getSellerIds())) {
            criteria.andSellerIdIn(req.getSellerIds());
        }
        itemOnLiveExample.setOrderByClause("create_time desc");
        RowBounds rowBounds = new RowBounds((req.getPageNum() - 1) * req.getPageSize(), req.getPageSize());
        List<ItemOnLive> items = itemOnLiveMapper.selectByExampleWithRowbounds(itemOnLiveExample, rowBounds);
        long count = itemOnLiveMapper.countByExample(itemOnLiveExample);
        List<ItemDTO> itemDTOS = ItemUtils.liveItemDo2Dtos(items);
        for (ItemDTO itemDTO : itemDTOS) {
            itemDTO.setSeller(sellerCache.get(itemDTO.getSellerId()));
            itemDTO.setMaxMaterialDate(itemDTO.getCreateTime());
        }
        pagebleRes.setData(itemDTOS);
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal((int) count);
        return pagebleRes;
    }


    public List<ItemDTO> getTopItems(ItemReq req) {
        if (req.getPageNum() == null) {
            req.setPageNum(1);
        }
        if (req.getPageSize() == null) {
            req.setPageSize(20);
        }
        UserDTO userDTO = SsoUserContext.getUser();
        req.setCreatorId(userDTO.getId());
        req.setOffset((req.getPageNum() -1) * req.getPageSize());
        if(ItemTypeEnum.Import.getValue().equals(req.getItemType())) {
            List<ItemExt> items = itemMapperExt.getItems(req);
            return ItemUtils.extDo2DTOs(items);
        }else{
            List<ItemExt> items = itemMapperExt.getLiveItems(req);
            return ItemUtils.extDo2DTOs(items);
        }

    }

    /**
     * 删除商品
     * @param req
     * @return
     */
    public Boolean deleteItem(DeleteItemReq req) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        ItemExample itemExample = new ItemExample();
        itemExample.createCriteria().andIdIn(req.getItemIds());
        int res = itemMapper.deleteByExample(itemExample);
        if (res > 0) {
            return true;
        }
        return false;
    }

    /**
     * 更新商品
     * @param req
     * @return
     */
    public Boolean updateItem(UpdateItemReq req) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        PagebleRes<ItemDTO> pagebleRes = new PagebleRes();
        ItemExample itemExample = new ItemExample();
        ItemExample.Criteria criteria = itemExample.createCriteria();
        criteria.andIdEqualTo(req.getId());
        Item item = new Item();
        if (req.getIsAvailable() != null) {
            item.setIsAvailable(req.getIsAvailable());
        }
        if (req.getIsPresell() != null) {
            item.setIsPresell(req.getIsPresell());
        }
        if (req.getIsPublish() != null) {
            item.setIsPublish(req.getIsPublish());
        }
        if (req.getItemName() != null) {
            item.setItemName(req.getItemName());
        }
        if (req.getOutItemId() != null) {
            item.setOutItemId(req.getOutItemId());
        }
        int res = itemMapper.updateByExample(item, itemExample);
        if (res > 0)
            return true;
        return false;
    }

    /**
     * Upload and parse items from file
     * @param file uploaded file (xlsx or csv)
     * @return List of parsed ItemDTO objects
     */
    public List<ItemDTO> uploadItems(MultipartFile file) {
        log.info("Starting file upload processing, filename: {}", file != null ? file.getOriginalFilename() : "null");

        try{
            // Check if file is null or empty
            if (file == null || file.isEmpty()) {
                log.warn("File is null or empty");
                throw new RuntimeException("上传文件为空，请选择有效的文件");
            }

            String filename = file.getOriginalFilename();
            log.info("Processing file: {}, size: {} bytes", filename, file.getSize());

            List<ItemDTO> result;
            if(filename.endsWith(".xlsx")) {
                log.info("Processing Excel file: {}", filename);
                result = getDataFromXlsxFile(file.getInputStream());
            }else if(filename.endsWith(".csv")) {
                log.info("Processing CSV file: {}", filename);
                result = getDataFromCsvFile(file.getInputStream());
            } else {
                log.warn("Unsupported file type: {}", filename);
                throw new RuntimeException("不支持的文件类型，请上传 .xlsx 或 .csv 文件");
            }

            log.info("File processing completed, parsed {} items", result.size());
            return result;

        }catch (Exception e) {
            log.error("Error processing uploaded file: {}", file != null ? file.getOriginalFilename() : "null", e);
            throw new RuntimeException("文件处理失败: " + e.getMessage());
        }
    }

    /**
     *  增加商品
     * @param itemReq
     * @return
     */
    @Transactional
    public Integer addItems(AddItemReq itemReq) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        List<ItemDTO> itemDTOS = itemReq.getItems();
        for(ItemDTO itemDTO: itemDTOS) {
            try {
                ItemDTO oldItemDTO = getItemByOutId(itemDTO.getOutItemId());
                if (oldItemDTO == null) {
                    Item record = ItemUtils.dto2DO(itemDTO);
                    record.setSellerId(itemReq.getSellerId());
                    record.setCreator(SsoUserContext.getUser().getUserName());
                    record.setCreatorId(SsoUserContext.getUser().getId());
                    record.setItemType(ItemTypeEnum.Import.getValue());
                    record.setIsPublish(false);
                    itemMapper.insert(record);
                    itemDTO.setId(record.getId());
                } else {
                    itemDTO.setId(oldItemDTO.getId());
                    itemMapper.updateByPrimaryKeySelective(ItemUtils.dto2DO(itemDTO));
                }
                List<VideoMaterialClipDTO> videoMaterialClipDTOList = itemDTO.getVideoMaterialClipDTOList();
                if (CollectionUtils.isEmpty(videoMaterialClipDTOList)) {
                    continue;
                }
                for (VideoMaterialClipDTO clipDTO : videoMaterialClipDTOList) {
                    VideoMaterialClip clip = VideoUtils.dto2DO(clipDTO);
                    clip.setSellerId(itemReq.getSellerId());
                    clip.setItemId(itemDTO.getId());
                    Date videoDate = clipDTO.getVideoDate();
                    VideoMaterialDTO videoMaterialDTO = null;
                    if(clipDTO.getScene() > 1) {
                        videoMaterialDTO = videoMaterialService.getVideoMaterialByScene(itemReq.getSellerId(),
                                videoDate, clipDTO.getScene());
                        if(videoMaterialDTO == null) {
                            continue;
                        }
                        clip.setStartTs(clipDTO.getStartTs() + videoMaterialDTO.getStartTime());
                    }
                    videoMaterialDTO = videoMaterialService.getVideoMaterial(itemReq.getSellerId(),
                            videoDate, clip.getStartTs());

                    if (videoMaterialDTO != null) {
                        clip.setVideoId(videoMaterialDTO.getId());
                        try {
                            videoMaterialClipMapper.insert(clip);
                        } catch (Exception e) {

                        }
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return itemDTOS.size();
    }


    private List<ItemDTO> getDataFromXlsxFile(InputStream inputStream){
        List<ItemDTO> itemDatas = new ArrayList<>();
        try {
            log.info("Starting to parse Excel file");
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

            int totalRows = sheet.getLastRowNum();
            log.info("Excel file has {} rows (including header)", totalRows + 1);

            if (totalRows < 1) {
                log.warn("Excel file has no data rows");
                return itemDatas;
            }

            // Process data rows (skip header row at index 0)
            for (int i = 1; i <= totalRows; i++) {
                try {
                    Row row = sheet.getRow(i);
                    if (row == null) {
                        log.warn("Row {} is null, skipping", i);
                        continue;
                    }

                    ItemDTO itemDTO = new ItemDTO();

                    // Safely get cell values with null checks
                    Cell nameCell = row.getCell(0);
                    Cell idCell = row.getCell(1);
                    Cell urlCell = row.getCell(5);
                    Cell clipCell = row.getCell(8);

                    if (nameCell == null || idCell == null) {
                        log.warn("Row {} missing required fields (name or id), skipping", i);
                        continue;
                    }

                    itemDTO.setItemName(getCellStringValue(nameCell));
                    itemDTO.setOutItemId(getCellStringValue(idCell));
                    itemDTO.setCreateTime(new Date());
                    itemDTO.setUpdateTime(new Date());
                    itemDTO.setIsAvailable(true);

                    if (urlCell != null) {
                        itemDTO.setShareUrl(getCellStringValue(urlCell));
                    }

                    if (clipCell != null) {
                        String clipStr = getCellStringValue(clipCell);
                        if (clipStr != null && !clipStr.trim().isEmpty()) {
                            List<VideoMaterialClipDTO> videoMaterialClipDTOS = getVideoMaterialClip(clipStr);
                            itemDTO.setVideoMaterialClipDTOList(videoMaterialClipDTOS);
                        }
                    }

                    itemDatas.add(itemDTO);
                    log.debug("Parsed item: {} ({})", itemDTO.getItemName(), itemDTO.getOutItemId());

                } catch (Exception e) {
                    log.error("Error parsing row {}: {}", i, e.getMessage());
                    // Continue processing other rows
                }
            }

            workbook.close();
            log.info("Successfully parsed {} items from Excel file", itemDatas.size());

        }catch (Exception e) {
            log.error("Error parsing Excel file", e);
            throw new RuntimeException("Excel文件解析失败: " + e.getMessage());
        }
        return itemDatas;
    }

    /**
     * Safely get string value from Excel cell
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    private List<ItemDTO> getDataFromCsvFile(InputStream inputStream){
        List<ItemDTO> itemDatas = new ArrayList<>();
        InputStreamReader reader = null;
        CSVReader csvReader = null;

        try {
            log.info("Starting to parse CSV file");
            reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            csvReader = new CSVReader(reader);

            List<String[]> records = csvReader.readAll();
            log.info("CSV file has {} rows (including header)", records.size());

            if (records.size() < 2) {
                log.warn("CSV file has no data rows");
                return itemDatas;
            }

            // Process data rows (skip header row at index 0)
            for (int i = 1; i < records.size(); i++) {
                try {
                    String[] row = records.get(i);

                    if (row.length < 2) {
                        log.warn("Row {} has insufficient columns, skipping", i);
                        continue;
                    }

                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setOutItemId(row[0] != null ? row[0].trim() : "");
                    itemDTO.setItemName(row[1] != null ? row[1].trim() : "");
                    itemDTO.setCreateTime(new Date());
                    itemDTO.setUpdateTime(new Date());
                    itemDTO.setIsAvailable(true);

                    // Optional fields
                    if (row.length > 2 && row[2] != null && !row[2].trim().isEmpty()) {
                        itemDTO.setShareUrl(row[2].trim());
                    }

                    if (row.length > 3 && row[3] != null && !row[3].trim().isEmpty()) {
                        List<VideoMaterialClipDTO> videoMaterialClipDTOS = getVideoMaterialClip(row[3].trim());
                        itemDTO.setVideoMaterialClipDTOList(videoMaterialClipDTOS);
                    }

                    itemDatas.add(itemDTO);
                    log.debug("Parsed item: {} ({})", itemDTO.getItemName(), itemDTO.getOutItemId());

                } catch (Exception e) {
                    log.error("Error parsing CSV row {}: {}", i, e.getMessage());
                    // Continue processing other rows
                }
            }

            log.info("Successfully parsed {} items from CSV file", itemDatas.size());

        }catch (Exception e) {
            log.error("Error parsing CSV file", e);
            throw new RuntimeException("CSV文件解析失败: " + e.getMessage());
        } finally {
            try {
                if (csvReader != null) csvReader.close();
                if (reader != null) reader.close();
            } catch (Exception e) {
                log.warn("Error closing CSV reader", e);
            }
        }
        return itemDatas;
    }

    private List<VideoMaterialClipDTO> getVideoMaterialClip(String clipStrs) {

        List<VideoMaterialClipDTO> videoMaterialClipDTOList = new ArrayList<>();
        String[] videoSplits = clipStrs.split("/");
        for(String videoSplit: videoSplits) {
            Integer scene = 0;
            if(videoSplit.contains("场")) {
                int index = videoSplit.indexOf("场");
                String sceneStr = videoSplit.substring(index-1,index);
                scene = ChineseToNum.getNum(sceneStr);
                if(scene == null) {
                    try {
                        scene = Integer.parseInt(sceneStr);
                    }catch (Exception e){
                        log.error("parse scene string error", e);
                    }
                }
            }
            String filteredVideoSplit = videoSplit.replaceAll("日常号", "").replaceAll("号", "日")
                    .replaceAll("（一场）","")
                    .replaceAll("（二场）","").replaceAll("（第一场）","").replaceAll("（第二场）","")
                    .replaceAll("（第三场）","").replaceAll("（第1场）","").replaceAll("（第2场）","")
                    .replaceAll("（第3场）","").replaceAll("第一场","").replaceAll("第二场","")
                    .replaceAll("（第1场）","").replaceAll("第2场","").replaceAll("第3场","")
                    .replaceAll("一场","").replaceAll("二场","").replaceAll("三场","")
                    .replaceAll("1场","").replaceAll("2场","").replaceAll("3场","")
                    .replaceAll("日","日 ").replaceAll("小号","").replaceAll("大号", "")
                    .replaceAll("\\(", "").replaceAll("\\)", "").replaceAll("（", "")
                    .replaceAll("）", "");
            try {
                if (!filteredVideoSplit.contains(" ")) {
                    continue;
                }
                filteredVideoSplit = filteredVideoSplit.trim();
                String dateStr = filteredVideoSplit.substring(0, filteredVideoSplit.indexOf("日") + 1);
                Date videoDate = TimeUtils.getDate(dateStr);
                String timesStr = filteredVideoSplit.substring(filteredVideoSplit.indexOf("日") + 1);
                timesStr = timesStr.replaceAll("：", ":");
                String[] times = timesStr.trim().split(" ");
                for (String time : times) {

                    VideoMaterialClipDTO videoMaterialClipDTO = new VideoMaterialClipDTO();
                    int index = time.indexOf(":");
                    if (index < 1) {
                        continue;
                    }
                    String hourStr = time.substring(0, index);
                    int hour = Integer.parseInt(hourStr.trim());
                    String minutesStr = time.substring(index + 1);
                    int minutes = Integer.parseInt(minutesStr.trim());
                    videoMaterialClipDTO.setStartTs(hour * 3600000 + minutes * 60000);
                    videoMaterialClipDTO.setCreateTime(new Date());
                    videoMaterialClipDTO.setUpdateTime(new Date());
                    videoMaterialClipDTO.setVideoDate(videoDate);
                    if(scene != null) {
                        videoMaterialClipDTO.setScene(scene);
                    }
                    videoMaterialClipDTOList.add(videoMaterialClipDTO);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return videoMaterialClipDTOList;
    }

    /**
     * 根据外键id获取商品信息
     * @param outId
     * @return
     */
    public ItemDTO getItemByOutId(String outId) {
        ItemExample itemExample = new ItemExample();
        itemExample.createCriteria().andOutItemIdEqualTo(outId);
        List<Item> items = itemMapper.selectByExample(itemExample);
        if(CollectionUtils.isEmpty(items)) {
            return null;
        }
        return ItemUtils.do2DTO(items.get(0));
    }

    /**
     * Start video grounding process with WebSocket real-time updates
     * This is the main entry point for frontend API calls
     * @param req Video grounding request
     * @return Task ID for tracking progress
     */
    public String startVideoGrounding(VideoGroundingReq req) {
        try {
            // Generate unique task ID if not provided
            String taskId = req.getTaskId();
            if (taskId == null || taskId.isEmpty()) {
                taskId = "task-" + System.currentTimeMillis();
            }

            // Get video path from videoId or use provided path
            String videoPath = req.getVideoPath();
            if (videoPath == null && req.getVideoId() != null) {
                // TODO: Query video path from database using videoId
                VideoMaterialDTO video = videoMaterialService.getVideoMaterialById(req.getVideoId().longValue());
                if (video != null) {
                    videoPath = video.getPath();
                } else {
                    throw new RuntimeException("Video not found with ID: " + req.getVideoId());
                }
            }

            if (videoPath == null) {
                throw new RuntimeException("Video path is required");
            }

            log.info("Starting video grounding for task: {}, video: {}", taskId, videoPath);

            // Start async processing with WebSocket updates
            recognizeProductsInVideoBatchWithWebSocket(videoPath, taskId, req.getItemType(), req.getSellerId());

            return taskId;
        } catch (Exception e) {
            log.error("Error starting video grounding", e);
            throw new RuntimeException("Failed to start video grounding: " + e.getMessage());
        }
    }

    /**
     * Batch video frame product recognition with WebSocket real-time updates
     * Extracts all frames at 10-second intervals and processes them with progress updates
     * @param videoPath Video file path
     * @param taskId Task identifier for WebSocket communication
     * @param itemType Item type filter (optional)
     * @param sellerId Seller ID filter (optional)
     * @return Processing result
     */
    public CompletableFuture<String> recognizeProductsInVideoBatchWithWebSocket(String videoPath, String taskId, Integer itemType, Integer sellerId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Extract all video frames at 10-second intervals
                ConcurrentLinkedQueue<VideoFrameData> frameQueue = extractVideoFramesBatch(videoPath, 10000); // 10 seconds in milliseconds

                if (frameQueue.isEmpty()) {
                    log.error("No frames extracted from video: {}", videoPath);
                    webSocketHandler.broadcastCompletion(taskId);
                    return null;
                }

                log.info("Extracted {} frames from video: {}", frameQueue.size(), videoPath);

                // Process frames with real-time WebSocket updates
                return processFramesBatchWithWebSocket(frameQueue, taskId, itemType, sellerId);

            } catch (Exception e) {
                log.error("Error during batch product recognition for video: {}", videoPath, e);
                webSocketHandler.broadcastCompletion(taskId);
                return null;
            }
        });
    }

    /**
     * Data class for video frame information
     */
    private static class VideoFrameData {
        public final long timestamp;
        public final String base64Data;

        public VideoFrameData(long timestamp, String base64Data) {
            this.timestamp = timestamp;
            this.base64Data = base64Data;
        }
    }

    /**
     * Extract video frames at specified intervals using FFmpeg
     * @param videoPath Video file path
     * @param intervalMs Interval in milliseconds (e.g., 10000 for 10 seconds)
     * @return Queue of extracted frame data
     */
    private ConcurrentLinkedQueue<VideoFrameData> extractVideoFramesBatch(String videoPath, long intervalMs) {
        ConcurrentLinkedQueue<VideoFrameData> frameQueue = new ConcurrentLinkedQueue<>();

        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoPath)) {
            grabber.start();

            double frameRate = grabber.getFrameRate();
            long totalFrames = grabber.getLengthInFrames();
            long intervalFrames = (long) (frameRate * intervalMs / 1000.0);

            log.info("Video info - FPS: {}, Total frames: {}, Interval frames: {}", frameRate, totalFrames, intervalFrames);

            Java2DFrameConverter converter = new Java2DFrameConverter();

            for (long frameIndex = 0; frameIndex < totalFrames; frameIndex += intervalFrames) {
                grabber.setFrameNumber((int) frameIndex);
                Frame frame = grabber.grabImage();

                if (frame != null) {
                    long timestampMs = (long) (frameIndex / frameRate * 1000);
                    BufferedImage bufferedImage = converter.getBufferedImage(frame);
                    String base64 = convertImageToBase64(bufferedImage);

                    if (base64 != null) {
                        frameQueue.offer(new VideoFrameData(timestampMs, base64));
                        log.debug("Extracted frame at {}ms (frame {})", timestampMs, frameIndex);
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error extracting video frames from: {}", videoPath, e);
        }

        return frameQueue;
    }

    /**
     * Process frames in batch with WebSocket real-time updates
     * @param frameQueue Queue of frame data
     * @param taskId Task identifier for WebSocket communication
     * @param itemType Item type filter
     * @param sellerId Seller ID filter
     * @return Batch processing result
     */
    private String processFramesBatchWithWebSocket(ConcurrentLinkedQueue<VideoFrameData> frameQueue, String taskId, Integer itemType, Integer sellerId) {
        List<VideoFrameData> frameList = new ArrayList<>(frameQueue);
        int totalSegments = frameList.size();
        StringBuilder results = new StringBuilder();

        try {
            for (int i = 0; i < frameList.size(); i++) {
                VideoFrameData frameData = frameList.get(i);

                // Send progress update via WebSocket
                webSocketHandler.broadcastProgress(taskId,
                    new ProductRecognitionWebSocketHandler.ProgressUpdate(i + 1, totalSegments));

                // Process individual frame
                SearchItemsByFrameResponseDTO result = null;
                try {
                    result = groundingHttpUtils.searchItemsByFrame(frameData.base64Data, itemType, sellerId);

                    // Parse result and send recognition update
                    ProductRecognitionWebSocketHandler.RecognitionResult recognitionResult = parseRecognitionResult(result, frameData.timestamp, i);
                    webSocketHandler.broadcastResult(taskId, recognitionResult);

                } catch (Exception e) {
                    log.error("Error processing frame {} at {}ms", i, frameData.timestamp, e);
                    // Send error result for this segment
                    webSocketHandler.broadcastResult(taskId,
                        new ProductRecognitionWebSocketHandler.RecognitionResult(
                            frameData.timestamp, i, null, 0.0, null, null));
                    result = null; // Set to null to indicate error
                }

                // Convert result to string for logging
                String resultString = result != null ? com.alibaba.fastjson.JSON.toJSONString(result) : "error";
                results.append(String.format("Timestamp: %dms, Result: %s\n", frameData.timestamp, resultString));
                log.info("Processed frame {} of {} at {}ms", i + 1, totalSegments, frameData.timestamp);
            }

            // Send completion notification
            webSocketHandler.broadcastCompletion(taskId);
            log.info("Batch processing completed for task: {}", taskId);

            return results.toString();
        } catch (Exception e) {
            log.error("Error in batch processing with WebSocket for task: {}", taskId, e);
            webSocketHandler.broadcastCompletion(taskId);
            return "Batch processing failed: " + e.getMessage();
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    private String processFramesBatch(ConcurrentLinkedQueue<VideoFrameData> frameQueue, Integer itemType, Integer sellerId) {
        // Convert VideoFrameData to GroundingHttpUtils.FrameData
        List<GroundingHttpUtils.FrameData> frameDataList = frameQueue.stream()
            .map(vfd -> new GroundingHttpUtils.FrameData(vfd.timestamp, vfd.base64Data))
            .collect(java.util.stream.Collectors.toList());

        try {
            // Use batch processing with concurrent HTTP requests
            CompletableFuture<String> batchResult = groundingHttpUtils.searchItemsByFramesBatch(frameDataList, itemType, sellerId);
            String result = batchResult.get(); // Wait for completion
            log.info("Batch processing completed for {} frames", frameDataList.size());
            return result;
        } catch (Exception e) {
            log.error("Error in batch processing", e);
            return "Batch processing failed: " + e.getMessage();
        }
    }

    /**
     * Parse recognition result from Python backend
     * @param responseDTO Structured response from Python backend (enriched with item names)
     * @param timestamp Frame timestamp
     * @param segmentIndex Segment index
     * @return Parsed recognition result
     */
    private ProductRecognitionWebSocketHandler.RecognitionResult parseRecognitionResult(SearchItemsByFrameResponseDTO responseDTO, long timestamp, int segmentIndex) {
        try {
            if (responseDTO == null || !responseDTO.hasResults()) {
                return new ProductRecognitionWebSocketHandler.RecognitionResult(timestamp, segmentIndex, null, 0.0, null, null);
            }

            // Extract top-1 result
            ItemSearchResultDTO topResult = responseDTO.getTopResult();
            if (topResult != null) {
                // Extract item information from enriched response
                String itemId = topResult.getItemId();
                String itemName = topResult.getItemName();
                Double score = topResult.getScore();
                Integer itemType = topResult.getItemType();

                // Handle null score
                if (score == null) {
                    score = 0.0;
                }

                // Apply confidence threshold
                if (score < 0.3) {
                    log.info("Score {} below threshold 0.3, setting to unknown", score);
                    return new ProductRecognitionWebSocketHandler.RecognitionResult(timestamp, segmentIndex, null, 0.0, null, null);
                }

                return new ProductRecognitionWebSocketHandler.RecognitionResult(timestamp, segmentIndex, itemType, score, itemId, itemName);
            } else {
                return new ProductRecognitionWebSocketHandler.RecognitionResult(timestamp, segmentIndex, null, 0.0, null, null);
            }
        } catch (Exception e) {
            log.error("Error parsing recognition result: {}", responseDTO, e);
            return new ProductRecognitionWebSocketHandler.RecognitionResult(timestamp, segmentIndex, null, 0.0, null, null);
        }
    }

    /**
     * Convert BufferedImage to Base64 string
     * @param image BufferedImage to convert
     * @return Base64 encoded string
     */
    private String convertImageToBase64(BufferedImage image) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            log.error("Error converting image to Base64", e);
            return null;
        }
    }

}
