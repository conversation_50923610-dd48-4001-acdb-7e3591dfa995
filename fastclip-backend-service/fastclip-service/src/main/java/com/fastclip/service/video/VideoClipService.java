package com.fastclip.service.video;

import com.fastclip.common.constant.VideoClipTagEnum;
import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.GetVideoClipsReq;
import com.fastclip.common.model.request.MergeVideoClipReq;
import com.fastclip.common.model.request.DeleteVideoClipReq;
import com.fastclip.common.model.request.TagVideoClipReq;
import com.fastclip.common.model.response.GetVideoClipsRes;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.SubtitlesCutUtils;
import com.fastclip.dao.utils.VideoClipTagUtils;
import com.fastclip.dao.utils.VideoClipUtils;
import com.fastclip.dao.utils.VideoMaterialSliceUtils;
import com.fastclip.service.subtitles.SubtitlesService;
import com.fastclip.service.works.WorksService;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VideoClipService {

    @Autowired
    VideoClipMapper videoClipMapper;

    @Autowired
    SubtitlesService subtitlesService;

    @Autowired
    SubtitlesCutMapper subtitlesCutMapper;

    @Autowired
    VideoClipDetailsMapper videoClipDetailsMapper;

    @Autowired
    VideoClipMapperExt videoClipMapperExt;

    @Autowired
    VideoClipTagMapper videoClipTagMapper;

    @Autowired
    LoadingCache<Long, VideoMaterialDTO> videoMaterialCache;

    @Autowired
    VideoMaterialSliceMapper videoMaterialSliceMapper;

    @Autowired
    WorksService worksService;


    /**
     * 合并视频片段
     * @param req
     * @return
     */
    @Transactional
    public Boolean mergeVideoClips(MergeVideoClipReq req) {
        List<Long> videoClipIds = req.getVideoClipIds();
        if(CollectionUtils.isEmpty(videoClipIds)) {
            return true;
        }
        worksService.deleteWorksByClipIds(req.getVideoClipIds());
        VideoClip newVideoClip = createNewVideoClip(req.getProjectId());

        List<VideoClipDetails> videoClipDetails = new ArrayList<>();
        List<SubtitlesCutDTO> allSubtitlesCutDTOList = new ArrayList<>();

        VideoClipExample videoClipExample = new VideoClipExample();
        videoClipExample.createCriteria().andIdIn(req.getVideoClipIds());
        videoClipExample.setOrderByClause("sort");
        List<VideoClip> videoClips = videoClipMapper.selectByExample(videoClipExample);
        Integer minSort = videoClips.get(0).getSort();
        for(VideoClip videoClip: videoClips) {
            List<SubtitlesCutDTO> subtitlesCutDTOs = subtitlesService.searchSubtitlesCutByVideoClipId(videoClip.getId());
            allSubtitlesCutDTOList.addAll(subtitlesCutDTOs);
            for(SubtitlesCutDTO subtitlesCutDTO: subtitlesCutDTOs) {
                VideoClipDetails videoClipDetail = createVideoClipDetails( SubtitlesCutUtils.dto2DO(subtitlesCutDTO));
                videoClipDetails.add(videoClipDetail);
            }
            //删除视频片段
            videoClipMapper.deleteByPrimaryKey(videoClip.getId());
            //删除视频片段详情
            deleteVideoClipDetailsByVideoClipId(videoClip.getId());
            //删除视频片段标签
            deleteVideoClipTagByVideoClipId(videoClip.getId());
            if(minSort > videoClip.getSort()) {
                minSort = videoClip.getSort();
            }
        }
        newVideoClip.setStartSubtitlesId(allSubtitlesCutDTOList.get(0).getSubtitlesId());
        newVideoClip.setStartSubtitlesCutId(allSubtitlesCutDTOList.get(0).getId());
        newVideoClip.setEndSubtitlesCutId(allSubtitlesCutDTOList.get(allSubtitlesCutDTOList.size()-1).getId());
        newVideoClip.setEndSubtitlesId(allSubtitlesCutDTOList.get(allSubtitlesCutDTOList.size()-1).getSubtitlesId());
        StringBuffer stringBuffer = new StringBuffer();
        Integer duration = 0;
        for(SubtitlesCutDTO subtitlesCutDTO: allSubtitlesCutDTOList) {
            stringBuffer.append(subtitlesCutDTO.getContent());
            duration += subtitlesCutDTO.getDuration();
        }
        newVideoClip.setSubtitles(stringBuffer.toString());
        newVideoClip.setDuration(duration);
        newVideoClip.setSort(minSort);
        newVideoClip.setSubtitlesCutCount(allSubtitlesCutDTOList.size());
        videoClipMapper.insert(newVideoClip);
        int sort = 0;
        for(VideoClipDetails videoClipDetails1: videoClipDetails) {
            sort++;
            videoClipDetails1.setSort(sort);
            videoClipDetails1.setVideoClipId(newVideoClip.getId());
            videoClipDetailsMapper.insert(videoClipDetails1);
        }
        return true;
    }

    /**
     * 解除合并视频片段
     * @param req
     * @return
     */
    @Transactional
    public Boolean splitVideoClip(DeleteVideoClipReq req) {
        worksService.deleteWorksByClipId(req.getVideoClipId());
        VideoClip videoClip = videoClipMapper.selectByPrimaryKey(req.getVideoClipId());
        videoClipMapper.deleteByPrimaryKey(req.getVideoClipId());
        VideoClipDetailsExample videoClipDetailsExample = new VideoClipDetailsExample();
        videoClipDetailsExample.createCriteria().andVideoClipIdEqualTo(req.getVideoClipId());
        List<VideoClipDetails> videoClipDetailsList = videoClipDetailsMapper.selectByExample(videoClipDetailsExample);
        //将所有视频片段顺序往后调
        int sortAdded = 0;
        UpdateSortReq updateSortReq =  new UpdateSortReq();
        updateSortReq.setProjectId(req.getProjectId());
        updateSortReq.setCurSort(videoClip.getSort());
        updateSortReq.setSortAdded(videoClipDetailsList.size() -1);
        videoClipMapperExt.updateSort(updateSortReq);
        //创建新的视频片段
        for(VideoClipDetails videoClipDetails: videoClipDetailsList) {
            VideoClip newVideoClip = new VideoClip();
            BeanUtils.copyProperties(videoClip, newVideoClip);
            newVideoClip.setSort(videoClip.getSort() + sortAdded);
            sortAdded ++;
            SubtitlesCut subtitlesCut = subtitlesCutMapper.selectByPrimaryKey(videoClipDetails.getSubtitlesCutId());
            newVideoClip.setSubtitles(subtitlesCut.getContent());
            newVideoClip.setDuration(subtitlesCut.getDuration());
            newVideoClip.setSubtitlesCutCount(1);
            videoClipMapper.insert(newVideoClip);
            videoClipDetails.setSort(1);
            videoClipDetails.setVideoClipId(newVideoClip.getId());
            videoClipDetails.setUpdateTime(new Date());
            videoClipDetailsMapper.updateByPrimaryKey(videoClipDetails);
        }
        return true;
    }

    public Integer getNextMaxSortValue(Long projectId) {
        VideoClipExample videoClipExample = new VideoClipExample();
        videoClipExample.createCriteria().andProjectIdEqualTo(projectId);
        videoClipExample.setOrderByClause("sort desc");
        RowBounds rowBounds = new RowBounds(0,1);
        List<VideoClip> videoClips = videoClipMapper.selectByExampleWithRowbounds(videoClipExample, rowBounds);
        if(CollectionUtils.isEmpty(videoClips)) {
            return 1;
        }
        return videoClips.get(0).getSort() + 1;
    }

    private VideoClipDetails createVideoClipDetails(SubtitlesCut subtitlesCut) {
        VideoClipDetails videoClipDetails = new VideoClipDetails();
        videoClipDetails.setCreateTime(new Date());
        videoClipDetails.setUpdateTime(new Date());
        videoClipDetails.setProjectId(subtitlesCut.getProjectId());
        videoClipDetails.setSubtitlesCutId(subtitlesCut.getId());
        return videoClipDetails;
    }

    private VideoClip createNewVideoClip(Long projectId) {
        VideoClip videoClip = new VideoClip();
        videoClip.setProjectId(projectId);
        videoClip.setCreateTime(new Date());
        videoClip.setUpdateTime(new Date());
        videoClip.setSubtitles("");
        return videoClip;
    }

    /**
     * 获取视频片段
     * @param req
     * @return
     */
    public GetVideoClipsRes getVideoClips(GetVideoClipsReq req) {
        GetVideoClipsRes videoClipsRes = new GetVideoClipsRes();
        VideoClipExample videoClipExample = new VideoClipExample();
        VideoClipExample.Criteria criteria = videoClipExample.createCriteria();
        if(req.getProjectId() != null) {
            criteria.andProjectIdEqualTo(req.getProjectId());
        }
        if(req.getClipId() != null) {
            criteria.andIdEqualTo(req.getClipId());
        }
        videoClipExample.setOrderByClause("sort");
        List<VideoClip> videoClips = videoClipMapper.selectByExample(videoClipExample);
        List<VideoClipDTO> videoClipDTOS =  VideoClipUtils.do2DTO(videoClips);
        setVideoClipsAndTags(videoClipsRes, videoClipDTOS);
        Integer totalTime = 0;
        for(VideoClipDTO videoClipDTO: videoClipDTOS) {
            setVideoClipDetails(videoClipDTO);
            List<SubtitlesCutDTO> subtitlesCutDTOList = videoClipDTO.getSubtitlesCutDTOList();
            StringBuilder stringBuilder = new StringBuilder();
            for(SubtitlesCutDTO subtitlesCutDTO: subtitlesCutDTOList) {
                SubtitlesDTO subtitlesDTO = subtitlesService.getSubtitlesById(subtitlesCutDTO.getSubtitlesId());
                if(subtitlesDTO != null) {
                    stringBuilder.append(subtitlesDTO.getContent());
                }
                totalTime += subtitlesCutDTO.getDuration();
            }
            videoClipDTO.setSubtitles(stringBuilder.toString());
        };
        videoClipsRes.setTotalTime(totalTime);
        return videoClipsRes;
    }

    /**
     * 获取视频片段，包括标签、详情等信息
     * @param clipId
     * @return
     */
    public VideoClipDTO getVedioClipById(Long clipId) {
        GetVideoClipsReq getVideoClips = new GetVideoClipsReq();
        getVideoClips.setClipId(clipId);
        List<VideoClipDTO> videoClipDTOS = getVideoClips(getVideoClips).getVideoClipList();
        if(CollectionUtils.isEmpty(videoClipDTOS)) {
            return null;
        }
        VideoClipDTO videoClipDTO = videoClipDTOS.get(0);
        setVideoClipDetails(videoClipDTO);
        return videoClipDTO;
    }


    private List<VideoClip> getVideoClips(List<Long> clipIds) {
        VideoClipExample videoClipExample = new VideoClipExample();
        videoClipExample.createCriteria().andIdIn(clipIds);
        return videoClipMapper.selectByExample(videoClipExample);
    }

    public List<VideoClipDTO> getVideoClipDTOs(List<Long> clipIds) {
        List<VideoClip> videoClips = getVideoClips(clipIds);
        return VideoClipUtils.do2DTO(videoClips);
    }

    private int deleteVideoClips(List<Long> clipIds) {
        VideoClipExample videoClipExample = new VideoClipExample();
        videoClipExample.createCriteria().andIdIn(clipIds);
        return videoClipMapper.deleteByExample(videoClipExample);
    }

    /**
     * 更新视频片段列表，主要用于顺序的调整。
     * @param videoClipDTOS
     * @return
     */
    public Boolean updateVedioClips(List<VideoClipDTO> videoClipDTOS) {
        if(CollectionUtils.isEmpty(videoClipDTOS)) {
            return true;
        }
        int res = videoClipMapperExt.batchUpdateByPrimaryKey(VideoClipUtils.dto2DO(videoClipDTOS));
        return res >= 1;
    }

    public List<VideoClipTagDTO> getVideoClipTagList() {
        List<VideoClipTagDTO> videoClipTagDTOS = new ArrayList<>();
        for(VideoClipTagEnum tagEnum: VideoClipTagEnum.values()) {
            VideoClipTagDTO videoClipTagDTO = new VideoClipTagDTO();
            videoClipTagDTO.setCode(tagEnum.getCode());
            videoClipTagDTO.setName(tagEnum.getValue());
            videoClipTagDTOS.add(videoClipTagDTO);
        }
        return videoClipTagDTOS;
    }

    /**
     * 视频片段打标接口
     * @param req
     * @return
     */
    @Transactional
    public Boolean tagVideoClip(TagVideoClipReq req) {
        Long videoClipId = req.getVideoClipId();
        VideoClipTagExample example = new VideoClipTagExample();
        example.createCriteria().andClipIdEqualTo(req.getVideoClipId());
        videoClipTagMapper.deleteByExample(example);
        for(String code:req.getTags()) {
            VideoClipTag tag = new VideoClipTag();
            tag.setClipId(videoClipId);
            tag.setTagCode(code);
            tag.setCreateTime(new Date());
            tag.setUpdateTime(new Date());
            int res = videoClipTagMapper.insert(tag);
            if(res <= 0) {
                throw new RuntimeException("add tag error");
            }
        }
        return true;
    }

    /**
     * 查询和设置视频片段的标签
     * @param videoClipDTOS
     * @return
     */
    private void setVideoClipsAndTags(GetVideoClipsRes res, List<VideoClipDTO> videoClipDTOS) {
        List<Long> videoClipIds = videoClipDTOS.stream().map(VideoClipDTO::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(videoClipIds)) {
            return;
        }
        VideoClipTagExample example = new VideoClipTagExample();
        example.createCriteria().andClipIdIn(videoClipIds);
        List<VideoClipTag> tags = videoClipTagMapper.selectByExample(example);
        Map<Long, List<VideoClipTagDTO>> videoClipTagsMap = new HashMap<>();
        Map<String, Integer> tagCountMap = new HashMap<>();
        for(VideoClipTag tag: tags) {
            VideoClipTagDTO videoClipTagDTO = VideoClipTagUtils.do2DTO(tag);
            List<VideoClipTagDTO> videoClipTagDTOS = videoClipTagsMap.get(tag.getClipId());
            if(CollectionUtils.isEmpty(videoClipTagDTOS)) {
                videoClipTagDTOS = new ArrayList<>();
                videoClipTagDTOS.add(videoClipTagDTO);
                videoClipTagsMap.put(tag.getClipId(), videoClipTagDTOS);
            }else {
                videoClipTagDTOS.add(videoClipTagDTO);
            }
            Integer tagCount = tagCountMap.get(tag.getTagCode());
            if(tagCount == null) {
                tagCount = 0;
            }
            tagCount++;
            tagCountMap.put(tag.getTagCode(), tagCount);
        }
        int countOfNoTags = 0;
        for(VideoClipDTO videoClipDTO: videoClipDTOS) {
            List<VideoClipTagDTO> clipTags = videoClipTagsMap.get(videoClipDTO.getId());
            if(CollectionUtils.isEmpty(clipTags)) {
                countOfNoTags++;
            }
            videoClipDTO.setTags(clipTags);
        }
        List<TagCountDTO> tagCountDTOS = new ArrayList<>();
        for(String tagCode: tagCountMap.keySet()) {
            TagCountDTO tagCountDTO = new TagCountDTO();
            tagCountDTO.setTagCode(tagCode);
            tagCountDTO.setTagName(VideoClipTagEnum.getName(tagCode));
            tagCountDTO.setCount(tagCountMap.get(tagCode));
            tagCountDTOS.add(tagCountDTO);
        }
        TagCountDTO tagCountDTO = new TagCountDTO();
        tagCountDTO.setTagCode("emptyTag");
        tagCountDTO.setTagName("无标签");
        tagCountDTO.setCount(countOfNoTags);
        tagCountDTOS.add(tagCountDTO);
        res.setTagCounts(tagCountDTOS);
        res.setVideoClipList(videoClipDTOS);
    }

    /**
     * 查询和设置视频片段的详情
     * @param videoClipDTO
     * @return
     */
    public void setVideoClipDetails(VideoClipDTO videoClipDTO) {
        VideoClipDetailsExample example = new VideoClipDetailsExample();
        example.createCriteria().andVideoClipIdEqualTo(videoClipDTO.getId());
        example.setOrderByClause("sort");
        List<VideoClipDetails> details = videoClipDetailsMapper.selectByExample(example);
        List<Long> subtitlesCutIds = details.stream().map(VideoClipDetails::getSubtitlesCutId).collect(Collectors.toList());
        SubtitlesCutExample subtitlesCutExample = new SubtitlesCutExample();
        subtitlesCutExample.createCriteria().andIdIn(subtitlesCutIds);
        List<SubtitlesCut> subtitlesCuts = subtitlesCutMapper.selectByExample(subtitlesCutExample);
        Map<Long, SubtitlesCutDTO> map = new HashMap<>();
        List<SubtitlesCutDTO> subtitlesCutDTOList = SubtitlesCutUtils.do2DTO(subtitlesCuts);
        for(SubtitlesCutDTO subtitlesCutDTO: subtitlesCutDTOList) {
            map.put(subtitlesCutDTO.getId(), subtitlesCutDTO);
        }
        List<SubtitlesCutDTO> sortedSubtitlesCutDTO = new ArrayList<>();
        subtitlesCutIds.forEach((item) -> {
            SubtitlesCutDTO subtitlesCutDTO = map.get(item);
            sortedSubtitlesCutDTO.add(subtitlesCutDTO);
            VideoMaterialDTO videoMaterialDTO = videoMaterialCache.get(subtitlesCutDTO.getVideoId());
            subtitlesCutDTO.setVideoMaterialDTO(videoMaterialDTO);
            //视频还是直播录制中的状态处理逻辑
            if(videoMaterialDTO != null && videoMaterialDTO.getVideoType().equals(VideoMaterialTypeEnum.LIVE.getValue()) &&
                    videoMaterialDTO.getStatus().equals(VideoMaterialCombineStatusEnum.PROCESSING.getValue())) {
                VideoMaterialSliceExample videoMaterialSliceExample = new VideoMaterialSliceExample();
                videoMaterialSliceExample.createCriteria().andVideoIdEqualTo(videoMaterialDTO.getId())
                        .andStartTsLessThan(subtitlesCutDTO.getCutStartTs());
                videoMaterialSliceExample.setOrderByClause("number desc");
                RowBounds rowBounds = new RowBounds(1, 1);
                List<VideoMaterialSlice> videoMaterialSlices = videoMaterialSliceMapper.selectByExampleWithRowbounds(videoMaterialSliceExample, rowBounds);
                if(CollectionUtils.isEmpty(videoMaterialSlices)) {
                    log.error("video material slices error, slice not fund, startTs={}, videoId={}", subtitlesCutDTO.getCutStartTs(), subtitlesCutDTO.getVideoId());
                }else{
                    subtitlesCutDTO.setVideoMaterialSliceDTO(VideoMaterialSliceUtils.do2DTO(videoMaterialSlices.get(0)));
                }
                SubtitlesDTO subtitlesDTO =  subtitlesService.getSubtitlesById(subtitlesCutDTO.getSubtitlesId());
                subtitlesCutDTO.setSubtitlesDTO(subtitlesDTO);
            }
        });
        videoClipDTO.setSubtitlesCutDTOList(sortedSubtitlesCutDTO);
    }

    private void deleteVideoClipTagByVideoClipId(Long videoClipId) {
        VideoClipTagExample videoClipTagExample = new VideoClipTagExample();
        videoClipTagExample.createCriteria().andClipIdEqualTo(videoClipId);
        videoClipTagMapper.deleteByExample(videoClipTagExample);
    }

    private void deleteVideoClipDetailsByVideoClipId(Long videoClipId) {
        VideoClipDetailsExample videoClipDetailsExample = new VideoClipDetailsExample();
        videoClipDetailsExample.createCriteria().andVideoClipIdEqualTo(videoClipId);
        videoClipDetailsMapper.deleteByExample(videoClipDetailsExample);
    }
}
