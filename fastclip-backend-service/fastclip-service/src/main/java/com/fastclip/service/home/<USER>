package com.fastclip.service.home;

import com.alibaba.excel.util.CollectionUtils;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dataobject.AccountWorksDO;
import com.fastclip.common.model.dataobject.OrderDO;
import com.fastclip.common.model.dto.DouyinAccountDTO;
import com.fastclip.common.model.dto.HomeData;
import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.utils.TimeUtils;
import com.fastclip.dao.mapper.OrdersMapper;
import com.fastclip.dao.mapper.OrderMapperExt;
import com.fastclip.dao.mapper.WorksMapper;
import com.fastclip.dao.mapper.WorksMapperExt;
import com.fastclip.dao.model.dataobject.WorksExample;
import com.fastclip.service.douyin.AccountService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HomeService {
    @Autowired
    WorksMapperExt worksMapperExt;

    @Autowired
    WorksMapper worksMapper;

    @Autowired
    OrdersMapper ordersMapper;

    @Autowired
    AccountService accountService;

    @Autowired
    OrderMapperExt orderMapperExt;

    public HomeData getHomeData(HomeDataReq homeDataReq) {
        UserDTO userDTO = SsoUserContext.getUser();
        HomeData homeData = new HomeData();
        getWorksDatas(homeDataReq, homeData);
        if(userDTO.getIsAdmin()) {
            getOrderDatas(homeDataReq, homeData);
        }
        return homeData;
    }

    /**
     * 获取作品数据
     * @param homeData
     */
    private void getWorksDatas(HomeDataReq homeDataReq, HomeData homeData) {
        UserDTO userDTO = SsoUserContext.getUser();
        Date date = TimeUtils.getNowDate();
        DouyinAccountReq req = new DouyinAccountReq();
        req.setOffset(homeDataReq.getOffset());
        req.setPageNum(homeDataReq.getPageNum());
        req.setPageSize(homeDataReq.getPageSize());
        PagebleRes<DouyinAccountDTO> douyinAccountDTOs = this.accountService.getDouyinAccountList(req);
        List<DouyinAccountDTO> douyinAccountDTOList = douyinAccountDTOs.getData();
        if (CollectionUtils.isEmpty(douyinAccountDTOList))
            return;
        List<String> phones = (List<String>)douyinAccountDTOList.stream().map(DouyinAccountDTO::getPhone).collect(Collectors.toList());
        WorksCountReq accountReq = new WorksCountReq();
        accountReq.setToday(date);
        accountReq.setPhones(phones);
        accountReq.setIsAdmin(userDTO.getIsAdmin());
        accountReq.setUserId(userDTO.getId());
        List<AccountWorksDO> publishedCountsToday = this.worksMapperExt.getPublishedCountsToday(accountReq);
        Map<String, Integer> publishedCountsMap = publishedCountsToday.stream().collect(Collectors.toMap(AccountWorksDO::getPhone, AccountWorksDO::getPublishedWorksCountToday));
        List<AccountWorksDO> worksCountsToday = this.worksMapperExt.getWorkCountsToday(accountReq);
        Map<String, Integer> worksCountsMap = worksCountsToday.stream().collect(Collectors.toMap(AccountWorksDO::getPhone, AccountWorksDO::getWorksCountToday));
        List<AccountWorksDO> unPublishedCounts = this.worksMapperExt.getUnPublishedCounts(accountReq);
        Map<String, Integer> unPublishedCountsMap = unPublishedCounts.stream().collect(Collectors.toMap(AccountWorksDO::getPhone, AccountWorksDO::getUnPublishedWorks));
        for (DouyinAccountDTO douyinAccountDTO : douyinAccountDTOList) {
            if (publishedCountsMap.get(douyinAccountDTO.getPhone()) != null) {
                douyinAccountDTO.setPublishedWorksCountToday(publishedCountsMap.get(douyinAccountDTO.getPhone()));
            } else {
                douyinAccountDTO.setPublishedWorksCountToday(Integer.valueOf(0));
            }
            if (worksCountsMap.get(douyinAccountDTO.getPhone()) != null) {
                douyinAccountDTO.setWorksCountToday(worksCountsMap.get(douyinAccountDTO.getPhone()));
            } else {
                douyinAccountDTO.setWorksCountToday(Integer.valueOf(0));
            }
            if (unPublishedCountsMap.get(douyinAccountDTO.getPhone()) != null) {
                douyinAccountDTO.setUnPublishedWorks(unPublishedCountsMap.get(douyinAccountDTO.getPhone()));
                continue;
            }
            douyinAccountDTO.setUnPublishedWorks(Integer.valueOf(0));
        }
        homeData.setDouyinAccountDTOS(douyinAccountDTOList);
        WorksExample worksCountTodayExample = new WorksExample();
        WorksExample.Criteria criteria = worksCountTodayExample.createCriteria();
        criteria.andCreateTimeGreaterThan(date);
        if (!userDTO.getIsAdmin())
            criteria.andCreatorIdEqualTo(userDTO.getId());
        Long worksCountToday = this.worksMapper.countByExample(worksCountTodayExample);
        WorksExample worksPublishedTodayExample = new WorksExample();
        criteria = worksPublishedTodayExample.createCriteria();
        criteria.andPublishedTimeGreaterThan(date);
        if (!userDTO.getIsAdmin())
            criteria.andCreatorIdEqualTo(userDTO.getId());
        Long worksCountPublished = this.worksMapper.countByExample(worksPublishedTodayExample);
        WorksExample worksUnpublished = new WorksExample();
        criteria = worksUnpublished.createCriteria();
        criteria.andIsPublishedEqualTo(false);
        if (!userDTO.getIsAdmin())
            criteria.andCreatorIdEqualTo(userDTO.getId());
        Long worksCountUnpublished = this.worksMapper.countByExample(worksUnpublished);
        WorksExample worksPublishedThisMonthExample = new WorksExample();
        criteria = worksPublishedThisMonthExample.createCriteria();
        criteria.andIsPublishedEqualTo(true)
                .andPublishedTimeGreaterThanOrEqualTo(TimeUtils.getFirstDayOfThisMonth());
        if (!userDTO.getIsAdmin())
            criteria.andCreatorIdEqualTo(userDTO.getId());
        Long worksCountPublishedThisMonth = this.worksMapper.countByExample(worksPublishedThisMonthExample);
        WorksExample worksPublishedLastMonthExample = new WorksExample();
        criteria = worksPublishedLastMonthExample.createCriteria();
        criteria.andIsPublishedEqualTo(Boolean.valueOf(true))
                .andPublishedTimeGreaterThanOrEqualTo(TimeUtils.getFirstDayOfLastMonth())
                .andPublishedTimeLessThan(TimeUtils.getFirstDayOfThisMonth());
        if (!userDTO.getIsAdmin())
            criteria.andCreatorIdEqualTo(userDTO.getId());
        Long worksCountPublishedLastMonth = this.worksMapper.countByExample(worksPublishedLastMonthExample);
        WorksExample worksPublishedTotalExample = new WorksExample();
        criteria = worksPublishedTotalExample.createCriteria();
        criteria.andIsPublishedEqualTo(true);
        if (!userDTO.getIsAdmin())
            criteria.andCreatorIdEqualTo(userDTO.getId());
        Long worksCountPublishedTotal = this.worksMapper.countByExample(worksPublishedTotalExample);
        homeData.setUnPublishedWorks(worksCountUnpublished.intValue());
        homeData.setWorksCountToday(worksCountToday.intValue());
        homeData.setPublishedWorksCountToday(worksCountPublished.intValue());
        homeData.setPublishedWorksCountThisMonth(worksCountPublishedThisMonth.intValue());
        homeData.setPublishedWorksCountLastMonth(worksCountPublishedLastMonth.intValue());
        homeData.setPublishedWorksCountTotal(worksCountPublishedTotal.intValue());
    }

    /**
     * 获取订单数据
     * @param homeData
     */
    private void getOrderDatas(HomeDataReq homeDataReq, HomeData homeData) {
        GetOrderListReq req = new GetOrderListReq();

        req.setStartDate(TimeUtils.getNowDate());
        OrderDO orderDO = orderMapperExt.getOrdersCount(req);
        homeData.setTotalPayAmountToday(orderDO.getTotalPayAmount() == null?0:orderDO.getTotalPayAmount());
        homeData.setOrdersCountToday(orderDO.getOrderCount());

        req.setStartDate(TimeUtils.getNowDateDiff(7));
        orderDO = orderMapperExt.getOrdersCount(req);
        homeData.setTotalPayAmountThisMonth(orderDO.getTotalPayAmount() == null?0:orderDO.getTotalPayAmount());
        homeData.setOrdersCountThisMonth(orderDO.getOrderCount());

        req.setStartDate(TimeUtils.getFirstDayOfThisYear());
        orderDO = orderMapperExt.getOrdersCount(req);
        homeData.setTotalPayAmountThisYear(orderDO.getTotalPayAmount() == null?0:orderDO.getTotalPayAmount());
        homeData.setOrdersCountThisYear(orderDO.getOrderCount());
    }
}
