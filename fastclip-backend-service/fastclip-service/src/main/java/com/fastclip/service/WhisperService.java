package com.fastclip.service;

import com.fastclip.service.http.SubtitlesHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WhisperService {

    @Autowired
    ProcessService processService;

    @Autowired
    SubtitlesHttpUtils httpUtils;

//    public void fetchSrt(String inputVideoPath){
//        try {
//            ProcessBuilder process1 = newFetchSrtProcessBuilder(inputVideoPath);
//            processService.printProcessExecResult(process1.start());
//        }catch (Exception e) {
//            log.error("fetch srt error", e);
//        }
//    }

    public void fetchSrt(String inputVideoPath, String outputVideoPath){
        try {
            httpUtils.getSubtitlesData(inputVideoPath, outputVideoPath);
        }catch (Exception e) {
            log.error("fetch subtitles error", e);
            throw new RuntimeException("fetch subtitles error");
        }
    }

    private ProcessBuilder newFetchSrtProcessBuilder(String inputFile) {
        return new ProcessBuilder(
                "whisper-cpp","-m",
                "/Users/<USER>/Documents/install/whisper-models/ggml-large-v3.bin",
                "-l" , "chinese","-osrt", "-f", inputFile);
    }
}
