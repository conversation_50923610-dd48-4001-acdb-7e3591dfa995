package com.fastclip.service.video;

import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.constant.VideoMaterialCombineStatusEnum;
import com.fastclip.common.constant.VideoMaterialStatusEnum;
import com.fastclip.common.constant.VideoMaterialTypeEnum;
import com.fastclip.common.model.context.SsoUserContext;
import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.CreateVideoReq;
import com.fastclip.common.model.request.UpdateVideoMaterialsReq;
import com.fastclip.common.model.request.VideoMaterialReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.dao.mapper.*;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.VideoUtils;
import com.fastclip.service.WhisperService;
import com.fastclip.service.douyin.LiveVideoSliceService;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VideoMaterialService {

    @Autowired
    VideoMaterialClipMapper videoMaterialClipMapper;

    @Autowired
    VideoMaterialMapper videoMaterialMapper;

    @Autowired
    LoadingCache<Long, SellerDTO> sellerCache;

    @Autowired
    LoadingCache<Long, ItemDTO> itemCache;

    @Autowired
    VideoSubtitlesFetchService videoSubtitlesFetchService;

    @Autowired
    SubtitlesMapper subtitlesMapper;

    @Autowired
    WhisperService whisperService;

    @Autowired
    LiveVideoSliceService liveVideoSliceService;

    @Value("${ffmpeg.hlsTmpPath}")
    String hlsTmpPath;

    @Value("${video.materialTmpPath}")
    String materialTmpPath;

    @Autowired
    ItemOnLiveMapper itemOnLiveMapper;

    @Autowired
    VideoMaterialSliceService videoMaterialSliceService;

    @Autowired
    ItemMapper itemMapper;

    public PagebleRes<VideoMaterialDTO> getVideoMaterial(VideoMaterialReq req) {
        if(ItemTypeEnum.Live.getValue().equals(req.getItemType())) {
            return getVideoMaterialsFromLiveVideo(req);
        }else {
            return getVideoMaterials(req);
        }
    }

    private PagebleRes<VideoMaterialDTO> getVideoMaterialsFromLiveVideo(VideoMaterialReq req) {
        ItemDTO itemDTO = itemCache.get(req.getItemId());
        Date createDateTime = itemDTO.getCreateTime();
        SimpleDateFormat simpleDateFormat =  new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = simpleDateFormat.format(createDateTime);
        VideoMaterialExample videoMaterialExample = new VideoMaterialExample();
        VideoMaterialExample.Criteria criteria = videoMaterialExample.createCriteria();
        criteria.andSellerIdEqualTo(req.getSellerId());
        criteria.andVideoNameEqualTo(dateStr);

        RowBounds rowBounds = new RowBounds(0,1);
        videoMaterialExample.setOrderByClause("start_date desc");
        List<VideoMaterial> videoMaterialClips =videoMaterialMapper.selectByExampleWithRowbounds(videoMaterialExample,rowBounds);
        List<VideoMaterialDTO> videoMaterialDTOS = VideoUtils.materialDo2DTOs(videoMaterialClips);
        for(VideoMaterialDTO videoMaterialDTO: videoMaterialDTOS) {
            videoMaterialDTO.setSeller(sellerCache.get(videoMaterialDTO.getSellerId()));
            if(VideoMaterialTypeEnum.LIVE.getValue().equals(videoMaterialDTO.getVideoType()) &&
                    VideoMaterialStatusEnum.PROCESSING.getValue().equals(videoMaterialDTO.getStatus())) {
                List<VideoMaterialSliceDTO> videoMaterialSliceDTOS = videoMaterialSliceService.
                        getVideoMaterialSlicesByVideoId(videoMaterialDTO.getId());
                videoMaterialDTO.setVideoMaterialSliceDTOS(videoMaterialSliceDTOS);
            }
        }
        PagebleRes<VideoMaterialDTO> pagebleRes = new PagebleRes();
        Long count = videoMaterialMapper.countByExample(videoMaterialExample);
        pagebleRes.setData(videoMaterialDTOS);
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal(count.intValue());
        return pagebleRes;
    }

    private PagebleRes<VideoMaterialDTO> getVideoMaterials(VideoMaterialReq req) {
        VideoMaterialExample videoMaterialExample = new VideoMaterialExample();
        VideoMaterialExample.Criteria criteria = videoMaterialExample.createCriteria();
        if(req.getSellerId() != null) {
            criteria.andSellerIdEqualTo(req.getSellerId());
        }
        if(req.getStartDate() != null) {
            criteria.andStartDateEqualTo(req.getStartDate());
        }

        if(req.getItemId() != null) {
            List<Long> videoIds = null;
            VideoMaterialClipExample videoMaterialClipExample = new VideoMaterialClipExample();
            videoMaterialClipExample.createCriteria().andItemIdEqualTo(req.getItemId());
            List<VideoMaterialClip> videoMaterialClipDTOs = videoMaterialClipMapper.selectByExample(videoMaterialClipExample);
            if(!CollectionUtils.isEmpty(videoMaterialClipDTOs)) {
                videoIds = videoMaterialClipDTOs.stream().map(VideoMaterialClip::getVideoId).collect(Collectors.toList());
            }
            if(!CollectionUtils.isEmpty(videoIds)) {
                criteria.andIdIn(videoIds);
            }
        }
        if(req.getPageSize() == null) {
            req.setPageSize(10);
        }
        if(req.getPageNum() == null) {
            req.setPageNum(1);
        }
        RowBounds rowBounds = new RowBounds((req.getPageNum() - 1) * req.getPageSize(), req.getPageSize());
        videoMaterialExample.setOrderByClause("create_time desc");
        List<VideoMaterial> videoMaterialClips =videoMaterialMapper.selectByExampleWithRowbounds(videoMaterialExample,rowBounds);
        List<VideoMaterialDTO> videoMaterialDTOS = VideoUtils.materialDo2DTOs(videoMaterialClips);
        for(VideoMaterialDTO videoMaterialDTO: videoMaterialDTOS) {
            videoMaterialDTO.setSeller(sellerCache.get(videoMaterialDTO.getSellerId()));
            if(VideoMaterialTypeEnum.LIVE.getValue().equals(videoMaterialDTO.getVideoType()) &&
                    VideoMaterialStatusEnum.PROCESSING.getValue().equals(videoMaterialDTO.getStatus())) {
                List<VideoMaterialSliceDTO> videoMaterialSliceDTOS = videoMaterialSliceService.
                        getVideoMaterialSlicesByVideoId(videoMaterialDTO.getId());
                videoMaterialDTO.setVideoMaterialSliceDTOS(videoMaterialSliceDTOS);
            }
        }
        PagebleRes<VideoMaterialDTO> pagebleRes = new PagebleRes();
        Long count = videoMaterialMapper.countByExample(videoMaterialExample);
        pagebleRes.setData(videoMaterialDTOS);
        pagebleRes.setPageNum(req.getPageNum());
        pagebleRes.setPagesize(req.getPageSize());
        pagebleRes.setTotal(count.intValue());
        return pagebleRes;
    }

    /**
     * 创建视频素材
     * @param createVideoReq
     * @return
     */
    public Boolean createVideoMaterial(CreateVideoReq createVideoReq) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin() && !userDTO.getHasUploadPrevilege()) {
            throw new RuntimeException("你没有权限");
        }
        try {
            SellerDTO sellerDTO = sellerCache.get(createVideoReq.getSellerId());
            if(createVideoReq.getVideoTmpPath()!=null) {
                File file = new File(createVideoReq.getVideoTmpPath());
                String newFilePath = sellerDTO.getMaterialBasePath() + file.getName();
                Files.copy( Paths.get(createVideoReq.getVideoTmpPath()),  Paths.get(newFilePath), StandardCopyOption.REPLACE_EXISTING);
                createVideoReq.setVideoPath(file.getName());
            }
            createVideoReq.setVideoPath(sellerDTO.getMaterialBasePath() + createVideoReq.getVideoPath());
            checkVideoReq(createVideoReq);
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(createVideoReq.getVideoPath());
            grabber.start();
            VideoMaterial videoMaterial = new VideoMaterial();
            videoMaterial.setIsSubtitlesDone(false);
            videoMaterial.setStartDate(createVideoReq.getStartDate());
            SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
            videoMaterial.setVideoName(formater.format(createVideoReq.getStartDate()));
            videoMaterial.setPath(createVideoReq.getVideoPath());
            videoMaterial.setSellerId(createVideoReq.getSellerId());
            videoMaterial.setDuration((int)(grabber.getLengthInTime()/1000L));
            videoMaterial.setSize(0);
            videoMaterial.setSubtitlesBpTs(0);
            videoMaterial.setCreateTime(new Date());
            videoMaterial.setUpdateTime(new Date());
            videoMaterial.setStartTime(createVideoReq.getStartTime());
            videoMaterial.setVideoType(createVideoReq.getVideoType());
            grabber.stop();
            int res = videoMaterialMapper.insert(videoMaterial);
            if(VideoMaterialTypeEnum.DOWNLOAD.getValue().equals( createVideoReq.getVideoType())) {
                VideoMaterialExample videoMaterialExample = new VideoMaterialExample();
                videoMaterialExample.createCriteria().andSellerIdEqualTo(createVideoReq.getSellerId()).
                        andStartDateEqualTo(createVideoReq.getStartDate());
                videoMaterialExample.setOrderByClause("path");
                List<VideoMaterial> videoMaterials = videoMaterialMapper.selectByExample(videoMaterialExample);
                videoMaterials.forEach(v -> v.setStartTime(0));
                for (int i = 1; i < videoMaterials.size(); i++) {
                    videoMaterials.get(i).setStartTime(videoMaterials.get(i - 1).getDuration() + videoMaterials.get(i - 1).getStartTime());
                    videoMaterialMapper.updateByPrimaryKey(videoMaterials.get(i));
                }
            }
            if(VideoMaterialTypeEnum.SPLIT.getValue().equals( createVideoReq.getVideoType())) {
                Long itemId = createVideoReq.getItemId();
                if(createVideoReq.getItemId() == null) {
                    Item item = new Item();
                    item.setItemName(createVideoReq.getItemName());
                    item.setOutItemId(createVideoReq.getItemOutId());
                    item.setSellerId(createVideoReq.getSellerId());
                    item.setCreateTime(new Date());
                    item.setUpdateTime(new Date());
                    item.setShareUrl(createVideoReq.getShareUrl());
                    item.setCreatorId(SsoUserContext.getUser().getId());
                    item.setCreator(SsoUserContext.getUser().getUserName());
                    item.setIsAvailable(true);
                    item.setDes("");
                    item.setIsPresell(false);
                    item.setIsPublish(false);
                    item.setItemType(ItemTypeEnum.Import.getValue());
                    itemMapper.insert(item);
                    itemId = item.getId();
                }
                VideoMaterialClip videoMaterialClip = new VideoMaterialClip();
                videoMaterialClip.setStartTs(0);
                videoMaterialClip.setVideoId(videoMaterial.getId());
                videoMaterialClip.setCreateTime(new Date());
                videoMaterialClip.setUpdateTime(new Date());
                videoMaterialClip.setItemId(itemId);
                videoMaterialClip.setSellerId(createVideoReq.getSellerId());
                videoMaterialClipMapper.insert(videoMaterialClip);
            }
            Runnable task = () -> {
                // 异步执行字幕解析
                videoSubtitlesFetchService.fetchSubtitles(videoMaterial.getId());
            };
            new Thread(task).start();
            if (res > 0) {
                if(VideoMaterialTypeEnum.SPLIT.getValue().equals( createVideoReq.getVideoType()) &&
                        createVideoReq.getVideoTmpPath() != null) {
                    Files.delete(Paths.get(createVideoReq.getVideoTmpPath()));
                }
                return true;
            }
            return false;
        }catch (Exception e) {
            log.error("create video error", e);
            throw new RuntimeException("create video material error");
        }
    }
    private void checkVideoReq(CreateVideoReq createVideoReq) {
        VideoMaterialExample videoMaterialExample = new VideoMaterialExample();
        videoMaterialExample.createCriteria().andPathEqualTo(createVideoReq.getVideoPath());
        List<VideoMaterial> videoMaterials = videoMaterialMapper.selectByExample(videoMaterialExample);
        if(!CollectionUtils.isEmpty(videoMaterials)) {
            throw new RuntimeException("素材已经使用");
        }
    }

    /**
     * 更新视频素材
     * @param req
     * @return
     */
    public Boolean updateVideoMaterials(UpdateVideoMaterialsReq req) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        List<VideoMaterialDTO> materials = req.getVideoMaterialDTOS();
        if(CollectionUtils.isEmpty(materials)) {
            return false;
        }
        if(materials.size() == 1) {
            return true;
        }
        List<VideoMaterialDTO> sortedMaterials = materials.stream().sorted(new Comparator<VideoMaterialDTO>() {
            @Override
            public int compare(VideoMaterialDTO o1, VideoMaterialDTO o2) {
                return o1.getSort() - o2.getSort();
            }
        }).collect(Collectors.toList());
        sortedMaterials.get(0).setStartTime(0);
        for(int i=1; i<sortedMaterials.size(); i++) {
            sortedMaterials.get(i).setStartTime(sortedMaterials.get(i-1).getDuration() + sortedMaterials.get(i-1).getStartTime());
        }
        for(VideoMaterialDTO videoMaterialDTO: req.getVideoMaterialDTOS()) {
            VideoMaterial videoMaterial = VideoUtils.materialDto2Do(videoMaterialDTO);
            videoMaterialMapper.updateByPrimaryKey(videoMaterial);
        }
        return true;
    }



    /**
     * 创建视频素材
     * @param createVideoReq
     * @return
     */
    public VideoMaterialDTO createVideoMaterialFromLiveRoom(CreateVideoReq createVideoReq) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        try {
            VideoMaterial videoMaterial;
            if(createVideoReq.getVideoId() == null) {
                videoMaterial = new VideoMaterial();
                videoMaterial.setIsSubtitlesDone(false);
                videoMaterial.setStartDate(createVideoReq.getStartDate());
                SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
                videoMaterial.setVideoName(formater.format(createVideoReq.getStartDate()));
                videoMaterial.setPath(createVideoReq.getVideoPath());
                videoMaterial.setSellerId(createVideoReq.getSellerId());
                videoMaterial.setSize(0);
                videoMaterial.setSubtitlesBpTs(0);
                videoMaterial.setCreateTime(new Date());
                videoMaterial.setUpdateTime(new Date());
                videoMaterial.setVideoType(2);
                videoMaterial.setStatus(2);
                videoMaterial.setLiveRoomId(createVideoReq.getLiveRoomId());
                videoMaterial.setStartTime(createVideoReq.getStartTime());
                videoMaterial.setCombineStatus(VideoMaterialCombineStatusEnum.PROCESSING.getValue());
                videoMaterial.setDuration(0);
                videoMaterial.setLatestSliceMergedId(-1);
                videoMaterial.setSort(1);
                videoMaterialMapper.insert(videoMaterial);
            }else{
                videoMaterial = videoMaterialMapper.selectByPrimaryKey(createVideoReq.getVideoId());
            }
            Runnable task = () -> {
                // 异步执行合并小文件
                liveVideoSliceService.fetchVideoSliceSubtitlesAndMerge(videoMaterial.getId());
            };
            new Thread(task).start();
            return VideoUtils.materialDo2DTO(videoMaterial);
        }catch (Exception e) {
            log.error("create video error", e);
            return null;
        }
    }



    public void setVideoMaterialDone(Long videoId) {
        VideoMaterial videoMaterial = new VideoMaterial();
        videoMaterial.setId(videoId);
        videoMaterial.setStatus(VideoMaterialStatusEnum.DONE.getValue());
        videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
    }


    /**
     * 删除视频素材
     * @param videoMaterialDTO
     * @return
     */
    @Transactional
    public Boolean deleteVideoMaterial(VideoMaterialDTO videoMaterialDTO) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        videoMaterialMapper.deleteByPrimaryKey(videoMaterialDTO.getId());
        SubtitlesExample subtitlesExample = new SubtitlesExample();
        subtitlesExample.createCriteria().andVideoIdEqualTo(videoMaterialDTO.getId());
        subtitlesMapper.deleteByExample(subtitlesExample);
        VideoMaterialClipExample videoMaterialClipExample = new VideoMaterialClipExample();
        videoMaterialClipExample.createCriteria().andVideoIdEqualTo(videoMaterialDTO.getId());
        videoMaterialClipMapper.deleteByExample(videoMaterialClipExample);
        return true;

    }

    /**
     * 给视频素材标记场次
     * @return
     */
    @Transactional
    public Boolean flagVideoMaterialScene(VideoMaterialDTO videoMaterialDTO) {
        UserDTO userDTO = SsoUserContext.getUser();
        if(!userDTO.getIsAdmin()) {
            throw new RuntimeException("你没有权限");
        }
        if(videoMaterialDTO.getId() == null || videoMaterialDTO.getStartScene() == null) {
            throw new RuntimeException("参数错误");
        }
        VideoMaterial videoMaterial = VideoUtils.materialDto2Do(videoMaterialDTO);
        videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
        return true;

    }

    public VideoMaterialDTO getVideoMaterialById(Long id) {
        VideoMaterial videoMaterial = videoMaterialMapper.selectByPrimaryKey(id);
        VideoMaterialDTO videoMaterialDTO = VideoUtils.materialDo2DTO(videoMaterial);
        if(VideoMaterialTypeEnum.LIVE.getValue().equals(videoMaterial.getVideoType()) &&
                VideoMaterialStatusEnum.PROCESSING.getValue().equals(videoMaterial.getStatus())) {
            List<VideoMaterialSliceDTO> videoMaterialSliceDTOS = videoMaterialSliceService.getVideoMaterialSlicesByVideoId(id);
            videoMaterialDTO.setVideoMaterialSliceDTOS(videoMaterialSliceDTOS);
        }
        return videoMaterialDTO;
    }


    public VideoMaterialDTO getVideoMaterial(Long sellerId, Date startDate, Integer startTs) {
        VideoMaterialExample videoMaterialExample = new VideoMaterialExample();
        VideoMaterialExample.Criteria criteria = videoMaterialExample.createCriteria();
        criteria.andSellerIdEqualTo(sellerId);
        criteria.andStartDateEqualTo(startDate);
        criteria.andStartTimeLessThan(startTs);
        videoMaterialExample.setOrderByClause("start_time desc");
        List<VideoMaterial>  videoMaterials = videoMaterialMapper.selectByExample(videoMaterialExample);
        if(CollectionUtils.isEmpty(videoMaterials)) {
            return null;
        }
        VideoMaterialDTO videoMaterialDTO = VideoUtils.materialDo2DTO(videoMaterials.get(0));
        if(VideoMaterialTypeEnum.LIVE.getValue().equals(videoMaterialDTO.getVideoType()) &&
                VideoMaterialStatusEnum.PROCESSING.getValue().equals(videoMaterialDTO.getStatus())) {
            List<VideoMaterialSliceDTO> videoMaterialSliceDTOS = videoMaterialSliceService.
                    getVideoMaterialSlicesByVideoId(videoMaterialDTO.getId());
            videoMaterialDTO.setVideoMaterialSliceDTOS(videoMaterialSliceDTOS);
        }
        return videoMaterialDTO;
    }

    public VideoMaterialDTO getVideoMaterialByScene(Long sellerId, Date startDate, Integer scene) {
        VideoMaterialExample videoMaterialExample = new VideoMaterialExample();
        VideoMaterialExample.Criteria criteria = videoMaterialExample.createCriteria();
        criteria.andSellerIdEqualTo(sellerId);
        criteria.andStartDateEqualTo(startDate);
        criteria.andStartSceneEqualTo(scene);
        List<VideoMaterial>  videoMaterials = videoMaterialMapper.selectByExample(videoMaterialExample);
        if(!CollectionUtils.isEmpty(videoMaterials)) {
            return VideoUtils.materialDo2DTO(videoMaterials.get(0));
        }
        return null;
    }

    /**
     * 获取所有的素材文件
     * @param sellerId
     * @return
     */
    public List<VideoMaterialFileDTO> getVideoMaterialFileDTOs(Long sellerId) {
        SellerDTO sellerDTO = sellerCache.get(sellerId);
        File file = new File(sellerDTO.getMaterialBasePath());
        File[] subFiles = file.listFiles();
        List<File> fileList = Arrays.stream(subFiles).sorted(new Comparator<File>() {
            @Override
            public int compare(File o1, File o2) {
                return o1.getName().compareTo(o2.getName());
            }
        }).collect(Collectors.toList());
        List<VideoMaterialFileDTO> fileDTOS = new ArrayList<>();
        for(int i=fileList.size()-1; i>=0; i--) {
            File subFile = fileList.get(i);
            String fileName = subFile.getName();
            if (fileName.endsWith(".mp4") || fileName.endsWith(".ts")) {
                VideoMaterialFileDTO videoMaterialDTO = new VideoMaterialFileDTO();
                videoMaterialDTO.setFileName(fileName);
                fileDTOS.add(videoMaterialDTO);
            }
            if (fileDTOS.size() >= 20) {
                break;
            }
        }
        return fileDTOS;
    }

    /**
     * @param file
     * @return
     */
    public String uploadMaterial(MultipartFile file) {
        try{
            // 获取文件名
            String fileName = file.getOriginalFilename();
            String filePath = materialTmpPath + File.separator + System.currentTimeMillis() + "_" + fileName;
            // 构建文件保存的路径
            Path path = Paths.get(filePath);
            // 保存文件到服务器上的指定位置
            Files.write(path, file.getBytes());
            return filePath;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
//
//    public static void main(String[] args) {
//        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber("/Volumes/共享文件夹/douyin/xueer/雪儿AMAZING 上大新22号83020241230085400.ts");
//
//        try {
//            grabber.start();
//            Frame frame = grabber.grabImage();
//            int imageWidth = frame.imageWidth;
//            int imageHeight = frame.imageHeight;
//            FFmpegFrameRecorder fFmpegFrameRecorder = new FFmpegFrameRecorder("/Volumes/共享文件夹/douyin/xueer/test.mp4"
//                    , imageWidth, imageHeight, 2);
//            fFmpegFrameRecorder.setFormat("mp4");
//            fFmpegFrameRecorder.setVideoBitrate(grabber.getVideoBitrate());
//            fFmpegFrameRecorder.setFrameRate(grabber.getFrameRate());
//            fFmpegFrameRecorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
//            fFmpegFrameRecorder.start();
//            for(int i=0; i<1000; i++) {
//                frame = grabber.grab();
//                System.out.println(frame.imageWidth + " " + frame.imageHeight);
//                fFmpegFrameRecorder.record(frame);
//            }
//            fFmpegFrameRecorder.stop();
//            fFmpegFrameRecorder.release();
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    public static void main(String[] args) {
//        File[] subFiles = new File("/Users/<USER>/Downloads").listFiles();
//        List<File> fileList = Arrays.stream(subFiles).sorted(new Comparator<File>() {
//            @Override
//            public int compare(File o1, File o2) {
//                return o1.getName().compareTo(o2.getName());
//            }
//        }).collect(Collectors.toList());
//        List<VideoMaterialFileDTO> fileDTOS = new ArrayList<>();
//        for(int i=fileList.size()-1; i>=0; i--) {
//            File subFile = fileList.get(i);
//            String fileName = subFile.getName();
//            if(fileName.endsWith(".mp4") || fileName.endsWith(".ts")) {
//                VideoMaterialFileDTO videoMaterialDTO = new VideoMaterialFileDTO();
//                videoMaterialDTO.setFileName(fileName);
//                fileDTOS.add(videoMaterialDTO);
//            }
//            System.out.println(fileName);
//            if(fileDTOS.size() >= 20) {
//                break;
//            }
//        }
//    }
}
