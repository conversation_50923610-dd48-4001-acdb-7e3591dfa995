package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.douyin.DouyinService;
import com.fastclip.service.item.ItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("item")
public class ItemController {

    @Autowired
    ItemService itemService;

    @Autowired
    DouyinService douyinService;

    @PostMapping("searchItems")
    public RequestTemplate.Response<PagebleRes<ItemDTO>> searchItems(@RequestBody ItemReq req){
        return new RequestTemplate().doRequest(() -> itemService.getItemList(req));
    }

    @PostMapping("getTopItems")
    public RequestTemplate.Response<List<ItemDTO>> getItems(@RequestBody ItemReq req){
        return new RequestTemplate().doRequest(() -> itemService.getTopItems(req));
    }

    @PostMapping("deleteItem")
    public RequestTemplate.Response<Boolean> deleteItems(@RequestBody DeleteItemReq req){
        return new RequestTemplate().doRequest(() -> itemService.deleteItem(req));
    }

    @PostMapping("updateItem")
    public RequestTemplate.Response<Boolean> updateItems(@RequestBody UpdateItemReq req){
        return new RequestTemplate().doRequest(() -> itemService.updateItem(req));
    }

    @PostMapping("uploadItems")
    public RequestTemplate.Response<List<ItemDTO>> uploadItems(@RequestParam("file") MultipartFile file){
        return new RequestTemplate().doRequest(() -> itemService.uploadItems(file));
    }

    @PostMapping("addItems")
    public RequestTemplate.Response<Integer> addItems(@RequestBody AddItemReq req){
        return new RequestTemplate().doRequest(() -> itemService.addItems(req));
    }

    @PostMapping("justifyPromotion")
    public RequestTemplate.Response<Boolean> justifyItemPromotion(@RequestBody ItemJustifyPromotionReq req){
        return new RequestTemplate().doRequest(() -> douyinService.justifyItemPromotion(req));
    }

    @PostMapping("startVideoGrounding")
    public RequestTemplate.Response<String> startVideoGrounding(@RequestBody VideoGroundingReq req){
        return new RequestTemplate().doRequest(() -> itemService.startVideoGrounding(req));
    }
}
