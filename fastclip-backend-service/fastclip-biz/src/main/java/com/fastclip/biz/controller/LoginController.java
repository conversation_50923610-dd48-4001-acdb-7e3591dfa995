package com.fastclip.biz.controller;

import com.fastclip.common.model.request.InsertMusicReq;
import com.fastclip.common.model.request.LoginReq;
import com.fastclip.common.model.request.LogoutReq;
import com.fastclip.common.model.response.LoginRes;
import com.fastclip.common.model.response.LogoutRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.auth.LoginService;
import com.fastclip.service.workbench.MusicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("auth")
public class LoginController {
    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    LoginService loginService;

    @PostMapping("login")
    public RequestTemplate.Response<LoginRes> login(@RequestBody LoginReq req) {
        return requestTemplate.doRequest(() -> loginService.login(req));
    }

    @PostMapping("logout")
    public RequestTemplate.Response<LogoutRes> logout() {
        return requestTemplate.doRequest(() -> loginService.logout());
    }
}
