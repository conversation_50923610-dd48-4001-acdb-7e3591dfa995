package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.QRCodeAPIDTO;
import com.fastclip.common.model.dto.QRCodeDTO;
import com.fastclip.common.model.request.GetQRCodeReq;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.douyin.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("invite")
@Slf4j
public class InviteController {

    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    AccountService accountService;

    @PostMapping("getQRCode")
    public RequestTemplate.Response<QRCodeDTO> getQRCode(@RequestBody GetQRCodeReq req) {
        return requestTemplate.doRequest(() -> accountService.getQRCode(req));
    }

    @PostMapping("unbind")
    public RequestTemplate.Response<Boolean> unbind(@RequestBody GetQRCodeReq req) {
        return requestTemplate.doRequest(() -> accountService.unbindInvite(req));
    }
}
