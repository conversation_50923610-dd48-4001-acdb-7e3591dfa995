//package com.fastclip.biz.controller;
//
//import com.fastclip.common.model.request.ChanxuanGetAndCutFlipsRequest;
//import com.fastclip.service.chanxuan.ChanxuanFindClipsService;
//import com.fastclip.service.chanxuan.ChanxuanGetAudioSrtService;
//import com.fastclip.service.cut.AutoCutService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.bind.annotation.*;
//
//@RestController
//@RequestMapping("chanxuan")
//@Slf4j
//public class ChanxuanController {
//
//    @Autowired
//    ChanxuanFindClipsService chanxuanFindClipsService;
//
//    @Autowired
//    ChanxuanGetAudioSrtService chanxuanGetAudioSrtService;
//
//    @Autowired
//    AutoCutService autoCutService;
//
//    @GetMapping("getClips")
//    public void cup() {
//        chanxuanFindClipsService.getClipsFromChanxuan();
//    }
//
//    @GetMapping("getLatestClips")
//    public void getLatestItems(String authorId) {
//        chanxuanFindClipsService.getLatestClips(authorId);
//    }
//
//
//    @PostMapping("getClipsByProductIds")
//    public void getClipsByProductIds(@RequestBody ChanxuanGetAndCutFlipsRequest req) {
//        chanxuanFindClipsService.getClipsByProductId(req.getProductIds());
//    }
//
//    @PostMapping("getAudioSrt")
//    public void getAudioSrt(@RequestBody ChanxuanGetAndCutFlipsRequest req) {
//        if(CollectionUtils.isEmpty(req.getProductIds())) {
//            chanxuanGetAudioSrtService.fetchAudioSrt();
//        }else{
//            chanxuanGetAudioSrtService.fetchAudioSrtByProductIds(req.getProductIds());
//        }
//    }
//
//    @GetMapping("autoCutClipByProductId")
//    public void autCutClip(@RequestParam("productId") String productId) {
//        autoCutService.autoCut(productId);
//    }
//
//    @GetMapping("autoCutWithMultiVideosByProductId")
//    public void autCutClipWithmultiVideos(@RequestParam("productId") String productId) {
//        autoCutService.autoCutWithMultiVideos(productId);
//    }
//
//    @GetMapping("autoCutAllClip")
//    public void autoCutAllClip(@RequestParam("cxAccount") String cxAccount) {
//        autoCutService.autoCutByAccount(cxAccount);
//    }
//
//    @GetMapping("cleanClips")
//    public void cleanClips() {
//        chanxuanFindClipsService.cleanClips();
//    }
//
//    @GetMapping("cleanItems")
//    public void cleanItems() {
//        chanxuanFindClipsService.cleanItems();
//    }
//
//    @GetMapping("autoCutByVideoPath")
//    public void cup(@RequestParam("videoPath") String videoPath) {
//        chanxuanFindClipsService.autoCutByVidePath(videoPath);
//    }
//
//}
