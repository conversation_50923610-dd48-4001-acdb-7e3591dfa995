package com.fastclip.biz.controller;

import com.alibaba.fastjson.JSON;
import com.fastclip.common.constant.VideoOrAudioTypeEnum;
import com.fastclip.common.model.request.VideoPlayReq;
import com.fastclip.service.video.VideoPlayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@ServerEndpoint("/videoPlay")
@Component
@Slf4j
public class VideoPlayWebSocket {

    private static VideoPlayService videoPlayService;

    static Map<String, Session> map = new ConcurrentHashMap<String,Session>();

    Session session;

    @Autowired
    public void setVideoPlayService(VideoPlayService videoPlayService) {
        VideoPlayWebSocket.videoPlayService = videoPlayService;
    }


    @OnOpen
    public void open(Session session, @PathParam("type") String type) {
        log.info("session id:" + session.getId() + " 连接打开！");
        map.put(session.getId(),session);
        this.session = session;
    }

    @OnClose
    public void close(Session session) {
        try {
            log.info("session id:" + session.getId() + " 连接关闭！");
            session.close();
            map.remove(session.getId());
        }catch (Exception e) {
            log.error("session id:" + session.getId() + " 关闭异常！", e);
        }
    }

    @OnError
    public void onError(Throwable error) {

    }

    @OnMessage
    public void onMessage(String message, Session session) {
        VideoPlayReq req = JSON.parseObject(message, VideoPlayReq.class);
//        if(VideoOrAudioTypeEnum.VIDEO.getCode().equals(req.getVideoOrAudio())) {
//            videoPlayService.playVideo(session, req);
//        }else{
//            videoPlayService.playAudio(session, req);
//        }
        videoPlayService.playVideo(session, req);
    }
}
