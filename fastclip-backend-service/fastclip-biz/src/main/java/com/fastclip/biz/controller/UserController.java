package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.UserDTO;
import com.fastclip.common.model.request.LoginReq;
import com.fastclip.common.model.request.UserReq;
import com.fastclip.common.model.response.LoginRes;
import com.fastclip.common.model.response.LogoutRes;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.auth.LoginService;
import com.fastclip.service.user.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("user")
public class UserController {
    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    UserService userService;

    @GetMapping("current")
    public RequestTemplate.Response<UserDTO> current() {
        return requestTemplate.doRequest(() -> userService.getCurrentUser());
    }

    @PostMapping("getUsers")
    public RequestTemplate.Response<PagebleRes<UserDTO>> getUsers(@RequestBody UserReq req) {
        return requestTemplate.doRequest(() -> userService.getUsers(req));
    }


}
