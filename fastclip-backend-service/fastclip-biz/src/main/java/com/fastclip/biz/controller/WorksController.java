package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.works.WorksService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("works")
@Slf4j
public class WorksController {

    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    WorksService worksService;

    @PostMapping("createWorks")
    public RequestTemplate.Response<Boolean> createWorks(@RequestBody CreateWorksReq req) {
        return requestTemplate.doRequest(() -> worksService.createWorks(req));
    }

    @PostMapping("delete")
    public RequestTemplate.Response<Boolean> deleteWorks(@RequestBody DeleteWorksReq req) {
        return requestTemplate.doRequest(() -> worksService.deleteWorks(req));
    }

    @PostMapping("getWorks")
    public RequestTemplate.Response<PagebleRes<WorksDTO>> getWorks(@RequestBody GetWorksReq req) {
        return requestTemplate.doRequest(() -> worksService.getPageableWorks(req));
    }

    @PostMapping("publish")
    public RequestTemplate.Response<Boolean> publishWorks(@RequestBody PublishWorksReq req) {
        return requestTemplate.doRequest(() -> worksService.publishWorks(req));
    }

    @PostMapping("combine")
    public RequestTemplate.Response<Boolean> combine(@RequestBody CombineWorksReq req) {
        return requestTemplate.doRequest(() -> worksService.addToTaskList(req.getWorksId()));
    }


    @PostMapping("combineNow")
    public RequestTemplate.Response<Boolean> combineNow(@RequestBody CombineWorksReq req) {
        return requestTemplate.doRequest(() -> worksService.combine(req.getWorksId()));
    }


    @PostMapping("createWorksDesc")
    public RequestTemplate.Response<Boolean> createWorksDesc(@RequestBody GetWorksCountReq req) {
        return requestTemplate.doRequest(() -> worksService.createWorksDesc(req.getWorksId()));
    }

    @PostMapping("download")
    public ResponseEntity<Resource> download(@RequestBody DownloadWorksReq req) {
        return worksService.download(req.getWorksId());
    }

    @PostMapping("downloadWithoutAss")
    public ResponseEntity<Resource> downloadWithoutAss(@RequestBody DownloadWorksReq req) {
        return worksService.downloadWithoutAss(req.getWorksId());
    }

    @PostMapping("downloadCover")
    public ResponseEntity<Resource> downloadCover(@RequestBody DownloadWorksReq req) {
        return worksService.downloadCover(req.getWorksId());
    }

    @CrossOrigin(origins = "*", allowCredentials = "true")
    @GetMapping("downloadWithToken")
    public ResponseEntity<Resource> download(@RequestParam String token, @RequestParam Integer type, @RequestParam Long worksId) {
        return worksService.downloadWithToken(token, type, worksId);
    }
}
