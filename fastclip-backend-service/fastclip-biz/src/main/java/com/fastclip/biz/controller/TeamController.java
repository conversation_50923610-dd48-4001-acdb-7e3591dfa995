package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.TeamDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.dao.model.dataobject.Team;
import com.fastclip.dao.utils.TeamUtils;
import com.fastclip.service.douyin.DouyinService;
import com.fastclip.service.item.ItemService;
import com.fastclip.service.team.TeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("team")
public class TeamController {

    @Autowired
    TeamService teamService;


    @PostMapping("searchTeams")
    public RequestTemplate.Response<PagebleRes<TeamDTO>> searchTeams(@RequestBody TeamReq req){
        return new RequestTemplate().doRequest(() -> teamService.getTeamList(req));
    }

    @PostMapping("deleteTeam")
    public RequestTemplate.Response<Boolean> deleteTeam(@RequestBody DeleteTeamReq req){
        return new RequestTemplate().doRequest(() -> teamService.deleteTeam(req));
    }

    @PostMapping("updateTeam")
    public RequestTemplate.Response<Boolean> updateTeam(@RequestBody TeamDTO req){
        return new RequestTemplate().doRequest(() -> teamService.updateTeam(TeamUtils.dto2DO(req)));
    }

    @PostMapping("addTeam")
    public RequestTemplate.Response<Integer> addTeam(@RequestBody TeamDTO req){
        return new RequestTemplate().doRequest(() -> teamService.addTeam(TeamUtils.dto2DO(req)));
    }
}
