package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.GetCoverRes;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.item.ItemService;
import com.fastclip.service.project.ProjectService;
import com.fastclip.service.seller.SellerService;
import com.fastclip.service.subtitles.SubtitlesService;
import com.fastclip.service.video.VideoMaterialClipService;
import com.fastclip.service.video.VideoMaterialService;
import com.fastclip.service.workbench.WorkbenchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("workbench")
@Slf4j
public class WorkbenchController {

    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    WorkbenchService workbenchService;

    @Autowired
    SellerService sellerService;

    @Autowired
    ProjectService projectService;

    @Autowired
    ItemService itemService;

    @Autowired
    VideoMaterialClipService videoMaterialClipService;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    SubtitlesService subtitlesService;

    @PostMapping("getSellerList")
    public RequestTemplate.Response<PagebleRes<SellerDTO>> getSellerList(@RequestBody SellerReq req) {
        return requestTemplate.doRequest(() -> sellerService.getSellerList(req));
    }

    @PostMapping("createSeller")
    public RequestTemplate.Response<Boolean> getSellerList(@RequestBody SellerDTO sellerDTO) {
        return requestTemplate.doRequest(() -> sellerService.createSeller(sellerDTO));
    }

    @PostMapping("deleteSeller")
    public RequestTemplate.Response<Boolean> deleteSellerList(@RequestBody SellerDTO sellerDTO) {
        return requestTemplate.doRequest(() -> sellerService.deleteSeller(sellerDTO));
    }

    @PostMapping("updateSeller")
    public RequestTemplate.Response<Boolean> updateSellerList(@RequestBody SellerDTO sellerDTO) {
        return requestTemplate.doRequest(() -> sellerService.updateSeller(sellerDTO));
    }

    @PostMapping("getProjectList")
    public RequestTemplate.Response<PagebleRes<ProjectDTO>> getProjectList(@RequestBody ProjectReq req) {
        return requestTemplate.doRequest(() -> projectService.getProjectList(req));
    }

    @PostMapping("createProject")
    public RequestTemplate.Response<Boolean> createProject(@RequestBody ProjectDTO projectDTO) {
        return requestTemplate.doRequest(() -> projectService.createProject(projectDTO));
    }

    @PostMapping("deleteProject")
    public RequestTemplate.Response<Boolean> deleteProject(@RequestBody ProjectDTO projectDTO) {
        return requestTemplate.doRequest(() -> projectService.deleteProject(projectDTO.getId()));
    }

    @PostMapping("setCover")
    public RequestTemplate.Response<Boolean> setCover(@RequestBody SetCoverReq req) {
        return requestTemplate.doRequest(() -> projectService.setCover(req));
    }

    @PostMapping("getCover")
    public RequestTemplate.Response<GetCoverRes> setCover(@RequestBody GetCoverReq req) {
        return requestTemplate.doRequest(() -> projectService.getCover(req));
    }

    @PostMapping("undefined")
    public RequestTemplate.Response<Boolean> undefined() {
        return requestTemplate.doRequest(() -> true);
    }
}
