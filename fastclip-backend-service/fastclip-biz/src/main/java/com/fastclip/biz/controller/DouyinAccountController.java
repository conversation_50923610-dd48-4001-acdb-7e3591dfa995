package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.DouyinAccountDTO;
import com.fastclip.common.model.dto.DouyinAccountTypeDTO;
import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.request.DeleteItemReq;
import com.fastclip.common.model.request.DouyinAccountReq;
import com.fastclip.common.model.request.ItemReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.dao.model.dataobject.DouyinAccount;
import com.fastclip.service.douyin.AccountService;
import com.fastclip.service.item.ItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("douyinAccount")
public class DouyinAccountController {

    @Autowired
    AccountService accountService;

    @PostMapping("getAccounts")
    public RequestTemplate.Response<PagebleRes<DouyinAccountDTO>> searchAccount(@RequestBody DouyinAccountReq req){
        return new RequestTemplate().doRequest(() -> accountService.getDouyinAccountList(req));
    }

    @PostMapping("updateAccount")
    public RequestTemplate.Response<Boolean> updateAccount(@RequestBody DouyinAccount req){
        return new RequestTemplate().doRequest(() -> accountService.updateAccount(req));
    }

    @PostMapping("addAccount")
    public RequestTemplate.Response<Boolean> addAccount(@RequestBody DouyinAccount req){
        return new RequestTemplate().doRequest(() -> accountService.addAccount(req));
    }

    @PostMapping("deleteAccount")
    public RequestTemplate.Response<Boolean> deleteAccount(@RequestBody DouyinAccount req){
        return new RequestTemplate().doRequest(() -> accountService.deleteAccount(req.getId()));
    }

    @GetMapping("getTypes")
    public RequestTemplate.Response<List<DouyinAccountTypeDTO>> getTypes(){
        return new RequestTemplate().doRequest(() -> accountService.getTypes());
    }

}
