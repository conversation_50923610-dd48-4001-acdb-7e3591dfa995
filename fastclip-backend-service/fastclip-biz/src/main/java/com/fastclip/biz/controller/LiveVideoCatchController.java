package com.fastclip.biz.controller;

import com.fastclip.common.model.request.LiveVideoCatchReq;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.douyin.LiveVideoService;
import com.fastclip.service.video.VideoMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("live")
@Slf4j
public class LiveVideoCatchController {

    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    LiveVideoService hlsVideo;

    @Autowired
    VideoMaterialService videoMaterialService;

    @PostMapping("catchVideo")
    public RequestTemplate.Response<Boolean> catchLiveVideo(@RequestBody LiveVideoCatchReq req) {
        return requestTemplate.doRequest(() -> hlsVideo.create(req.getUrl(), req.getSellerId()));
    }

    @PostMapping("mergeVideo")
    public RequestTemplate.Response<Boolean> mergeLiveVideo(@RequestBody LiveVideoCatchReq req) {
        return requestTemplate.doRequest(() -> hlsVideo.create(req.getUrl(), req.getSellerId()));
    }
}
