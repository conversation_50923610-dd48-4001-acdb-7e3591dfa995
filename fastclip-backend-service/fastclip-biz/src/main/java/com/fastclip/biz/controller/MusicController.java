package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.WorksDTO;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.workbench.MusicService;
import com.fastclip.service.works.WorksService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("works")
@Slf4j
public class MusicController {

    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    MusicService musicService;

    @PostMapping("insert")
    public RequestTemplate.Response<Boolean> insetMusics(@RequestBody InsertMusicReq req) {
        return requestTemplate.doRequest(() -> musicService.insertMusics(req));
    }
}
