package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.AllianceDarenOrderDTO;
import com.fastclip.common.model.dto.OrdersDTO;
import com.fastclip.common.model.request.GetOrderListReq;
import com.fastclip.common.model.request.SyncOrderReq;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.douyin.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("order")
@Slf4j
public class OrderController {

    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    private OrderService orderService;

    @PostMapping("callback")
    public RequestTemplate.Response<Boolean> insertMusics(@RequestBody AllianceDarenOrderDTO allianceDarenOrderDTO) {
        return requestTemplate.doRequest(() -> orderService.insertOrder(allianceDarenOrderDTO.getContent()));
    }

    @PostMapping("sync")
    public RequestTemplate.Response<Boolean> sync(@RequestBody SyncOrderReq syncOrderReq) {
        return requestTemplate.doRequest(() -> orderService.syncOrder(syncOrderReq));
    }

    @PostMapping("getOrderList")
    public RequestTemplate.Response<PagebleRes<OrdersDTO>> getOrderList(@RequestBody GetOrderListReq req) {
        return requestTemplate.doRequest(() -> orderService.getOrderList(req));
    }
}
