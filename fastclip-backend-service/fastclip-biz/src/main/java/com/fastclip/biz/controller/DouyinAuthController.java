package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.DouyinAuthReq;
import com.fastclip.common.model.dto.HomeData;
import com.fastclip.common.model.request.HomeDataReq;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.douyin.DouyinService;
import com.fastclip.service.home.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("douyin")
public class DouyinAuthController {

    @Autowired
    DouyinService douyinService;

    @GetMapping("auth")
    public RequestTemplate.Response<Boolean> auth(@RequestParam("code") String code, @RequestParam("state") String state,
                                                  @RequestParam("scopes") String scopes) {
        return new RequestTemplate().doRequest(() ->  douyinService.auth(code, state, scopes));
    }
}
