//package com.fastclip.biz.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.fastclip.dao.conn.DouyinConnection;
//import com.fastclip.dao.mapper.DouyinMapper;
//import com.fastclip.service.FfmpegService;
//import QwenService;
//import com.fastclip.service.WhisperService;
//import com.fastclip.service.douyin.DouyinService;
//import com.fastclip.common.model.ao.DouyinAO;
//import com.fastclip.common.model.ao.SearchRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("flipcut")
//@Slf4j
//public class FlipCut {
//    @Autowired
//    FfmpegService ffmpegService;
//
//    @Autowired
//    WhisperService whisperService;
//
//    @Autowired
//    QwenService qwenService;
//
//    @Autowired
//    DouyinConnection douyinConnection;
//
//    @Value("${douyin.sleep_duration}")
//    Integer sleepDuration;
//
//    @Value("${douyin.max_page}")
//    Integer max_page;
//
//    @Autowired
//    DouyinMapper douyinMapper;
//
//    @Autowired
//    DouyinService douyinService;
//
//
//    @RequestMapping("cup")
//    public void cup(@RequestParam("localPath") String localPath, @RequestParam("fileName") String fileName) {
//        try {
//            String orginalVedioPath = localPath + "/" + fileName+ ".mp4";
//            String audioPath = localPath + "/" + fileName + ".wav";
//            String srtPath = audioPath + ".srt";
//
////            ffmpegService.acodec(orginalVedioPath, audioPath);
////            whisperService.fetchSrt(audioPath);
//            qwenService.askSrtSummary(srtPath, srtPath);
//        }catch (Exception e) {
//            log.error("process error", e);
//        }
//    }
//
//    @PostMapping("searchCut")
//    public void cup(@RequestBody SearchRequest request) {
//        if(request.getSearchKey() == null) {
//            return;
//        }
//        try {
//            while (true) {
//                Boolean findAVideoMatchConditions = false;
//                int i = 0;
//                while (!findAVideoMatchConditions && i <= max_page) {
//                    List<DouyinAO> douyinAOS = douyinConnection.getData(request.getSearchKey(), i * 20, 20);
//                    if (CollectionUtils.isEmpty(douyinAOS)) {
//                        break;
//                    }
//                    for (DouyinAO douyinAO : douyinAOS) {
//                        log.info("start to process data:" + JSON.toJSONString(douyinAO));
//
//                        douyinService.process(douyinAO);
//                        log.info("finish to process data:" + JSON.toJSONString(douyinAO));
//                    }
//                    i++;
//                }
//
//                Thread.sleep(sleepDuration);
//            }
//        }catch (Exception e) {
//            log.error("download and cup error", e);
//        }
//    }
//}
