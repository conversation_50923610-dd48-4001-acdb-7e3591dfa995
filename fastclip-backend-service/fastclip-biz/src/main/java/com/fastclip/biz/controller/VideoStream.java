package com.fastclip.biz.controller;

import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

@Controller
public class VideoStream {
    @MessageMapping("/video-feed")
    @SendTo("/video/video-feed")
    public byte[] videoFeed(byte[] videoFrame) {
        return videoFrame;
    }
}
