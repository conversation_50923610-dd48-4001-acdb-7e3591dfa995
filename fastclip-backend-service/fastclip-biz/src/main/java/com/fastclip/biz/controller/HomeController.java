package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.HomeData;
import com.fastclip.common.model.request.HomeDataReq;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.home.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("home")
public class HomeController {

    @Autowired
    HomeService homeService;

    @PostMapping("getWorksCount")
    public RequestTemplate.Response<HomeData> getWorksCount(@RequestBody HomeDataReq homeDataReq) {
        return new RequestTemplate().doRequest(() ->  homeService.getHomeData(homeDataReq));
    }
}
