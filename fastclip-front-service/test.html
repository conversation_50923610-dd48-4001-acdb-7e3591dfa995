<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS Stream with Video.js</title>
    <link href="https://vjs.zencdn.net/7.14.3/video-js.css" rel="stylesheet" />
    <style>
        /* 自定义 Video.js 播放器样式 */
        .vjs-default-skin {
            font-family: "Arial", sans-serif;
            font-size: 16px;
            color: #FFF;
            background-color: #333;
            border: 2px solid #555;
            border-radius: 10px;
            overflow: hidden;
        }
 
        .vjs-control-bar {
            background: rgba(0, 0, 0, 0.7);
        }
 
        .vjs-big-play-button {
            background: rgba(255, 255, 255, 0.3);
            border: none;
            border-radius: 50%;
        }
 
        .vjs-progress-holder {
            background: rgba(255, 255, 255, 0.2);
        }
 
        .vjs-play-progress {
            background: #FFCC00; /* 进度条前景色 */
        }
 
        .vjs-load-progress {
            background: #666; /* 加载进度条色 */
        }
 
        .vjs-seek-to-live-control {
            display: none;
        }
 
        .video-js {
            width: 720px;  /* 固定宽度 */
            height: 405px; /* 固定高度，宽高比为16:9 */
            margin: 0 auto;  /* 水平居中 */
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
            display: block;  /* 确保播放器为块级元素 */
        }
 
        body {
            background-color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
    </style>
</head>
<body>
    <video id="my-video" class="video-js vjs-default-skin" controls autoplay>
                     // 修改此处的IP为你的IP即可
        <source src="rtmp://127.0.0.1:1935/live/stream" type="rtmp/flv">
    </video>
 
    <script src="https://vjs.zencdn.net/7.14.3/video.min.js"></script>
    <script>
        var video = document.getElementById('my-video');
        video.play()
    </script>
</body>
</html>