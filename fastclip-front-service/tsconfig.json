{"compilerOptions": {"outDir": "build/dist", "module": "esnext", "target": "es5", "lib": ["esnext", "dom"], "sourceMap": true, "baseUrl": ".", "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noUnusedLocals": true, "allowJs": true, "skipLibCheck": true, "experimentalDecorators": true, "strict": true, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"]}}, "include": ["mock/**/*", "src/**/*", "tests/**/*", "test/**/*", "__test__/**/*", "typings/**/*", "config/**/*", ".eslintrc.js", ".stylelintrc.js", ".prettierrc.js", "jest.config.js", "mock/*"], "exclude": ["node_modules", "build", "dist", "scripts", "src/.umi/*", "webpack", "jest"]}