{"private": true, "author": "8102 <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@umijs/max": "^4.3.19", "antd": "^5.4.0", "base-64": "^1.0.0", "cors": "^2.8.5", "moment": "^2.30.1", "react-datepicker": "^8.1.0", "react-datetime-picker": "^6.0.1", "react-scroll-to": "^3.0.0-beta.6", "socket.io-client": "^4.8.0", "umi-request": "^1.0.8", "yarn": "^1.22.22"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "husky": "^9", "less": "^4.2.0", "less-loader": "^12.2.0", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "react-dev-inspector": "^2.0.1", "typescript": "^5.0.3"}}