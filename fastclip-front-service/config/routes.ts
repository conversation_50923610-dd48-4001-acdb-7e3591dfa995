﻿import AuthComponent from "@/components/AuthComponent";

export default[
  {
    path: '/login',
    component: './Login',
    layout: false
  },
  {
    path: '/',
    component: '../layouts/BasicLayout',
    layout: false,
    routes: [
          {
              path: '/',
              redirect: '/home',
          },
          {
            path: '/workbench',
            redirect: '/workbench/projectList',
          },
          {
            path: '/home',
            name: 'home',
            component: './Home'
          },
          {
            path: '/workbench',
            name: 'workbench',
            component: './Workbench',
            routes: [
              {
                path: '/workbench/projectList',
                name: 'projectList',
                component: './Workbench/ProjectList',
              },
              {
                path: '/workbench/project',
                name: 'project',
                component: './Workbench/Project',
              },
              {
                path: '/workbench/worksList',
                name: 'project',
                component: './Workbench/WorksList',
              }
            ]
          },
          {
            path: '/personalCenter',
            name: 'personalCenter',
            component: './PersonalCenter',
          },
          {
            path: '/management',
            name: 'management',
            component: './Management',
            routes:[
              {
                path: '/management/douyinAccountList',
                name: 'douyinAccountList',
                component: './Management/DouyinAccountList',
              },{
                path: '/management/videoMaterialList',
                name: 'videoMaterialList',
                component: './Management/VideoMaterialList',
              },{
                path: '/management/sellerList',
                name: 'sellerList',
                component: './Management/SellerList',
              },
              {
                path: '/management/itemList',
                name: 'itemList',
                component: './Management/ItemList',
              },
              {
                path: '/management/orderList',
                name: 'orderList',
                component: './Management/OrderList',
              },
              {
                path: '/management/teamList',
                name: 'orderList',
                component: './Management/TeamList',
              }]
          }
    ]
  }
];

