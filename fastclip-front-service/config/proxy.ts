/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  //  dev环境
  dev: {
    '/api/fastclip': {
      // target: 'http://*************:8078/',
      // target: 'https://www.wanxiangqiepian.com:8002/',
       target: 'http://localhost:8078/',
      changeOrigin: true,
      ws: true,
      onProxyReqWs: (proxyReq: any, req: any, socket: any) => {
        console.log('WebSocket proxy request:', req.url);
      },
      onError: (err: any, req: any, res: any) => {
        console.error('Proxy error:', err);
      },
    },
  },
  //生产环境
  // dev: {
  //   '/api': {
  //     target: 'http://*************:9000/',
  //     changeOrigin: true,
  //     //pathRewrite: { '^/api': '/' },
  //   },
  //   '/out': {
  //     target: 'http://************:30789/',
  //     changeOrigin: true,
  //     //pathRewrite: { '^/api': '/' },
  //   },
  // },
};
