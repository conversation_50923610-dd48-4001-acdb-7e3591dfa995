declare namespace API {
  interface VideoRecord{
    id?: string;
    duration?: number;
    startDateTime?: string;
    endDateTime?: string;
    title?: string;
    starId?: string;
    starName?: string;
  }
  interface ResultVideoList{
    pageSize?: number;
    total?: number;
    pageNum?: number;
    dataList?: Array<VideoRecord>;
}
  
  interface VideoSearchVO{
    userId?: string;
    pageSize?: number;
    pageNum?: number;
    key?: number;
  }

  interface ProjectVO{
    id?: number;
    projectName?: string;
    itemId?: number;
    itemName?: string;
    sellerId: number;
    sellerName?: string;
    des?: string;
    picUrl?: string;
    status?: number;
    itemType?: number;
  }

  interface ProjectReq{
    id?: number;
    projectName?: string;
    itemIds?: number[];
    itemName?: string;
    sellerIds?: number[];
    status?: number;
    pageNum?: number;
    pageSize?: number;
  }

  interface ResultProjectList{
    pageSize?: number;
    total?: number;
    pageNum?: number;
    dataList?: Array<ProjectVO>;
}

  interface SellerVO{
    id?: number;
    sellerName?: string;
    des?: string;
  }

  interface SellerReq{
    id?: number;
    sellerName?: string;
  }

  interface ResultSellerList{
    pageSize?: number;
    total?: number;
    pageNum?: number;
    dataList?: Array<SellerVO>;
}

  interface ItemVO{
    id?: number;
    itemName?: string;
    outItemId?: string;
    sellerId?: number;
    seller?: SellerVO;
    des?: string;
    shareUrl?: string;
    isPresell?: boolean;
    isAvailable?: boolean;  
    isPublish?: Boolean;
  }
  
  interface ItemReq{
    itemIds?: number[];
    itemName?: string;
    outItemIds?: string[];
    sellerIds?: number[];
    des?: string;
    isPresell?: boolean;
    isAvailable?: boolean;  
    isPublish?: Boolean;
  }

  interface DeleteItemReq{
    itemIds?: number[];
  }

  interface ResultItemList{
    pageSize?: number;
    total?: number;
    pageNum?: number;
    dataList?: Array<ItemVO>;
}
  interface CeateProjectReq{
    projectName?: string;
    itemId?: number;
    sellerId?: number;
  }

  interface VideoMaterialClipRecord{
    id?: number;
    itemId?: number;
    videoId?: number;
    duration?: number;
    startTs?: number;
    endTs?: number;
  }

  interface VideoMaterialClipReq{
    videoId?: number;
    itemId?: number;
  }

  interface VideoMaterialRecord{
    id?: number;
    sellerId: number;
    videoId?: number;
    videoName?: string;
    duration?: number;
    path?: string;
    seller?: SellerVO;
    startDate?:string;
    startTime?: number;
  }

  interface VideoMaterialReq{
    sellerId?: number;
    itemId?: number;
    duration?: number;
    path?: string;
    startDate: string;
  }

  interface searchSubtitlesReq{
    projectId?: number;
    videoId?: number;
    startTs?: number;
    endTs?: number;
  }

  interface SubtitlesRecord{
    id: number;
    videoId: number;
    startTs: number;
    endTs: number;
    content: string;
    duration?: number;
    isAdded?: boolean;
  }


  interface addSubtitlesReq{
    projectId?: number;
    subtitlesId?: number;
  }

  interface removeSubtitlesReq{
    projectId?: number;
    subtitlesId?: number;
    videoClipId?: number;
  }

  interface searchSubtitlesCutReq{
    projectId?: number;
  }

  interface SubtitlesCutRecord{
    id: number;
    projectId: number;
    videoId: number;
    subtitlesId: number;
    cutStartTs: number;
    cutEndTs: number;
    content: string;
    cutStartContent: number;
    cutEndContent: number;
    duration?: number;
  }

  interface VideoClipRecord{
    id: number;
    projectId: number;
    startSubtitlesCutId: number;
    endSubtitlesCutId: number;
    startSubtitlesId: number;
    endSubtitlesId: number;
    sort: number;
    videoId: number;
    subtitles: any;
    duration: number;
    subtitlesCutCount: number;
    tags: videoClipTagRecord[];
  }

  interface getVideoClipsReq{
    projectId?: number;
  }

  interface mergeVideoClipReq{
    projectId?: number;
    videoClipIds?: number[]
  }

  interface deleteVideoClipReq{
    projectId?: number;
    videoClipId?: number;
  }

  interface updateVideoClipsReq{
    projectId?: number;
    videoClipDTOs?: VideoClipRecord[];
  }

  interface CutDataRecord{
    videoClipId: number;
    projectId: number;
    index: number;
    startOffset: number;
    endOffset: number;
    preContent: string;
  }

  interface divPosition{
    top?: number;
    left?: number;
  }

  interface playVideoReq{
    projectId?: number;
    playType?: number;
    videoClipId?: number;
    subtitlesId?: number;
    videoId?: number;
    worksId?: number;
    startTs?: number;
  }

  interface videoClipTagRecord{
    name?: name;
    code?: code;
  }
  
  interface tagVideoClipReq{
    videoClipId?: number;
    tags?: string[];
  }

  interface WorksRecordVO{
    id?: number;
    projectId?: number;
    itemId?: number;
    itemName?: string;
    sellerId?: number;
    sellerName?: string;
    duration?: number;
    isComposed?: boolean;
    isPublished?: boolean;
    videoPath?: string;
    sellerName?: string;
    createTime?: string;
    updateTime?: string;
  }

  interface ResultWorksList{
    pageSize?: number;
    total?: number;
    pageNum?: number;
    dataList?: Array<WorksRecordVO>;
  }

  interface CoverRecord{
    data?: string;
  }
}
