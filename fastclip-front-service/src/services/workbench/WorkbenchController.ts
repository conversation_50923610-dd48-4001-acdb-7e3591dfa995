import request from '@/utils/request';

export async function getSellerList(
    body: API.SellerReq
){
    return request<API.ResultSellerList> ('/api/fastclip/workbench/getSellerList', {
        method: 'POST',
        data: body
    })
}

export async function createSeller(
    body: API.SellerVO
){
    return request<Boolean> ('/api/fastclip/workbench/createSeller', {
        method: 'POST',
        data: body
    })
}

export async function deleteSeller(
    body: API.SellerVO
){
    return request<Boolean> ('/api/fastclip/workbench/deleteSeller', {
        method: 'POST',
        data: body
    })
}

export async function createVideo(
    body: any
){
    return request<Boolean> ('/api/fastclip/video/createVideo', {
        method: 'POST',
        data: body
    })
}

export async function createLiveVideo(
    body: any
){
    return request<Boolean> ('/api/fastclip/live/catchVideo', {
        method: 'POST',
        data: body
    })
}

export async function deleteVideo(
    body: API.VideoMaterialRecord
){
    return request<Boolean> ('/api/fastclip/video/deleteVideo', {
        method: 'POST',
        data: body
    })
}

export async function fetchSubtitles(
    body: API.VideoMaterialRecord
){
    return request<Boolean> ('/api/fastclip/video/fetchSubtitles', {
        method: 'POST',
        data: body
    })
}

export async function refetchSubtitles(
    body: API.VideoMaterialRecord
){
    return request<Boolean> ('/api/fastclip/video/refetchSubtitles', {
        method: 'POST',
        data: body
    })
}

export async function editSubtitles(
    body: any
){
    return request<Boolean> ('/api/fastclip/video/editSubtitles', {
        method: 'POST',
        data: body
    })
}

export async function getAllSellerList(
){
    return request<API.ResultSellerList> ('/api/fastclip/workbench/getSellerList', {
        method: 'POST',
        data: {}
    })
}

export async function getProjectList(
    body: API.ProjectReq
){
    return request<API.ResultProjectList> ('/api/fastclip/workbench/getProjectList', {
        method: 'POST',
        data: body
    })
}

export async function searchItems(
    body: API.ItemReq
){
    return request<API.ResultItemList> ('/api/fastclip/item/searchItems', {
        method: 'POST',
        data: body
    })
}

export async function getTopItems(
    body: API.ItemReq
){
    return request<Array<API.ItemVO>> ('/api/fastclip/item/getTopItems', {
        method: 'POST',
        data: body
    })
}

export async function deleteItem(
    body: API.DeleteItemReq
){
    return request<Boolean> ('/api/fastclip/item/deleteItem', {
        method: 'POST',
        data: body
    })
}

export async function updateItem(
    body: API.VideoMaterialReq
){
    return request<Boolean> ('/api/fastclip/item/updateItem', {
        method: 'POST',
        data: body
    })
}

export async function uploadItems(
    body: any
){
    return request<Boolean> ('/api/fastclip/item/uploadItems', {
        method: 'POST',
        data: body
    })
}

export async function addItems(
    body: any
){
    return request<number> ('/api/fastclip/item/addItems', {
        method: 'POST',
        data: body
    })
}

export async function getHomeData(){
    return request<any> ('/api/fastclip/home/<USER>', {
        method: 'POST',
        data: {}
    })
}


export async function getDouyinAccounts(
    body: any
){
    return request<number> ('/api/fastclip/douyinAccount/getAccounts', {
        method: 'POST',
        data: body
    })
}

export async function addAccount(
    body: any
){
    return request<number> ('/api/fastclip/douyinAccount/addAccount', {
        method: 'POST',
        data: body
    })
}

export async function updateAccount(
    body: any
){
    return request<number> ('/api/fastclip/douyinAccount/updateAccount', {
        method: 'POST',
        data: body
    })
}

export async function createProjectApi(
    body: API.CeateProjectReq
){
    return request<Boolean> ('/api/fastclip/workbench/createProject', {
        method: 'POST',
        data: body
    })
}

export async function getVideoMaterialClips(
    body: API.VideoMaterialClipReq
){
    return request<API.VideoMaterialClipRecord> ('/api/fastclip/video/getVideoMaterialClips', {
        method: 'POST',
        data: body
    })
}

export async function getVideoMaterials(
    body: API.VideoMaterialReq
){
    return request<API.VideoMaterialRecord> ('/api/fastclip/video/getVideoMaterials', {
        method: 'POST',
        data: body
    })
}

export async function updateVideoMaterials(
    body: any
){
    return request ('/api/fastclip/video/updateVideoMaterials', {
        method: 'POST',
        data: body
    })
}

export async function searchSubtitles(
    body: API.searchSubtitlesReq
){
    return request<API.SubtitlesRecord> ('/api/fastclip/video/searchSubtitles', {
        method: 'POST',
        data: body
    })
}

export async function addSubtitlesToProject(
    body: API.addSubtitlesReq
){
    return request<API.VideoMaterialRecord> ('/api/fastclip/video/addSubtitlesToProject', {
        method: 'POST',
        data: body
    })
}

export async function searchSubtitlesCut(
    body: API.searchSubtitlesCutReq
){
    return request<API.SubtitlesRecord> ('/api/fastclip/video/searchSubtitlesCuts', {
        method: 'POST',
        data: body
    })
}

export async function getWorksIdsOfSubtitlesClip(
    body: any
){
    return request<any> ('/api/fastclip/video/getWorksIdsOfSubtitlesClip', {
        method: 'POST',
        data: body
    })
}

export async function removeSubtitlesCut(
    body: API.removeSubtitlesReq
){
    return request<Boolean> ('/api/fastclip/video/removeSubtitlesFromProject', {
        method: 'POST',
        data: body
    })
}

export async function getVideoClips(
    body: API.getVideoClipsReq
){
    return request<API.VideoClipRecord> ('/api/fastclip/video/getVideoClips', {
        method: 'POST',
        data: body
    })
}

export async function mergeVideoClips(
    body: API.mergeVideoClipReq
){
    return request<Boolean> ('/api/fastclip/video/mergeVideoClips', {
        method: 'POST',
        data: body
    })
}

export async function splitVideoClip(
    body: API.deleteVideoClipReq
){
    return request<Boolean> ('/api/fastclip/video/splitVideoClip', {
        method: 'POST',
        data: body
    })
}

export async function updateVideoClips(
    body: API.updateVideoClipsReq
){
    return request<Boolean> ('/api/fastclip/video/updateVideoClips', {
        method: 'POST',
        data: body
    })
}

export async function flagVideoMaterialStartScene(
    body: any
){
    return request<Boolean> ('/api/fastclip/video/flagStartScene', {
        method: 'POST',
        data: body
    })
}

export async function getVideoClipTagList(){
        return request<API.videoClipTagRecord> ('/api/fastclip/video/getVideoClipTagList', {
        method: 'GET',
        data: {}
    })
}

export async function tagVideoClip(
    body: API.tagVideoClipReq
){
    return request<Boolean> ('/api/fastclip/video/tagVideoClip', {
    method: 'POST',
    data: body
})
}

export async function getWorks(
    body: any
){
    return request<API.ResultWorksList> ('/api/fastclip/works/getWorks', {
    method: 'POST',
    data: body
})
}


export async function deleteWorks(
    body: any
){
    return request<Boolean> ('/api/fastclip/works/delete', {
    method: 'POST',
    data: body
})
}

export async function publishWork(
    body: any
){
    return request<Boolean> ('/api/fastclip/works/publish', {
    method: 'POST',
    data: body
})
}

export async function createWorks(
    body: any
){
    return request<Boolean> ('/api/fastclip/works/createWorks', {
    method: 'POST',
    data: body
})
}

export async function combineWorks(
    body: any
){
    return request<Boolean> ('/api/fastclip/works/combine', {
    method: 'POST',
    data: body
})
}

export async function createWorksDesc(
    body: any
){
    return request<Boolean> ('/api/fastclip/works/createWorksDesc', {
    method: 'POST',
    data: body
})
}

export async function getCoverApi(
    body: any
){
    return request<API.CoverRecord> ('/api/fastclip/workbench/getCover', {
    method: 'POST',
    data: body
})
}

export async function setCoverApi(
    body: any
){
    return request<Boolean> ('/api/fastclip/workbench/setCover', {
    method: 'POST',
    data: body
})
}

export async function login(
    body: any
){
    return request<any> ('/api/fastclip/auth/login', {
    method: 'POST',
    data: body
})
}

export async function logout(
){
    return request<any> ('/api/fastclip/auth/logout', {
    method: 'POST'
})
}

export async function currentUser(
){
    return request<any> ('/api/fastclip/user/current', {
    method: 'Get'
})
}

export async function justifyItemPromotion(
    body: any
){
    return request<any> ('/api/fastclip/item/justifyPromotion', {
    method: 'POST',
    data: body
})
}


export async function downloadWorks(
    body: any
){
    return request<any> ('/api/fastclip/works/download', {
    method: 'POST',
    headers: {
        'Content-Type':'application/json;charset=UTF-8'
    },
    responseType: 'blob',
    data: body,
    getResponse:true
})
}

export async function downloadWorksWithOutAss(
    body: any
){
    return request<any> ('/api/fastclip/works/downloadWithoutAss', {
    method: 'POST',
    headers: {
        'Content-Type':'application/json;charset=UTF-8'
    },
    responseType: 'blob',
    data: body,
    getResponse:true
})
}


export async function downloadWorksCover(
    body: any
){
    return request<any> ('/api/fastclip/works/downloadCover', {
    method: 'POST',
    headers: {
        'Content-Type':'application/json;chartset=ISO-8859-1'
    },
    responseType: 'blob',
    data: body,
    getResponse:true
})
}

export async function deleteDouyinAccount(
    body: any
){
    return request<Boolean> ('/api/fastclip/douyinAccount/deleteAccount', {
        method: 'POST',
        data: body
    })
}

export async function deleteProject(
    body: any
){
    return request<Boolean> ('/api/fastclip/workbench/deleteProject', {
        method: 'POST',
        data: body
    })
}


export async function updateSeller(
    body: any
){
    return request<Boolean> ('/api/fastclip/workbench/updateSeller', {
        method: 'POST',
        data: body
    })
}

export async function getVideoMaterialFiles(
    body: any
){
    return request<Boolean> ('/api/fastclip/video/getVideoMaterialFiles', {
        method: 'POST',
        data: body
    })
}

export async function getQRCode(
    body: any
){
    return request<Boolean> ('/api/fastclip/invite/getQRCode', {
        method: 'POST',
        data: body
    })
}

export async function unbindInvite(
    body: any
){
    return request<Boolean> ('/api/fastclip/invite/unbind', {
        method: 'POST',
        data: body
    })
}


export async function getOrderList(
    body: any
){
    return request<any> ('/api/fastclip/order/getOrderList', {
        method: 'POST',
        data: body
    })
}

export async function syncOrder(
    body: any
){
    return request<any> ('/api/fastclip/order/sync', {
        method: 'POST',
        data: body
    })
}


export async function deleteTeam(
    body: any
){
    return request<any> ('/api/fastclip/team/deleteTeam', {
        method: 'POST',
        data: body
    })
}


export async function updateTeam(
    body: any
){
    return request<any> ('/api/fastclip/team/updateTeam', {
        method: 'POST',
        data: body
    })
}


export async function searchTeams(
    body: any
){
    return request<any> ('/api/fastclip/team/searchTeams', {
        method: 'POST',
        data: body
    })
}

export async function addTeam(
    body: any
){
    return request<any> ('/api/fastclip/team/addTeam', {
        method: 'POST',
        data: body
    })
}

export async function getDouyinAccountTypes(){
    return request<any> ('/api/fastclip/douyinAccount/getTypes', {
        method: 'Get',
        data: {}
    })
}

export async function getUsers(
    body: any
){
    return request<any> ('/api/fastclip/user/getUsers', {
        method: 'POST',
        data: body
    })
}