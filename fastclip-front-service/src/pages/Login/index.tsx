import React, { useState } from 'react';
import services from  '../../services/workbench';
import {message} from 'antd';
import './index.less'

 
const LoginForm = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');


  const {login} = services.WorkbenchController
 
  const handleSubmit = async (e:any) => {
    e.preventDefault();
    try {
      // 发送登录请求到服务器，这里使用模拟的API调用
      login(
        { userName:username, passwd: password }
      ).then((res) => {
        if(res.result.loginSuccess) {
          console.log('登录成功');
          localStorage.setItem('token', res.result.token)
          window.location.href = '/home'
        }else {
          message.error("登录失败！");
        }
      });
 
      // 处理登录成功的逻辑，如页面跳转或状态更新
      
    } catch (err:any) {
      setError(err.message);
    }
  };
 
  return (
    <div className='LoginContent'>
      <div className='Line'>
      <form onSubmit={handleSubmit}>
        <label htmlFor="username">
          用户名:
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
          />
        </label>
        <label htmlFor="password">
          密码:
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
        </label>
        <button type="submit">登录</button>
      </form>
      </div>
    </div>
  );
};
 
export default LoginForm;