import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-components';
import { EditableProTable, ModalForm, ProForm, ProFormSelect, ProFormText, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Form, Input, message, Modal, Select, Space, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less'
import services from  '../../../services/workbench';
import { getSellerList } from '@/services/workbench/WorkbenchController';

const DouyinAccountList: React.FC = () => {

  const editableFormRef = useRef<EditableFormInstance>();
  const {deleteDouyinAccount, getQRCode, unbindInvite, getDouyinAccountTypes, searchTeams} = services.WorkbenchController
  const [accountType, setAccountType] = useState()
  const [teamOptions, setTeamOptions] = useState()
  const [teamId, setTeamId] = useState()
  const [sellerOptions, setSellerOptions] = useState()
  const [sellerId, setSellerId] = useState()
  const [selfUserOptions, setSelfUserOptions] = useState()
  const [selfId, setSelfId] = useState()


  const qrCodeCanvas = useRef(null)
  const columns: ProColumns<any>[] = [
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 180,
      search: false
    },
    {
      title: '抖音名称',
      dataIndex: 'douyinName',
      width: 250    
    },
    {
      title: '抖音号',
      dataIndex: 'code',
      width: 250    
    },
    {
      title: '达人名称',
      dataIndex: 'seller_id',
      render: (_, record) => (
        <div>{record.sellerDTO.sellerName}</div>
      ),
      width: 200,
      renderFormItem: (item, { value, onChange }) =>  (
      <div>
        <Select
          options={sellerOptions}
          showSearch
          style= {{width : '200px', marginTop : '8px'}}
          onSearch={(sellerName) =>{loadSellerData(sellerName)}}
          onChange={(value) => {setSellerId(value)}}
          filterOption={false}
          defaultValue={sellerId}
        />
      </div>
      )
      
    },
    {
      title: '账号类型',
      dataIndex: 'type',
      width: 200,
      valueEnum: {
        1: {
          text: '个人号',
        },
        2: {
          text: '团队号',
        },
        3: {
          text: '自营号',
        },
      },
      request: async () => getDouyinAccountTypes().then(
        (res: any) => 
          res.result.map(item => {return {value: item.code, label: item.desc};}
        )
      ),
      fieldProps: (_, { rowIndex }) => {
        return {
          onSelect: (selectValue) => {
            // 每次选中重置参数
            setAccountType(selectValue);
            editableFormRef.current?.setRowData?.(rowIndex, { fraction: [] });
          },
        };
      },
    },
    {
      title: '团队',
      dataIndex: 'team_id',
      render: (_, record) => (
        <div>{
          accountType==2?record.teamDTO?record.teamDTO.name:"-":"-"
          }</div>
      ),
      width: 200,
      renderFormItem: (item, { value, onChange }) =>  (
      <div>
        <Select
          options={teamOptions}
          showSearch
          style= {{width : '200px', marginTop : '8px'}}
          onSearch={(teamName) =>{loadTeamData(teamName)}}
          onChange={(value) => {setTeamId(value)}}
          filterOption={false}
          defaultValue={accountType==2?teamId:""}
        />
      </div>
      )
      },
      {
        title: '自营剪辑手',
        dataIndex: 'self_id',
        render: (_, record) => (
          <div>{
            accountType==3?record.selfUserDTO?record.selfUserDTO.userName:"-":"-"
            }</div>
        ),
        width: 200,
        renderFormItem: (item, { value, onChange }) =>  (
        <div>
          <Select
            options={teamOptions}
            showSearch
            style= {{width : '200px', marginTop : '8px'}}
            onSearch={(teamName) =>{loadTeamData(teamName)}}
            onChange={(value) => {setTeamId(value)}}
            filterOption={false}
            defaultValue={accountType==2?teamId:""}
          />
        </div>
        ),
      editable: accountType==2?true:false
    },
    {
      title: '分润比例',
      key: 'showTime',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      editable: false
    },
    {
      title: '创建时间',
      key: 'showTime',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      editable: false
    },
    {
      title: '操作',
      width: 180,
      key: 'option',
      valueType: 'option',
      render: (text, record, _,action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
            setAccountType(record.type);
            setSellerId(record.sellerId);
            setTeamId(record.teamId);
          }}
        >
          编辑
        </a>,
        <a
          key="handleDelete"
          onClick={() => {
            handleDelete(record);
            refreshTable();
          }}
        >
          删除
        </a>,
        !record.inviteDone?<a
          key="handleQR"
          onClick={() => {
            handleQR(record);
          }}
        >
          绑定
        </a>:<a
          key="handleUnbind"
          onClick={() => {
            handleUnbind(record);
          }}
        >
          解绑
        </a>
      ],
    },
  ];

  const actionRef = useRef<ActionType>();

  const {getDouyinAccounts, addAccount, updateAccount, getAllSellerList, getUsers} = services.WorkbenchController
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [qrCode, setQRCode] = useState('')
  const [isQRCodeVisable, setIsQRCodeVisable] = useState(false)

  const refreshTable = () => {
    actionRef.current?.reload();
  }

  useEffect(()=>{
    loadTeamData("");
    loadSellerData("");
    loadSelfUserData("");
  },[])

  const handleSubmit = (values) => {
    return addAccount({phone:values.phone, sellerId:values.sellerId, code: values.code, coverBackground: "/data/app/douyin/baseCover/base_cover1.jpg",
      type:accountType, teamId:teamId, selfId: selfId}).then((res) => {
       if(res.result) {
          message.info("新增成功！");
          refreshTable();
          return true;
       }else{
          message.info("新增失败，" + res.resultDesc);
          return false;
       }
    })
  }

  const loadTeamData = (teamName:any) => {
    searchTeams({teamName: teamName}).then(
      (res: any) =>  {
        let itemOptionsData = res.result.data.map(item => {
          return {value: item.id, label: item.name};
        })
        setTeamOptions(itemOptionsData);
      }
    )
  }

  const loadSelfUserData = (userName:any) => {
    getUsers({userName: userName, isSelfCutter: true}).then(
      (res: any) =>  {
        let itemOptionsData = res.result.data.map(item => {
          return {value: item.id, label: item.userName};
        })
        setSelfUserOptions(itemOptionsData);
      }
    )
  }


  const loadSellerData = (sellerName:any) => {
    getSellerList({sellerName: sellerName}).then(
      (res: any) =>  {
        let sellerOptionsData = res.result.data.map(item => {
          return {value: item.sellerId, label: item.sellerName};
        })
        setSellerOptions(sellerOptionsData);
      }
    )
  }

  const handelUpdateSubmit = (record?:any) => {
    updateAccount({...record, teamId: teamId, sellerId:sellerId}).then((res) => {
      if(res.result) {
        message.success("更新成功！");
        refreshTable();
      }else {
        message.error("更新失败,"+ res.resultDesc);
      }
    })
  }

  const handleDelete = (record?:any) => {
    deleteDouyinAccount({id:record.id}).then((res) => {
      if(res.result) {
        message.success("删除成功！");
        refreshTable();
      }else {
        message.error("删除失败,"+ res.resultDesc);
      }
    })
  }

  const handleQR = (record?:any) => {
    setIsQRCodeVisable(true);
    // const context = qrCodeCanvas.current.getContext('2d');
    // context.clearRect(0,0, qrCodeCanvas.width, qrCodeCanvas.height);
    setQRCode('');
    getQRCode({douyinAccountId:record.id}).then((res) => {
      if(res.result) {
        setQRCode(res.result.qrcode);
      }else {
        message.error("获取二维码失败，"+ res.resultDesc);
      }
    })
  }

  const handleUnbind = (record?:any) => {
    unbindInvite({douyinAccountId:record.id}).then((res) => {
      if(res.result) {
        message.info("完成解除绑定！");
        refreshTable();
      }else {
        message.error("解除绑定失败，"+ res.resultDesc);
      }
    })
  }

  const handleCancelQRCode = () => {
    setIsQRCodeVisable(false);
  }
  
  useEffect(()=>{
    if(qrCodeCanvas.current) {
      const ctx = qrCodeCanvas.current.getContext('2d');
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, 350, 350);
      };
      img.src=`data:image/png;base64,${qrCode}`;
    }
  },[qrCode])

  return (
      <>
      <EditableProTable
        editableFormRef={editableFormRef}
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={async (params, sort, filter) => {
          return (await 
            getDouyinAccounts({...params, pageNum: params.current}).then((res:any) => (
              {
              data: res.result.data?.map((item:any) => ({...item, sellerName:item.sellerDTO?item.sellerDTO.sellerName:"无"})),
              total: res.result.total,
              sucess: res.resultCode
            }
          )));
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          onSave: async (rowKey, data, row) => {
            handelUpdateSubmit(data)
          },
          onChange: setEditableRowKeys,
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
          onChange(value) {
            console.log('value: ', value);
          },
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        form={{
          // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        dateFormatter="string"
        headerTitle="抖音账号列表"
        toolBarRender={() => [
          <ModalForm<{
            name: string;
            company: string;
          }>
          
            title="新增抖音账号"
            trigger={
              <Button type="primary">
                <PlusOutlined />
                新增抖音账号
              </Button>
            }
            autoFocusFirstInput
            modalProps={{
              destroyOnClose: true,
              onCancel: () => console.log('run'),
            }}
            submitTimeout={2000}
            onFinish={async (values) => handleSubmit(values)}
          >
            <ProForm.Group>
              <ProFormText
                width="md"
                name="phone"
                label="手机号"
                tooltip="长度为13位"
                placeholder="请输入手机号"
              />
               <ProFormText
                width="md"
                name="code"
                label="抖音账号"
                placeholder="请输入抖音账号"
              />
              <ProFormSelect
                request={async () => getAllSellerList().then(
                  (res: any) => 
                    res.result.data.map(item => {return {value: item.sellerId, label: item.sellerName};}
                  )
                )}
                width="xs"
                name="sellerId"
                label="达人"
              />
               <ProFormSelect
                request={async () => getDouyinAccountTypes().then(
                  (res: any) => 
                    res.result.map(item => {return {value: item.code, label: item.desc};}
                  )
                )}
                width="xs"
                name="type"
                label="账号类型"
                onChange={(value) => setAccountType(value)}
              />
              {accountType==2?<div>
              <div>团队</div>
              <Select
                options={teamOptions}
                showSearch
                style= {{width : '200px', marginTop : '8px'}}
                onSearch={(teamName) =>{loadTeamData(teamName)}}
                onChange={(value) => {setTeamId(value)}}
                filterOption={false}
              />
              </div>:null}
              {accountType==3?<div>
              <div>自营剪辑手</div>
              <Select
                options={selfUserOptions}
                showSearch
                style= {{width : '200px', marginTop : '8px'}}
                onSearch={(userName) =>{loadSelfUserData(userName)}}
                onChange={(value) => {setSelfId(value)}}
                filterOption={false}
              />
              </div>:null}
              <ProFormText
                width="lg"
                name="coverBackground"
                label="封面背景（修改base_cover1.jpg的序号即可，可选序号为：1-12）"
                initialValue="/data/app/douyin/baseCover/base_cover1.jpg"
                hidden={true}
              />
            </ProForm.Group>
          </ModalForm>,
          <Dropdown
            key="menu"
            menu={{
              items: [
                {
                  label: '1st item',
                  key: '1',
                },
                {
                  label: '2nd item',
                  key: '2',
                },
                {
                  label: '3rd item',
                  key: '3',
                },
              ],
            }}
          >
            <Button>
              <EllipsisOutlined />
            </Button>
          </Dropdown>,
        ]}
      />
      <Modal className='qrCodeModal' open={isQRCodeVisable} title="邀请码" onCancel={handleCancelQRCode}>
      <canvas ref={qrCodeCanvas} width='450' height='600'></canvas>
    </Modal>
    </>
  );
}
export default DouyinAccountList;
