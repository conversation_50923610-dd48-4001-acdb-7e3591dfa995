import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-components';
import { EditableProTable, ModalForm, ProForm, ProFormDigit, ProFormRate, ProFormSelect, ProFormText, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Form, Input, message, Modal, Space, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less'
import services from  '../../../services/workbench';

const TeamList: React.FC = () => {

  const editableFormRef = useRef<EditableFormInstance>();
  const columns: ProColumns<any>[] = [
    {
      title: '团队id',
      dataIndex: 'id',
      width: 150,
      search: false
    },
    {
      title: '团队名称',
      dataIndex: 'name',
      width: 150    
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
      width: 200
    },
    {
      title: '省份',
      dataIndex: 'province',
      width: 150
    },
    {
      title: '城市',
      dataIndex: 'city',
      width: 150
    },
    {
      title: '团队人数',
      dataIndex: 'numOfDouyinAccount',
      width: 150
    },
    {
      title: '分润比例',
      dataIndex: 'shareRatio',
      width: 150
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      editable: false,
      width: 250
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      editable: false,
      width: 250
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      render: (text, record, _,action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <a
          key="handleDelete"
          onClick={() => {
            handleDelete(record);
            refreshTable();
          }}
        >
          删除
        </a>
      ],
    },
  ];

  const actionRef = useRef<ActionType>();

  const {deleteTeam, searchTeams, updateTeam, addTeam} = services.WorkbenchController
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const refreshTable = () => {
    actionRef.current?.reload();
  }

  const handleSubmit = (values) => {
    return addTeam({phone:values.phone, name:values.name, province: values.province, 
      city: values.city, shareRatio: values.shareRatio}).then((res) => {
       if(res.result) {
          message.info("新增成功！");
          refreshTable();
          return true;
       }else{
          message.info("新增失败，" + res.resultDesc);
          return false;
       }
    })
  }


  const handelUpdateSubmit = (record?:any) => {
    updateTeam(record).then((res) => {
      if(res.result) {
        message.success("更新成功！");
        refreshTable();
      }else {
        message.error("更新失败,"+ res.resultDesc);
      }
    })
  }

  const handleDelete = (record?:any) => {
    deleteTeam({id:record.id}).then((res) => {
      if(res.result) {
        message.success("删除成功！");
        refreshTable();
      }else {
        message.error("删除失败,"+ res.resultDesc);
      }
    })
  }

  return (
      <>
      <EditableProTable
        editableFormRef={editableFormRef}
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={async (params, sort, filter) => {
          return (await 
            searchTeams({...params, pageNum: params.current}).then((res:any) => (
              {
              data: res.result.data?.map((item:any) => ({...item, shareRatio: item.shareRatio + "%"})),
              total: res.result.total,
              sucess: res.resultCode
            }
          )));
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          onSave: async (rowKey, data, row) => {
            handelUpdateSubmit(data)
          },
          onChange: setEditableRowKeys,
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
          onChange(value) {
            console.log('value: ', value);
          },
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        form={{
          // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        dateFormatter="string"
        headerTitle="团队列表"
        toolBarRender={() => [
          <ModalForm<{
            name: string;
            company: string;
          }>
          
            title="新增团队"
            trigger={
              <Button type="primary">
                <PlusOutlined />
                新增团队
              </Button>
            }
            autoFocusFirstInput
            modalProps={{
              destroyOnClose: true,
              onCancel: () => console.log('run'),
            }}
            submitTimeout={2000}
            onFinish={async (values) => handleSubmit(values)}
          >
            <ProForm.Group>
              <ProFormText
                width="md"
                name="name"
                label="团队名称"
                placeholder="请输入团队名称"
              />
              <ProFormText
                width="md"
                name="phone"
                label="联系方式"
                tooltip="长度为13位"
                placeholder="请输入联系方式"
              />
              <ProFormText
                width="md"
                name="province"
                label="省"
                placeholder="请输入省"
              />
              <ProFormText
                width="md"
                name="city"
                label="市"
                placeholder="请输入市"
              />
              <ProFormDigit
                width="md"
                name="shareRatio"
                label="分润比例"
                placeholder="请输入分润比例"
                
                colSize={30}
              />
            </ProForm.Group>
          </ModalForm>,
        ]}
      />
    </>
  );
}
export default TeamList;
