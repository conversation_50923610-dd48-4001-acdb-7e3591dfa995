import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, ProForm, ProFormSelect, ProFormText, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, message, Space, Tag } from 'antd';
import React, { useRef } from 'react';
import services from  '../../../services/workbench';

const SellerList: React.FC = () => {

  const columns: ProColumns<API.SellerVO>[] = [
    {
      title: '达人id',
      dataIndex: 'sellerId',
      width: 80,
      search: false
    },
    {
      title: '达人名称',
      dataIndex: 'sellerName',
      width: 200    
    },
    {
      title: '描述',
      dataIndex: 'desc',
      width: 200   
    },
    {
      title: '素材基础路径',
      dataIndex: 'materialBasePath',
      width: 300   
    },
    {
      title: '创建时间',
      key: 'showTime',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      width: 150 
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      width: 150 
    },
    {
      title: '操作',
      width: 80,
      key: 'option',
      valueType: 'option',
      render: (text, record) => [
        <a key='delete' onClick={ () => {handelDeleteSubmit(record)}}>删除</a>,
      ],
    },
  ];

  const actionRef = useRef<ActionType>();

  const {getSellerList, createSeller, deleteSeller, updateSeller} = services.WorkbenchController

  const refreshTable = () => {
    actionRef.current?.reload();
  }

  const handleSubmit = (values) => {
    return createSeller({sellerName:values.sellerName, des:values.des, materialBasePath: values.materialBasePath}).then((res) => {
       if(res.result) {
          message.info("新增成功！");
          refreshTable();
          return true;
       }else{
          message.info("新增失败！");
          return false;
       }
    })
  }

  const handelDeleteSubmit = (record) => {
    deleteSeller({sellerId: record.sellerId}).then((res) => {
      if(res.result) {
        message.success("删除成功！");
        refreshTable();
      }else {
        message.error("删除失败!");
      }
    })

  }

  return (
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={async (params, sort, filter) => {
          return (await 
            getSellerList({...params, pageNum: params.current}).then((res:any) => (
              {
              data: res.result.data,
              total: res.result.total,
              sucess: res.resultCode
            }
          )));
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
          onChange(value) {
            console.log('value: ', value);
          },
        }}
        rowKey="sellerId"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        form={{
          // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        dateFormatter="string"
        headerTitle="达人列表"
        toolBarRender={() => [
          <ModalForm<{
            name: string;
            company: string;
          }>
          
            title="新增达人"
            trigger={
              <Button type="primary">
                <PlusOutlined />
                新增达人
              </Button>
            }
            autoFocusFirstInput
            modalProps={{
              destroyOnClose: true,
              onCancel: () => console.log('run'),
            }}
            submitTimeout={2000}
            onFinish={async (values) => handleSubmit(values)}
          >
            <ProForm.Group>
              <ProFormText
                width="md"
                name="sellerName"
                label="达人名称"
                tooltip="最长为 24 位"
                placeholder="请输入达人名称"
              />
               <ProFormText
                width="md"
                name="des"
                label="描述"
                tooltip="最长为 24 位"
                placeholder="请输入达人描述"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                width="lg"
                name="materialBasePath"
                label="素材基础路径"
                placeholder="请输入素材基础路径"
              />
            </ProForm.Group>
          </ModalForm>,
          <Dropdown
            key="menu"
            menu={{
              items: [
                {
                  label: '1st item',
                  key: '1',
                },
                {
                  label: '2nd item',
                  key: '2',
                },
                {
                  label: '3rd item',
                  key: '3',
                },
              ],
            }}
          >
            <Button>
              <EllipsisOutlined />
            </Button>
          </Dropdown>,
        ]}
      />
  );
}
export default SellerList;
