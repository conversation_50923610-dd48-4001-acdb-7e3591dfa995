import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, ProForm, ProFormDatePicker, ProFormDateTimePicker, ProFormSelect, ProFormText, ProFormUploadButton, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Form, Input, message, Modal, Select, Space, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import services from '../../../services/workbench'
import Styles from './index.less'
import { subDays, format } from 'date-fns';

const OrderList: React.FC = () => {

  const columns: ProColumns[] = [
    {
      title: '内部id',
      dataIndex: 'id',
      width: '10%',
      search: false
    },
    {
      title: '订单外部id',
      dataIndex: 'orderId',
      width: '10%'  
    },
    {
      title: '抖音号',
      dataIndex: 'douyinAccountCode',
      render: (_, record) => (
        <div>{record.douyinAccountDTO?.code}</div>
      ),
      width: '10%'  
    },
    {
      title: '抖音昵称',
      render: (_, record) => (
        <div>{record.douyinAccountDTO?.douyinName}</div>
      ),
      width: '10%',
      hideInSearch:true
    },
    {
      title: '账号类型',
      dataIndex: 'accountType',
      render: (_, record) => (
        <div>{
          record.douyinAccountDTO?.type==1?'个人号':record.douyinAccountDTO?.type==2?'团队号':'自营号'
          }</div>
      ),
      valueEnum: {
        1: {
          text: '个人号',
        },
        2: {
          text: '团队号',
        },
        3: {
          text: '自营号',
        },
      },
      width: '10%'  
    },
    {
      title: '团队名称',
      dataIndex: 'teamId',
      render: (_, record) => (
        <div>{
          record.douyinAccountDTO?.type==2?record.douyinAccountDTO?.teamDTO.name:"-"
          }</div>
      ),
      width: '10%',
      renderFormItem: (item, { value, onChange }) =>  (
          <Select
            options={teamOptions}
            showSearch
            style= {{width : '200px', marginTop : '8px'}}
            onSearch={(teamName) =>{loadTeamData(teamName)}}
            onChange={(value) => {setTeamId(value)}}
            onClear={() => {setTeamId(0)}}
            filterOption={false}
            allowClear={true}
          />
       ), 
    },
    {
      title: '自营剪辑手',
      dataIndex: 'selfId',
      render: (_, record) => (
        <div>{
          record.douyinAccountDTO?.type==3?record.douyinAccountDTO.selfUserDTO.userName:"-"
          }</div>
      ),
      width: '10%',
      renderFormItem: (item, { value, onChange }) =>  (
          <Select
            options={userOptions}
            showSearch
            style= {{width : '200px', marginTop : '8px'}}
            onSearch={(teamName) =>{loadLeftCutterData(teamName)}}
            onChange={(value) => {setSelfCutterId(value)}}
            onClear={() => {setSelfCutterId(0)}}
            filterOption={false}
            allowClear={true}
          />
       ), 
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: '20%'    ,
      sorter: true,
      hideInSearch:true
    },
    {
      title: '商品数量',
      dataIndex: 'itemNum',
      width: '10%'    ,
      sorter: true,
      hideInSearch:true
    },
    {
      disable: true,
      width: '10%',
      title: '支付金额',
      dataIndex: 'totalPayAmountStr',
      search: false,
      hideInSearch:true
    },
    {
      disable: true,
      title: '佣金率',
      dataIndex: 'commissionRateStr',
      search: true,
      width: '10%',
      hideInSearch:true
    },{
      disable: true,
      title: '预估佣金',
      dataIndex: 'commissionRateStr',
      search: true,
      width: '10%',
      hideInSearch:true
    },{
      disable: true,
      title: '订单状态',
      dataIndex: 'flowPoint',
      filters: true,
      onFilter: true,
      ellipsis: true,
      width: 80,
      valueType: 'select',
      valueEnum: {
        'PAY_SUCC': {
          text: '支付完成',
        },
        'REFUND': {
          text: '退货',
        },
        'SETTLE': {
          text: '结算',
        },
        'CONFIRM': {
          text: '确认收货',
        },
      },
    },
    {
      title: '付款时间',
      key: 'startDate',
      dataIndex: 'paySuccessTime',
      valueType: 'dateTime',
      width: '10%',
      search: {
        title: '开始时间',
      },
      initialValue: subDays(new Date(), 7)

    },
    {
      key: 'endDate',
      valueType: 'dateTime',
      width: '10%',
      hidden: true,
      search: {
        title: '结束时间',
      },
      initialValue: new Date()
    },
    {
      title: '更新时间',
      key: 'showTime',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      width: '10%',
    },
    // {
    //   title: '操作',
    //   width: '10%',
    //   key: 'option',
    //   valueType: 'option',
    //   render: (text, record) => [
    //     <a key='delete' onClick={ () => {handelDeleteSubmit(record)}}>删除</a>,
    //   ],
    // },
  ];

  const actionRef = useRef<ActionType>();
  const {getSellerList, getOrderList, searchTeams, syncOrder, getUsers} = services.WorkbenchController
  const [teamOptions, setTeamOptions] = useState()
  const [teamId, setTeamId] = useState(0)
  const [orderData, setOrderData] = useState()
  const [userOptions, setUserOptions] = useState()
  const [selfCutterId, setSelfCutterId] = useState(0)

  useEffect(()=>{
    loadTeamData("");
    loadLeftCutterData("");
  },[])


  const loadTeamData = (teamName:any) => {
    searchTeams({teamName: teamName}).then(
      (res: any) =>  {
        let itemOptionsData = res.result.data.map(item => {
          return {value: item.id, label: item.name};
        })
        setTeamOptions(itemOptionsData);
      }
    )
  }

  const loadLeftCutterData = (userName:any) => {
    getUsers({userName: userName, isSelfCutter: true}).then(
      (res: any) =>  {
        let itemOptionsData = res.result.data.map(item => {
          return {value: item.id, label: item.userName};
        })
        setUserOptions(itemOptionsData);
      }
    )
  }


  const onSyncOrderOK= (values:any) => {
    return syncOrder({
      isSyncAll: true
    }).then((res:any) => {
      if(res.result) {
        message.success('提交成功');
        return res.result;
      }else{
        message.success('提交失败');
      }
    })
  }

  return (
    <>
    <ProTable<API.ItemVO>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort, filter) => {
        
        let orderData = await getOrderList({...params, teamId:teamId,  selfCutterId: selfCutterId, pageNum: params.current, 
          startDate: format(params.startDate, "yyyy-MM-dd hh:mm:ss"), endDate: format(params.endDate, "yyyy-MM-dd hh:mm:ss")});
        if(orderData!=null) {
            setOrderData(orderData.result);
        }
        return {data: orderData.result.data, total: orderData.result.total, sucess: orderData.resultCode }
        
      }}
      editable={{
        type: 'multiple',
      }}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        defaultValue: {
          option: { fixed: 'right', disable: true },
        },
        onChange(value) {
          console.log('value: ', value);
        },
      }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
        defaultCollapsed: false,
      }}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      // form={{
      //   // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
      //   syncToUrl: (values, type) => {
      //     if (type === 'get') {
      //       return {
      //         ...values,
      //         created_at: [values.startTime, values.endTime],
      //       };
      //     }
      //     return values;
      //   },
      // }}
      pagination={{
        pageSize: 10,
        onChange: (page) => console.log(page),
      }}
      dateFormatter="string"
      headerTitle={ 
        <div className={Styles.orderCount}>
          <div className={Styles.orderCountCell}>订单数量：{orderData?.total}</div>
          <div className={Styles.orderCountCell}>订单总额：{orderData?.totalPayAmount/100}</div>
        </div>     
      }
      toolBarRender={() => (
        <div>
          <div>
          <ModalForm<{
            name: string;
            company: string;
          }>
          
            title="同步所有订单"
            trigger={
              <Button type="primary">
                <PlusOutlined />
                同步所有订单
              </Button>
            }
            autoFocusFirstInput
            modalProps={{
              destroyOnClose: true,
              onCancel: () => console.log('run'),
            }}
            submitTimeout={2000}
            onFinish={async (values) => onSyncOrderOK(values)}
          >
            <ProForm.Group>
              确定全量同步所有订单？
            </ProForm.Group>
          </ModalForm>
          </div>
        </div>)
    }
    />
    </>
  );
}
export default OrderList;
