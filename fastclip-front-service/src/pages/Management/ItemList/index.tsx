import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, ProForm, ProFormDatePicker, ProFormDateTimePicker, ProFormSelect, ProFormText, ProFormUploadButton, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Form, Input, message, Modal, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import services from '../../../services/workbench'
import styles from './index.less'

const ItemList: React.FC = () => {

  const columns: ProColumns[] = [
    {
      title: '商品id',
      dataIndex: 'id',
      width: '10%',
      search: false
    },
    {
      title: '商品外部id',
      dataIndex: 'outItemId',
      width: '10%'  
    },
    {
      title: '商品名称',
      dataIndex: 'itemName',
      width: '20%'    ,
      sorter: true,
    },
    {
      disable: true,
      width: '40%',
      title: '分享链接',
      dataIndex: 'shareUrl',
      search: false
    },
    {
      disable: true,
      title: '关联达人',
      dataIndex: 'sellerName',
      search: true,
      width: '10%'
    },
    {
      disable: true,
      title: '项目',
      dataIndex: 'isPublish',
      filters: true,
      onFilter: true,
      ellipsis: true,      
      search: true,
      width: '10%',
      valueType: 'select',
      render: (text, record) => 
        record.projectId==null?<a key='createProject' onClick={ () => {handleCreateProject(record)}}>创建项目</a>:
        <a>{record.projectId}</a>
    },
    {
      title: '创建时间',
      key: 'showTime',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      width: '10%',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      width: '10%'
    },
    {
      title: '操作',
      width: '10%',
      key: 'option',
      valueType: 'option',
      render: (text, record) => [
        <a key='delete' onClick={ () => {handelDeleteSubmit(record)}}>删除</a>,
      ],
    },
  ];

  const actionRef = useRef<ActionType>();
  const {searchItems, deleteItem, getAllSellerList, uploadItems, addItems, createProjectApi} = services.WorkbenchController
  const [items, setItems] = useState([])
  const [createProjectModalVisable, setCreateProjectModalVisable] = useState(false)
  const [createProjectInfo, setCreateProjectInfo] = useState(null)
  const [projectName, setProjectName] = useState(null)

  const refreshTable = () => {
    actionRef.current?.reload();
  }

  const handelDeleteSubmit = (record:any) => {
    deleteItem({itemIds: [record.id]}).then((res:any) => {
      if(res.result) {
        message.success("删除成功！");
        refreshTable();
      }else {
        message.error("删除失败!");
      }
    })
  }

  const handleUpload = (file:any) => {
    const formData = new FormData();
    formData.append('file', file);

    // Use unified request utility with proper error handling
    uploadItems(formData).then((res:any) => {
      console.log('Upload response:', res);
      if(res.result && Array.isArray(res.result)) {
        setItems(res.result);
        message.success(`文件解析成功，共解析出 ${res.result.length} 个商品`);
      } else {
        message.error('文件解析失败，请检查文件格式');
        setItems([]);
      }
    }).catch((error) => {
      console.error('Upload error:', error);
      message.error('文件上传失败: ' + error.message);
      setItems([]);
    });
  }

  const handleSubmit = (values) => {
    console.log('Submitting items:', items, 'sellerId:', values.seller);

    if (!items || items.length === 0) {
      message.error("请先上传并解析商品文件");
      return Promise.resolve(false);
    }

    if (!values.seller) {
      message.error("请选择达人");
      return Promise.resolve(false);
    }

    return addItems({items:items, sellerId:values.seller}).then((res: any) => {
      console.log('Add items response:', res);
      if(res && res.result >= 0) {
        message.success("上传成功 共增加[" + res.result + "]");
        refreshTable();
        setItems([]); // Clear items after successful submission
        return true;
      }else {
        message.error("增加失败！");
        return false;
      }
    }).catch((error: any) => {
      console.error('Add items error:', error);
      message.error("提交失败: " + (error.message || error));
      return false;
    });
  }

  const handleCreateProject= (values:any) => {
    setCreateProjectModalVisable(true);
    setCreateProjectInfo(values);
  }

  const onCreateProjectCancel= () => {
    setCreateProjectModalVisable(false);
  }

  const handleProjectName = (e:any) => {
    setProjectName(e.target.value)
  }

  const handleAddItemSubmit = (values:any) => {
    
  }

  const onCreateProjectOK= (values:any) => {
    return createProjectApi({
      sellerId: createProjectInfo?.sellerId,
      projectName: projectName,
      itemId: createProjectInfo?.id,
      itemType: createProjectInfo?.itemType,
    }).then((res:any) => {
      if(res.result) {
        message.success('提交成功');
        setCreateProjectModalVisable(false);
        return res.result;
      }else{
        message.success('提交失败');
      }
    })
  }

  return (
    <>
    <ProTable<API.ItemVO>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort, filter) => {
        return (await 
          searchItems({...params, pageNum: params.current}).then((res:any) => (
            {
            data: res.result.data.map((item) => ({...item, sellerName:item.seller.sellerName})),
            total: res.result.total,
            sucess: res.resultCode
          }
        )));
      }}
      editable={{
        type: 'multiple',
      }}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        defaultValue: {
          option: { fixed: 'right', disable: true },
        },
        onChange(value) {
          console.log('value: ', value);
        },
      }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
        defaultCollapsed: false,
      }}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      form={{
        // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              created_at: [values.startTime, values.endTime],
            };
          }
          return values;
        },
      }}
      pagination={{
        pageSize: 10,
        onChange: (page) => console.log(page),
      }}
      dateFormatter="string"
      headerTitle="商品列表"
      toolBarRender={() => [
        <ModalForm<{
          name: string;
          company: string;
        }>
        
          title="上传商品"
          trigger={
            <Button type="primary">
              <PlusOutlined />
              上传商品
            </Button>
          }
          autoFocusFirstInput
          modalProps={{
            destroyOnClose: true,
            onCancel: () => console.log('run'),
          }}
          submitTimeout={2000}
          onFinish={async (values) => handleSubmit(values)}
        >
          <ProForm.Group>
            <ProFormUploadButton
                extra="支持扩展名：.jpg .zip .doc .wps .csv .xlsx"
                label="商品附件表"
                name="file"
                title="上传文件"
                action={handleUpload}
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormSelect
              request={async () => getAllSellerList().then(
                (res: any) => 
                  res.result.data.map(item => {return {value: item.sellerId, label: item.sellerName};}
                )
              )}
              width="xs"
              name="seller"
              label="达人"
            />
          </ProForm.Group>
        </ModalForm>,
        <ModalForm<{
          name: string;
          company: string;
        }>
        
          title="增加商品"
          trigger={
            <Button type="primary">
              <PlusOutlined />
              增加商品
            </Button>
          }
          autoFocusFirstInput
          modalProps={{
            destroyOnClose: true,
            onCancel: () => console.log('run'),
          }}
          submitTimeout={2000}
          onFinish={async (values) => handleAddItemSubmit(values)}
        >
          <ProForm.Group>
            <ProFormSelect
              request={async () => getAllSellerList().then(
                (res: any) => 
                  res.result.data.map(item => {return {value: item.sellerId, label: item.sellerName};}
                )
              )}
              width="xs"
              name="seller"
              label="达人"
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormDateTimePicker
              name="startTime"
              label="日期时间">
            </ProFormDateTimePicker>
          </ProForm.Group>
        </ModalForm>,
        <Dropdown
          key="menu"
          menu={{
            items: [
              {
                label: '1st item',
                key: '1',
              },
              {
                label: '2nd item',
                key: '2',
              },
              {
                label: '3rd item',
                key: '3',
              },
            ],
          }}
        >
          <Button>
            <EllipsisOutlined />
          </Button>
        </Dropdown>,
      ]}
    />
    <Modal open={createProjectModalVisable} onCancel={onCreateProjectCancel} onOk={onCreateProjectOK} className={styles.createProjectClass}>
      <Form>
        <div>
        项目名称：<Input name="projectName" onChange={handleProjectName}></Input>
        </div>
        <div className={styles.itemName}>
        商品名称：<Input name="itemName" value={createProjectInfo?.itemName} disabled={true}></Input>
        </div>
      </Form>
    </Modal>
    </>
  );
}
export default ItemList;
