import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, ProForm, ProFormDatePicker, ProFormSelect, ProFormText, ProFormTimePicker, ProFormUploadButton, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Checkbox, Dropdown, message, Modal, Select, Space, Tag } from 'antd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import services from '@/services/workbench';
import VideoMaterialStartTimeEditor from '../components/VideoMaterialStartTimeEditor';
import ProductMarkingModal from './components/ProductMarkingModal';
import ProjectContext from '../../Workbench/Project/compoents/ProjectContext'
import styles from './index.less'

const VideoMaterialList: React.FC = () => {

  const columns: ProColumns<API.VideoMaterialRecord>[] = [
    {
      title: '视频素材id',
      dataIndex: 'id',
      width: 60,
      search: false
    },
    {
      title: '达人名称',
      dataIndex: 'sellerName',
      width: 80    
    },
    {
      title: '素材日期',
      key: 'showTime',
      dataIndex: 'startDate',
      sorter: true,
      width: 100  
    },
    {
      disable: true,
      width: 200,
      title: '素材地址',
      dataIndex: 'path',
      search: false
    },
    {
      disable: true,
      title: '视频时长',
      dataIndex: 'duration',
      search: false,
      width: 100
    },
    {
      disable: true,
      title: '当前解析时间',
      dataIndex: 'subtitlesBpTs',
      search: false,
      width: 100
    },
    {
      disable: true,
      title: '开始时间',
      dataIndex: 'startTime',
      search: false,
      width: 100
    },
    {
      disable: true,
      title: '字幕解析状态',
      dataIndex: 'isSubtitlesDone',
      filters: true,
      onFilter: true,
      ellipsis: true,
      width: 80,
      valueType: 'select',
      valueEnum: {
        true: {
          text: '已完成',
        },
        false: {
          text: '解析中',
        },
      },
    },
    {
      title: '创建时间',
      key: 'showTime',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      width: 80
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
      width: 80
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      render: (text, record) => [
        <a key='delete' onClick={ () => {handleDeleteSubmit(record)}}>删除</a>,
        <a key='fetchSubtitles' onClick={ () => {handleFetchSubtitlesSubmit(record)}}>解析字幕</a>,
        <a key='editStartTime' onClick={ () => {handleEditStartTime(record)}}>标记场次</a>,
        <a key='markProducts' onClick={ () => {handleMarkProducts(record)}}>标记商品</a>,
      ],
    },
  ];

  const actionRef = useRef<ActionType>();

  const {getVideoMaterials, getAllSellerList, createVideo, deleteVideo, fetchSubtitles,
     createLiveVideo, getVideoMaterialFiles, getTopItems} = services.WorkbenchController
  const {user} = useContext(ProjectContext);
  const [editStartTimeRecord, setEditStartTimeRecord] = useState(null)
  const [openEditStartTime, setOpenEditStartTime] = useState(false)
  const [materialFileOptions, setMaterialFileOptions] = useState()
  const [splitMaterialFileOptions, setSplitMaterialFileOptions] = useState()
  const [addNewItem, setAddNewItem] = useState(false)
  const [uploadVideoMaterial, setUploadVideoMaterial] = useState(false)
  const [itemOptions, setItemOptions] = useState()
  const [sellerId, setSellerId] = useState()
  const [itemId, setItemId] = useState()
  const [tmpVideoPath, setTmpVideoPath] = useState()
  const [markProductsRecord, setMarkProductsRecord] = useState(null)
  const [openMarkProducts, setOpenMarkProducts] = useState(false)

  useEffect(() => {
    if(user?.hasUploadPrevilege) {
      setUploadVideoMaterial(true);
    }
  },[user])

  const refreshTable = () => {
    actionRef.current?.reload();
  }

  const loadMaterialFileOptions = (id:any) => {
    getVideoMaterialFiles({sellerId: id}).then(
        (res: any) =>  {
          setMaterialFileOptions(res.result.map(item => {return {value: item.fileName, label: item.fileName};}))
        }
    )
  }

  const loadSplitMaterialFileOptions = (id:any) => {
    getVideoMaterialFiles({sellerId: id}).then(
        (res: any) =>  {
          setSplitMaterialFileOptions(res.result.map(item => {return {value: item.fileName, label: item.fileName};}))
        }
    )
  }

  const handleDeleteSubmit = (record) => {
    deleteVideo({id: record.id}).then((res) => {
      if(res.result) {
        message.success("删除成功！");
        refreshTable();
      }else {
        message.error("删除失败!");
      }
    })

  }

  const handleFetchSubtitlesSubmit = async (record) => {
    fetchSubtitles({id: record.id});
    message.info("提交成功")
  }

  const handleEditStartTime = async (record:any) => {
    setEditStartTimeRecord(record);
    setOpenEditStartTime(true);
  }

  const handleMarkProducts = async (record:any) => {
    setMarkProductsRecord(record);
    setOpenMarkProducts(true);
  }

  const handleSubmit = (values) => {
    return createVideo({videoPath:values.filePath, sellerId:values.seller, 
                        startDate: values.startDate, startTime: 0, videoType:1}).then((res) => {
      if(res.result) {
        message.info("增加成功！");
        refreshTable();
        return true;
      }else {
        message.info("增加失败," + res.resultDesc);
        return false;
      }
    })
  }

  const handleSplitSubmit = (values) => {
    return createVideo({videoPath:values.splitFilePath, 
                        sellerId: values.splitItemSeller, 
                        startDate: values.startDate, 
                        startTime: 0, videoType:3,
                        itemOutId: values.itemOutId,
                        itemName: values.itemName,
                        itemId: itemId,
                        videoTmpPath: tmpVideoPath,
                        shareUrl: values.shareUrl}).then((res) => {
      if(res.result) {
        message.info("增加成功！");
        refreshTable();
        return true;
      }else {
        message.info("增加失败," + res.resultDesc);
        return false;
      }
    })
  }

  const onCheckboxChange = (e:any) => {
    setAddNewItem(e.target.checked)
  }

  const onVideoUploadCheckboxChange = (e:any) => {
    setUploadVideoMaterial(e.target.checked)
  }

  const loadItemData = (sellerId:any, itemName:any) => {
    getTopItems({sellerIds:[sellerId], itemName:itemName, itemType:1, pageSize:100}).then(
      (res: any) =>  {
        let itemOptionsData = res.result.map(item => {
          return {value: item.id, label: item.itemName};
        })
        setItemOptions(itemOptionsData);
      }
    )
  }

  const handleLiveSubmit = (values) => {
    return createLiveVideo({url:values.url, sellerId:values.seller}).then((res) => {
      if(res.result) {
        message.info("增加成功！");
        refreshTable();
        return true;
      }else {
        message.info("增加失败！");
        return false;
      }
    })
  }

  const handleUpload = (file:any) => {
    const formData = new FormData();
    formData.append('file', file);
    fetch('/api/fastclip/video/uploadVideo', {
      method: 'POST',
      body: formData,
    })
    .then(response => response.json())
    .then((res:any) => {
      setTmpVideoPath(res.result);
    })
  }

  return (
    <>
    <ProTable<API.ProjectVO>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort, filter) => {
        return (await 
          getVideoMaterials({...params, pageNum: params.current}).then((res:any) => (
            {
            data: res.result.data.map((item) => ({...item, sellerName:item.seller.sellerName})),
            total: res.result.total,
            sucess: res.resultCode
          }
        )));
      }}
      editable={{
        type: 'multiple',
      }}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        defaultValue: {
          option: { fixed: 'right', disable: true },
        },
        onChange(value) {
          console.log('value: ', value);
        },
      }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      form={{
        // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              created_at: [values.startTime, values.endTime],
            };
          }
          return values;
        },
      }}
      pagination={{
        pageSize: 10,
        onChange: (page) => console.log(page),
      }}
      dateFormatter="string"
      headerTitle="素材列表"
      toolBarRender={() => [
        user?.isAdmin?<ModalForm<{
          name: string;
          company: string;
        }>
        
          title="新增导入素材"
          trigger={
            <Button type="primary">
              <PlusOutlined />
              新增导入素材
            </Button>
          }
          autoFocusFirstInput
          modalProps={{
            destroyOnClose: true,
            onCancel: () => console.log('run'),
          }}
          submitTimeout={2000}
          onFinish={async (values) => handleSubmit(values)}
        >
          <ProForm.Group>
            <ProFormSelect
              request={async () => getAllSellerList().then(
                (res: any) => 
                  res.result.data.map(item => {return {value: item.sellerId, label: item.sellerName};}
                )
              )}
              width="xs"
              name="seller"
              label="达人"
              onChange={(value:any) => 
                {
                  loadMaterialFileOptions(value);
                }
              }
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormSelect
              options={materialFileOptions}
              width="lg"
              name="filePath"
              label="素材列表"
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormDatePicker
              name="startDate"
              label="直播日期"
            >
            </ProFormDatePicker>
          </ProForm.Group>
        </ModalForm>:null,
        //  <ModalForm<{
        //   name: string;
        //   company: string;
        // }>
        
        //   title="新增直播素材"
        //   trigger={
        //     <Button type="primary">
        //       <PlusOutlined />
        //       新增直播素材
        //     </Button>
        //   }
        //   autoFocusFirstInput
        //   modalProps={{
        //     destroyOnClose: true,
        //     onCancel: () => console.log('run'),
        //   }}
        //   submitTimeout={2000}
        //   onFinish={async (values) => handleLiveSubmit(values)}
        // >
        //   <ProForm.Group>
        //     <ProFormText
        //       width="xl"
        //       name="url"
        //       label="直播地址"
        //       placeholder="请输入抖音直播间地址"
        //     />
        //   </ProForm.Group>
        //   <ProForm.Group>
        //     <ProFormSelect
        //       request={async () => getAllSellerList().then(
        //         (res: any) => 
        //           res.result.data.map(item => {return {value: item.sellerId, label: item.sellerName};}
        //         )
        //       )}
        //       width="xs"
        //       name="seller"
        //       label="达人"
        //     />
        //   </ProForm.Group>
        // </ModalForm>,
         <ModalForm<{
          name: string;
          company: string;
        }>
        
          title="新增分镜素材"
          trigger={
            <Button type="primary">
              <PlusOutlined />
              新增分镜素材
            </Button>
          }
          autoFocusFirstInput
          modalProps={{
            destroyOnClose: true,
            onCancel: () => console.log('run'),
          }}
          submitTimeout={2000}
          onFinish={async (values) => handleSplitSubmit(values)}
        >
          <ProForm.Group>
            <ProFormSelect
              request={async () => getAllSellerList().then(
                (res: any) => 
                  res.result.data.map(item => {return {value: item.sellerId, label: item.sellerName};}
                )
              )}
              width="xs"
              name="splitItemSeller"
              label="达人"
              onChange={(value:any) => 
                {
                  loadSplitMaterialFileOptions(value);
                  setSellerId(value);
                  loadItemData(value, null);
                }
              }
            />
          </ProForm.Group>
          <ProForm.Group>
             <Checkbox checked={uploadVideoMaterial} onChange={(e) => onVideoUploadCheckboxChange(e)}>上传素材</Checkbox>
          </ProForm.Group>
           <ProForm.Group>
            {uploadVideoMaterial? 
            <ProFormUploadButton
                extra="支持扩展名：.mp4 .ts"
                label="视频素材（mp4、ts文件格式）"
                name="file"
                title="上传素材文件"
                action={handleUpload}
                
            />:<ProFormSelect
              options={splitMaterialFileOptions}
              width="lg"
              name="splitFilePath"
              label="素材列表"
            />}
          </ProForm.Group>
          <ProForm.Group>
            <ProFormDatePicker
              name="startDate"
              label="直播日期"
            >
            </ProFormDatePicker>
          </ProForm.Group>
          <ProForm.Group>
             <Checkbox checked={addNewItem} onChange={(e) => onCheckboxChange(e)}>新增商品</Checkbox>
          </ProForm.Group>
          {addNewItem?<ProForm.Group>
            <ProFormText
              width="xl"
              name="itemOutId"
              label="商品id"
              placeholder="请输入商品id"
            />
            <ProFormText
              width="xl"
              name="itemName"
              label="商品名称"
              placeholder="请输入商品名称"
            />
             <ProFormText
              width="xl"
              name="shareUrl"
              label="商品链接"
              placeholder="请输入商品链接"
            />
          </ProForm.Group>:<ProForm.Group>
           <div><Select
           options={itemOptions}
           showSearch
           style= {{width : '700px', marginTop : '8px'}}
           onSearch={(itemName) =>{loadItemData(sellerId,itemName)}}
           onChange={(value) => {setItemId(value)}}
           filterOption={false}
         />
         </div></ProForm.Group>}
        </ModalForm>,
        <Dropdown
          key="menu"
          menu={{
            items: [
              {
                label: '1st item',
                key: '1',
              },
              {
                label: '2nd item',
                key: '2',
              },
              {
                label: '3rd item',
                key: '3',
              },
            ],
          }}
        >
          <Button>
            <EllipsisOutlined />
          </Button>
        </Dropdown>,
      ]}
    />
    <Modal width={1000} open={openEditStartTime} onCancel={() => setOpenEditStartTime(false)} onOk={() => setOpenEditStartTime(false)}>
      <VideoMaterialStartTimeEditor editStartTimeRecord={editStartTimeRecord}></VideoMaterialStartTimeEditor>
    </Modal>
    <ProductMarkingModal
      visible={openMarkProducts}
      onCancel={() => setOpenMarkProducts(false)}
      videoRecord={markProductsRecord}
    />
    </>
  );
}
export default VideoMaterialList;
