export interface Segment {
  start: number;
  end: number;
  category?: string;
  status?: 'pending' | 'processing' | 'completed' | 'error';
  confidence?: number;
  itemId?: string;
  itemName?: string;
  // Manual selection support
  isManuallySelected?: boolean;
  originalItemId?: string;
  originalItemName?: string;
  originalConfidence?: number;
  // Store all recognition results for manual selection
  allRecognitionResults?: Array<{
    itemId: string;
    itemName: string;
    confidence: number;
  }>;
}

export interface SegmentProgressBarProps {
  duration: number;
  segments: Segment[];
  currentTime?: number;
  onSegmentClick?: (idx: number) => void;
  onManualSelect?: (segmentIndex: number, selectedItem: { itemId: string; itemName: string; confidence: number }) => void;
}

export const STATUS_COLORS = {
  pending: '#f0f0f0',
  processing: '#1890ff',
  completed: '#52c41a',
  error: '#ff4d4f',
};

// Dynamic color palette for product recognition
export const PRODUCT_COLORS = [
  '#1890ff', '#52c41a', '#faad14', '#722ed1', '#f5222d',
  '#fa541c', '#13c2c2', '#eb2f96', '#2f54eb', '#52c41a',
  '#fadb14', '#a0d911', '#1890ff', '#722ed1', '#eb2f96',
  '#fa8c16', '#13c2c2', '#f5222d', '#2f54eb', '#52c41a'
];

// Color management for dynamic item recognition
class ColorManager {
  private itemColorMap: Map<string, string> = new Map();
  private usedColors: Set<string> = new Set();
  private colorIndex: number = 0;

  // Get color for a specific itemId
  getColorForItem(itemId: string | null | undefined): string {
    // Handle null/undefined itemId (recognition failed)
    if (!itemId) {
      return '#d9d9d9'; // Gray color for failed recognition
    }

    // Return existing color if already assigned
    if (this.itemColorMap.has(itemId)) {
      return this.itemColorMap.get(itemId)!;
    }

    // Assign new color
    const color = PRODUCT_COLORS[this.colorIndex % PRODUCT_COLORS.length];
    this.itemColorMap.set(itemId, color);
    this.usedColors.add(color);
    this.colorIndex++;

    return color;
  }

  // Get all assigned item-color mappings
  getAllItemColors(): Array<{ itemId: string; color: string }> {
    return Array.from(this.itemColorMap.entries()).map(([itemId, color]) => ({
      itemId,
      color
    }));
  }

  // Reset color assignments
  reset(): void {
    this.itemColorMap.clear();
    this.usedColors.clear();
    this.colorIndex = 0;
  }

  // Get color mapping for legend display
  getItemColorMapping(segments: Segment[]): Array<{ itemId: string; itemName: string; color: string }> {
    const uniqueItems = new Map<string, string>();

    segments.forEach(segment => {
      if (segment.itemId && segment.itemName) {
        uniqueItems.set(segment.itemId, segment.itemName);
      }
    });

    return Array.from(uniqueItems.entries()).map(([itemId, itemName]) => ({
      itemId,
      itemName,
      color: this.getColorForItem(itemId)
    }));
  }
}

// Global color manager instance
export const colorManager = new ColorManager();
