import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Modal, Card, Row, Col, Tag, Spin, Button, Progress, message } from 'antd';
import { PlayCircleOutlined, ClockCircleOutlined, StopOutlined } from '@ant-design/icons';
import VideoTimeline from './VideoTimeline';
import styles from './ProductMarkingModal.less';
import { request } from '@/utils/request';

interface ProductMarkingModalProps {
  visible: boolean;
  onCancel: () => void;
  videoRecord: any;
}

interface TimelineSegment {
  startTime: number;
  endTime: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
  productType?: number;
  confidence?: number;
  itemId?: string;
  itemName?: string;
  // Manual selection support
  isManuallySelected?: boolean;
  originalItemId?: string;
  originalItemName?: string;
  originalConfidence?: number;
  // Store all recognition results for manual selection
  allRecognitionResults?: Array<{
    itemId: string;
    itemName: string;
    confidence: number;
  }>;
}

interface WebSocketMessage {
  type: 'connection' | 'progress_update' | 'recognition_result' | 'recognition_complete' | 'error' | 'pong';
  taskId?: string;
  sessionId?: string;
  status?: string;
  currentSegment?: number;
  totalSegments?: number;
  percentage?: number;
  timestamp?: number;
  segmentIndex?: number;
  itemType?: number;
  confidence?: number;
  itemId?: string;
  itemName?: string;
  message?: string;
}

enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

const ProductMarkingModal: React.FC<ProductMarkingModalProps> = ({
  visible,
  onCancel,
  videoRecord
}) => {
  const [timelineSegments, setTimelineSegments] = useState<TimelineSegment[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(ConnectionStatus.DISCONNECTED);
  const [currentTaskId, setCurrentTaskId] = useState<string>('');
  const [totalSegments, setTotalSegments] = useState(0);
  const [currentSegment, setCurrentSegment] = useState(0);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // WebSocket connection management
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected, skipping...');
      return;
    }

    console.log('=== Starting WebSocket Connection ===');
    setConnectionStatus(ConnectionStatus.CONNECTING);

    // Use proxy path for WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/api/fastclip/ws/product-recognition`;

    console.log('WebSocket URL:', wsUrl);
    console.log('Current location:', window.location);
    console.log('Protocol:', protocol);
    console.log('Host:', window.location.host);
    console.log('Expected final URL after proxy:', `ws://localhost:8078/ws/product-recognition`);

    try {
      console.log('Creating WebSocket connection...');
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = (event) => {
        console.log('=== WebSocket Connected Successfully ===');
        console.log('Event:', event);
        console.log('ReadyState:', wsRef.current?.readyState);
        console.log('URL:', wsRef.current?.url);
        console.log('Protocol:', wsRef.current?.protocol);
        console.log('=======================================');

        setConnectionStatus(ConnectionStatus.CONNECTED);
        startHeartbeat();
      };

      wsRef.current.onmessage = (event) => {
        console.log('WebSocket message received:', event.data);
        try {
          const wsMessage: WebSocketMessage = JSON.parse(event.data);
          console.log('Parsed message:', wsMessage);
          handleWebSocketMessage(wsMessage);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error, 'Raw data:', event.data);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('=== WebSocket Connection Closed ===');
        console.log('Code:', event.code);
        console.log('Reason:', event.reason);
        console.log('WasClean:', event.wasClean);
        console.log('==================================');

        setConnectionStatus(ConnectionStatus.DISCONNECTED);
        stopHeartbeat();

        // Auto-reconnect after 3 seconds if not a clean close
        if (!event.wasClean) {
          console.log('Scheduling reconnection in 3 seconds...');
          reconnectTimeoutRef.current = setTimeout(connectWebSocket, 3000);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('=== WebSocket Error ===');
        console.error('Error event:', error);
        console.error('ReadyState:', wsRef.current?.readyState);
        console.error('URL:', wsRef.current?.url);
        console.error('=====================');

        setConnectionStatus(ConnectionStatus.ERROR);
      };

      console.log('WebSocket event handlers attached');

    } catch (error) {
      console.error('=== Failed to Create WebSocket ===');
      console.error('Error:', error);
      console.error('URL:', wsUrl);
      console.error('================================');
      setConnectionStatus(ConnectionStatus.ERROR);
    }
  }, []);

  const handleWebSocketMessage = useCallback((wsMessage: WebSocketMessage) => {
    console.log('Received WebSocket message:', wsMessage);

    switch (wsMessage.type) {
      case 'connection':
        console.log('WebSocket connection confirmed:', wsMessage.sessionId);
        break;

      case 'progress_update':
        if (wsMessage.currentSegment && wsMessage.totalSegments && wsMessage.percentage !== undefined) {
          setCurrentSegment(wsMessage.currentSegment);
          setTotalSegments(wsMessage.totalSegments);
          setProcessingProgress(wsMessage.percentage);
        }
        break;

      case 'recognition_result':
        if (wsMessage.segmentIndex !== undefined) {
          updateSegmentResult(
            wsMessage.segmentIndex,
            wsMessage.itemType || 0,
            wsMessage.confidence || 0,
            wsMessage.itemId,
            wsMessage.itemName
          );
        }
        break;

      case 'recognition_complete':
        setIsProcessing(false);
        message.success('商品识别完成！');
        break;

      case 'error':
        setIsProcessing(false);
        message.error(`识别失败: ${wsMessage.message}`);
        break;

      case 'pong':
        // Heartbeat response
        break;

      default:
        console.warn('Unknown message type:', wsMessage.type);
    }
  }, []);

  const updateSegmentResult = useCallback((segmentIndex: number, itemType: number, confidence: number, itemId?: string, itemName?: string) => {
    setTimelineSegments(prev =>
      prev.map((segment, index) =>
        index === segmentIndex ? {
          ...segment,
          status: 'completed' as const,
          productType: itemType,
          confidence,
          itemId,
          itemName,
          // Store only the actual recognition result
          allRecognitionResults: itemId ? [{
            itemId,
            itemName: itemName || '',
            confidence
          }] : []
        } : segment
      )
    );
  }, []);

  // Handle manual selection of product
  const handleManualSelect = useCallback((segmentIndex: number, selectedItem: { itemId: string; itemName: string; confidence: number }) => {
    setTimelineSegments(prev =>
      prev.map((segment, index) =>
        index === segmentIndex ? {
          ...segment,
          // Store original recognition result
          originalItemId: segment.itemId,
          originalItemName: segment.itemName,
          originalConfidence: segment.confidence,
          // Update with manually selected item
          itemId: selectedItem.itemId,
          itemName: selectedItem.itemName,
          confidence: selectedItem.confidence,
          isManuallySelected: true
        } : segment
      )
    );
  }, []);



  const startHeartbeat = () => {
    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000); // Send ping every 30 seconds
  };

  const stopHeartbeat = () => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  };

  useEffect(() => {
    if (videoRecord?.duration) {
      const segmentDurationMs = 10 * 1000;
      const totalSegs = Math.ceil(videoRecord.duration / segmentDurationMs);

      const segments: TimelineSegment[] = [];
      for (let i = 0; i < totalSegs; i++) {
        segments.push({
          startTime: i * segmentDurationMs,
          endTime: Math.min((i + 1) * segmentDurationMs, videoRecord.duration),
          status: 'pending'
        });
      }

      setTimelineSegments(segments);
      setTotalSegments(totalSegs);
    }
  }, [videoRecord]);

  useEffect(() => {
    if (visible) {
      connectWebSocket();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      stopHeartbeat();
    };
  }, [visible, connectWebSocket]);

  const handleStartProcessing = async () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      message.error('WebSocket连接未建立，请稍后重试');
      return;
    }

    if (!videoRecord?.id) {
      message.error('视频信息不完整');
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(0);
    setCurrentSegment(0);

    // Reset all segments to pending
    setTimelineSegments(prev =>
      prev.map(segment => ({ ...segment, status: 'pending' as const }))
    );

    try {
      // Generate task ID
      const taskId = `task-${Date.now()}-${videoRecord.id}`;
      setCurrentTaskId(taskId);

      // Call Java backend API to start video grounding
      const result = await request('/api/fastclip/item/startVideoGrounding', {
        method: 'POST',
        data: {
          videoId: videoRecord.id,
          videoPath: videoRecord.path,
          taskId: taskId,
          enableRealTimeUpdates: true,
          confidenceThreshold: 0.5
        },
      });

      // console.log('Video grounding result:', result);
      if (result.resultCode === 0) {
        console.log('Video grounding started with task ID:', result.result);
        // WebSocket will handle the real-time updates
      } else {
        throw new Error(result.resultDesc || 'Failed to start video grounding');
      }
    } catch (error) {
      console.error('Error starting video grounding:', error);
      message.error(`启动识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
      setIsProcessing(false);
    }
  };

  const handleStopProcessing = () => {
    setIsProcessing(false);
    setProcessingProgress(0);
    setCurrentSegment(0);
    // Reset segments to pending
    setTimelineSegments(prev =>
      prev.map(segment => ({ ...segment, status: 'pending' as const }))
    );
    message.info('已停止识别');
  };



  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED: return '已连接';
      case ConnectionStatus.CONNECTING: return '连接中...';
      case ConnectionStatus.ERROR: return '连接错误';
      default: return '未连接';
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED: return 'success';
      case ConnectionStatus.CONNECTING: return 'processing';
      case ConnectionStatus.ERROR: return 'error';
      default: return 'default';
    }
  };

  return (
    <Modal
      title="商品标记"
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      className={styles.productMarkingModal}
    >
      <div className={styles.modalContent}>

        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <PlayCircleOutlined />
              视频信息
            </div>
          }
          size="small"
          style={{ marginBottom: 16 }}
          extra={
            <Tag color={getConnectionStatusColor()}>
              {getConnectionStatusText()}
            </Tag>
          }
        >
          <Row gutter={16}>
            <Col span={8}>
              <div><strong>视频ID:</strong> {videoRecord?.id}</div>
            </Col>
            <Col span={8}>
              <div><strong>达人:</strong> {videoRecord?.sellerName}</div>
            </Col>
            <Col span={8}>
              <div><strong>时长:</strong> {formatTime(videoRecord?.duration || 0)}</div>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 8 }}>
            <Col span={12}>
              <div><strong>素材路径:</strong> {videoRecord?.path}</div>
            </Col>
            <Col span={12}>
              <div><strong>素材日期:</strong> {videoRecord?.startDate}</div>
            </Col>
          </Row>
        </Card>

        {isProcessing && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <div style={{ textAlign: 'center' }}>
              <Spin size="small" style={{ marginRight: 8 }} />
              正在识别视频商品...
            </div>
            <Progress
              percent={Math.round(processingProgress)}
              status="active"
              format={(percent) => `${currentSegment}/${totalSegments} (${percent}%)`}
              style={{ marginTop: 12 }}
            />
            <div style={{ marginTop: 8, textAlign: 'center', color: '#666', fontSize: '12px' }}>
              正在处理第 {currentSegment} 个片段，共 {totalSegments} 个片段
            </div>
          </Card>
        )}
        <Card 
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <ClockCircleOutlined />
              视频时间轴 (每段10秒)
            </div>
          }
          size="small"
          extra={
            timelineSegments.length > 0 && (
              <div style={{ display: 'flex', gap: 8 }}>
                {!isProcessing ? (
                  <Button
                    type="primary"
                    onClick={handleStartProcessing}
                    disabled={connectionStatus !== ConnectionStatus.CONNECTED}
                    icon={<PlayCircleOutlined />}
                  >
                    开始识别
                  </Button>
                ) : (
                  <Button
                    danger
                    onClick={handleStopProcessing}
                    icon={<StopOutlined />}
                  >
                    停止识别
                  </Button>
                )}
              </div>
            )
          }
        >
          <VideoTimeline
            segments={timelineSegments}
            isProcessing={isProcessing}
            onSegmentClick={(segment) => {
              console.log('Clicked segment:', segment);
            }}
            onManualSelect={handleManualSelect}
          />
        </Card>
      </div>
    </Modal>
  );
};

export default ProductMarkingModal;
