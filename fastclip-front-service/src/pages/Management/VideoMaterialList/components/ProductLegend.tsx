import React from 'react';
import { colorManager, type Segment } from './types';
import styles from './ProductMarkingModal.less';

interface ProductLegendProps {
  segments: Segment[];
}

const ProductLegend: React.FC<ProductLegendProps> = ({ segments }) => {
  // Collect unique products from current items and original items only
  const getAllProducts = () => {
    const allProducts = new Map<string, { itemId: string; itemName: string; color: string }>();

    segments.forEach(seg => {
      if (seg.status === 'completed') {
        // Add current displayed item (might be manually selected)
        if (seg.itemId) {
          const color = colorManager.getColorForItem(seg.itemId);
          allProducts.set(seg.itemId, {
            itemId: seg.itemId,
            itemName: seg.itemName || seg.itemId,
            color
          });
        }

        // Add original recognition result (if it was manually replaced)
        if (seg.originalItemId && seg.originalItemId !== seg.itemId) {
          const color = colorManager.getColorForItem(seg.originalItemId);
          allProducts.set(seg.originalItemId, {
            itemId: seg.originalItemId,
            itemName: seg.originalItemName || seg.originalItemId,
            color
          });
        }
      }
    });

    return Array.from(allProducts.values()).sort((a, b) =>
      a.itemId.localeCompare(b.itemId)
    );
  };

  const itemColorMapping = getAllProducts();

  // If no recognized items, don't show legend
  if (itemColorMapping.length === 0) {
    return null;
  }

  return (
    <div className={styles.productLegend}>
      <span className={styles.legendTitle}>已识别商品：</span>
      <div className={styles.legendItems}>
        {itemColorMapping.map(({ itemId, itemName, color }) => (
          <div key={itemId} className={styles.legendItem}>
            <div
              className={styles.colorDot}
              style={{ backgroundColor: color }}
            />
            <span className={styles.itemId}>{itemId}</span>
            <span className={styles.itemName}>{itemName}</span>
          </div>
        ))}
        
        {/* Show legend for failed recognition */}
        {segments.some(seg => seg.status === 'completed' && !seg.itemId) && (
          <div className={styles.legendItem}>
            <div
              className={styles.colorDot}
              style={{ backgroundColor: '#d9d9d9' }}
            />
            <span className={styles.itemId}>--</span>
            <span className={styles.itemName}>识别失败</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductLegend;
