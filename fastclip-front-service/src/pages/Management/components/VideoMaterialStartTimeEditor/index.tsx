import { Key, DragSortTable, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Space, Table, Tag } from 'antd';
import { useEffect, useState, useContext, useRef} from 'react';
import styles from './index.less'
import { timestampToTime } from '@/utils/dateUtils';
import services from '@/services/workbench'


const VideoMaterialStartTimeEditor = (props: any) => {

  const {editStartTimeRecord} =  props;

  const {getVideoMaterials, flagVideoMaterialStartScene} = services.WorkbenchController;
  const [videoMaterialList, setVideoMaterialList] = useState([])
  const [editFlags, setEditFlags] = useState([])


  const loadData = () => {
    getVideoMaterials({sellerId: editStartTimeRecord.sellerId, startDate: editStartTimeRecord.startDate}).then((res:any) => {
      setVideoMaterialList(res.result.data);
      setEditFlags(res.result.data.map((item:any) => false))
    });
  }

  useEffect(() => {
    loadData();
  }, [editStartTimeRecord]);

  const columns: ProColumns[] = [
    {
      dataIndex: 'sort',
      className: 'drag-visible',
      colSize:1
    },
    {
      title: editStartTimeRecord.startDate + '素材路径',
      render: (dom, item, index) => {
        return (
          <div>
              <div className={styles.Title}>{item.path}</div>
          </div>
        );
      },
      colSize:10
    },
    {
      title: '素材时长',
      render: (dom, item, index) => {
        return (
          <div >{timestampToTime(item.duration)}
          </div>
        );
      },
      colSize:10
    },
    {
      title: '开始时间',
      render: (dom, item, index) => {
        return (
          <div >{timestampToTime(item.startTime)}
          </div>
        );
      },
      colSize:10
    },{
      title: '场次标记',
      render: (dom, item, index) => {
        return (
          <div >
            {
            editFlags[index]
            ?
            <>
            <input size={10} defaultValue={videoMaterialList[index].startScene} onChange={(value) => changeStartScene(index, value)} />
            <a onClick= {() => submitFlag(index)}>确定</a> <a onClick= {() => cancelFlag(index)}>取消</a>
            </>
            :
            videoMaterialList[index].startSceneFlag
            ?
            <>
            <input size={10} value={videoMaterialList[index].startScene} disabled={true} />
            <a onClick={() => doFlag(index)}>修改</a> <a onClick= {() => deleteFlag(index)}>删除</a>
            </>
            :<a onClick={() => doFlag(index)}>标记</a>}
          </div>
        );
      },
      colSize:10
    }
  ];

  const cancelFlag = (index: number) => {
    setEditFlags(editFlags.map((item, i) => (i === index ? false : item)));
  }

  const  doFlag = (index: number) => {
    setEditFlags(editFlags.map((item, i) => (i === index ? true : item)));
  }

  const submitFlag = (index: number) => {
    flagVideoMaterialStartScene(videoMaterialList[index]).then((res) => {
      if(res.result) {
        message.success('提交成功');
        setEditFlags(editFlags.map((item, i) => (i === index ? false : item)));
        loadData();
        return res.result;
      }else{
        message.success('提交失败');
      }
    })
  }

  const deleteFlag = (index: number) => {
    flagVideoMaterialStartScene({id:videoMaterialList[index].id, startSceneFlag:false, startScene:0}).then((res) => {
      if(res.result) {
        message.success('删除成功');
        setEditFlags(editFlags.map((item, i) => (i === index ? false : item)));
        loadData();
        return res.result;
      }else{
        message.success('删除失败');
      }
    })
  }

  const changeStartScene = (index: number, event) => {
    let tmp = videoMaterialList;
    if(event.target.value > 0) {
      tmp[index].startSceneFlag = true;
      tmp[index].startScene = event.target.value;
      setVideoMaterialList(tmp);
    }
  }

  return (
    <>
      <ProTable
        headerTitle="只需标记场次的起始视频"
        className= {styles.ProList}
        rowKey="id"
        dataSource={videoMaterialList}
        // rowSelection={rowSelection}
        rowClassName={styles.ProListRow}
        // dragSortKey="sort"
        columns={columns}
        // onDragSortEnd={handleDragSortEnd}
        search={false}
        pagination={false}
        loading={false}
        options={{
          setting: false, // 隐藏设置按钮
          density: false, // 如果不需要密度选择，也可以一并关闭
          fullScreen: false, // 如果不需要全屏功能，同样可以关闭
          reload: false,
        }}
      />
    </>
  );
};

export default VideoMaterialStartTimeEditor;