import services from '@/services/workbench'

import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Checkbox, Form, message, Radio, Result, Select } from 'antd';
import { useContext, useEffect, useState } from 'react';
import styles from './index.less'

const waitTime = (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

const {getAllSellerList, getTopItems, createProjectApi} = services.WorkbenchController;

const CreateProjectModalForm: React.FC = (props: any) => {
  const {onSubmitSuccess} = props;
  const [form] = Form.useForm<{ name: string; company: string }>();
  const [itemOptions, setItemOptions] = useState([])
  const [sellerId, setSellerId] = useState(0)
  const [selectItemId, setSelectItemId] = useState(0)
  const [useLiveItem, setUseLiveItem] = useState(false)

  const loadItemData = (sellerId:any, itemName:any) => {
    getTopItems({sellerIds:[sellerId], hasProject:false, hasMaterial:true, itemName:itemName, itemType: useLiveItem?2:1, pageSize:100}).then(
      (res: any) =>  {
        let itemOptionsData = res.result.map(item => {
          if(useLiveItem) {
            return {value: item.id, label: item.itemName + '('+ item.createTime + ')'};
          }else {
            return {value: item.id, label: item.itemName + '('+ item.maxMaterialDate + ')'};
          }
        })
        setItemOptions(itemOptionsData);
      }
    )
  }

  useEffect(() => {
    loadItemData(sellerId, null)
  },[useLiveItem])

  const handleSelectSellerChange = (sellerId: number) => {
    setSellerId(sellerId);
    loadItemData(sellerId, null)
  }

  const handleSumbmit = (values:any) => {
    return createProjectApi({
      sellerId: values.seller,
      projectName: values.projectName,
      itemId: selectItemId,
      itemType: useLiveItem?2:1
    }).then((res:any) => {
      if(res.result) {
        message.success('提交成功');
        onSubmitSuccess(selectItemId);
        return res.result;
      }else{
        message.success('提交失败');
      }
    })
  }

  const onCheckboxChange = (e:any) => {
    setUseLiveItem(e.target.checked)
  }

  return (
    <ModalForm<{
      name: string;
      company: string;
    }>
      title="新建项目"
      trigger={
        <Button type="primary">
          <PlusOutlined />
          新建项目
        </Button>
      }
      width={1000}
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run'),
      }}
      submitTimeout={2000}
      onFinish={(values) => handleSumbmit(values)}
    >
      <ProForm.Group>
        <ProFormText
          width="md"
          name="projectName"
          label="项目"
          tooltip="最长为 24 位"
          placeholder="请输入项目名称"
        />
        {/* <Checkbox onChange={(e) => onCheckboxChange(e)}>使用直播商品</Checkbox> */}
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          request={async () => getAllSellerList().then(
            (res: any) => 
              res.result.data.map(item => {return {value: item.sellerId, label: item.sellerName};}
            )
          )}
          width="xs"
          name="seller"
          label="达人"
          onChange={(value: number) => handleSelectSellerChange(value)}
        />
        <div>
          <div>商品</div>
          <div><Select
            options={itemOptions}
            showSearch
            style= {{width : '800px', marginTop : '8px'}}
            onSearch={(itemName) =>{loadItemData(sellerId,itemName)}}
            onChange={(value) => {setSelectItemId(value)}}
            filterOption={false}
          />
          </div>
        </div>
      </ProForm.Group>
    </ModalForm>
  );
};

export default CreateProjectModalForm;