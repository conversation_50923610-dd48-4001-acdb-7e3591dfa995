import React, { useContext, useEffect, useRef, useState } from 'react';
import io from 'socket.io-client';
import ProjectContext from '../../Project/compoents/ProjectContext';
import './index.less';
import { timestampToTime } from '@/utils/dateUtils';
import services from '@/services/workbench';
import { message } from 'antd';
import Modal from 'antd/lib/modal';

const VideoPlay = (props: any) => {
  const {projectId} = props;
  const {isPlayVideo, setIsPlayVideo, playVideoReq, curTimestamp, setCurTimestamp, setPlayVideoReq, curSubtitlesId} = useContext(ProjectContext);
  const coverCanvas = useRef(null)
  const coverMaskCanvas = useRef(null)
  const videoSocket = useRef()
  const {setCoverApi, getCoverApi} = services.WorkbenchController;
  // const audioSocket = useRef<WebSocket>()
  const audioMediaSource = useRef<MediaSource>()
  const [videoTime, setVideoTime] = useState(0);
  const [audioTime, setAudioTime] = useState(0);

  useEffect(() => {
    setIsPlayVideo(false);
  },[]);

  useEffect(() => {
    // audioMediaSource.current?.endOfStream()
    videoSocket.current?.close()
    // audioSocket.current?.close()

    if (isPlayVideo) {
      const protocol = window.location.protocol;
      const url = '***************:8078/api/fastclip/videoPlay'
      if (protocol === 'https:') {
        videoSocket.current = new WebSocket(`wss://${url}`);
      } else {
        videoSocket.current = new WebSocket(`ws://${url}`);
      }

      // videoSocket.current = new WebSocket('ws://*************:8078/api/fastclip/videoPlay');
      // videoSocket.current = new WebSocket('ws://*************:8088/api/fastclip/videoPlay');
      // videoSocket.current = new WebSocket('ws://localhost:8088/api/fastclip/videoPlay');
 
      // audioSocket.current = new WebSocket('ws://127.0.0.1:8078/api/fastclip/videoPlay');
  
  
      const videoMineCodec = 'video/mp4; codecs="avc1.42E01E, mp4a.40.2"';
      const aduioMineCodec = 'audio/mpeg';
  
      // 取得dom元素

      let videoCanvas = document.getElementById("videoCanvas");

      let audioPlay = document.getElementById("audioPlay");
      audioPlay.play()

      audioMediaSource.current = new MediaSource();
      
      let audioSourceBuffer:SourceBuffer;

      // 浏览器兼容性检查
      if ('MediaSource' in window && MediaSource.isTypeSupported(videoMineCodec) && MediaSource.isTypeSupported(aduioMineCodec)) {
        // 创建ObjectURL
        audioPlay.src = window.URL.createObjectURL(audioMediaSource.current);

        audioMediaSource.current.addEventListener('sourceopen', () => {
          // 给 sourceBuffer 赋值
          audioSourceBuffer = audioMediaSource.current?.addSourceBuffer(aduioMineCodec)
        })
      } else {
          console.error('unsupported MIME type')
      } 

      videoSocket.current.onopen = () => {
        console.log('Video WebSocket connection opened');
        videoSocket.current?.send(JSON.stringify({...playVideoReq, videoOrAudio:'video'}))
      };

      videoSocket.current.onmessage = (event) => {
        // isPlayVideo && handleVideoOrAudioFrame(event.data, videoSourceBuffer);
        const data = JSON.parse(event.data);
        setVideoTime(data.timestamp)
        const ctx = videoCanvas.getContext('2d');
        const img = new Image();
        img.onload = () => {
          ctx.drawImage(img, 0, 0, data.imageWidth, data.imageHeight);
        };
        img.src = `data:image/png;base64,${data.imageData}`;
        setCurTimestamp(data.videoTimestamp);
        setVideoTime(data.videoTimestamp)
        setAudioTime(data.audioTimestamp)
        if(!audioSourceBuffer.updating && data.sampleData != null) {
          audioSourceBuffer.abort();
          const arrayBufferMeta = stringToArrayBuffer(data.sampleData, 'base64');
          audioSourceBuffer.appendBuffer(arrayBufferMeta?.buffer);
        }else{
          console.log('audioSourceBuffer updating...')
        }
      };
  
      // audioSocket.current.onopen = () => {
      //   console.log('Audio WebSocket connection opened');
      //   audioSocket.current?.send(JSON.stringify({...playVideoReq, videoOrAudio:'audio'}))
      // };
  
      // let count = 0;
      // let dataArray = '';
      // audioSocket.current.onmessage = (event) => {
      //   // isPlayVideo && handleVideoOrAudioFrame(event.data, audioSourceBuffer);
      //   const data = JSON.parse(event.data);
      //   setAudioTime(data.timestamp)
      //   const arrayBufferMeta = stringToArrayBuffer(data.data, 'base64');
      //   if(!audioSourceBuffer.updating) {
      //     audioSourceBuffer.appendBuffer(arrayBufferMeta?.buffer);
      //   }
      // };
    }
  }, [isPlayVideo, playVideoReq]);

  const onHandlePlay = () => {
    setIsPlayVideo(true);
    let curVideoReq = playVideoReq;
    curVideoReq.subtitlesId = curSubtitlesId;
    setPlayVideoReq(curVideoReq);
  }

  const onHandleStop = () => {
    setIsPlayVideo(false);
  }

  const handleVideoOrAudioFrame = (dataJson:any, sourceBuffer:SourceBuffer) => {
    const data = JSON.parse(dataJson);
    const arrayBufferMeta = stringToArrayBuffer(data.data, 'base64')
    if(audioMediaSource.current.duration >=10 ) {
      sourceBuffer.remove(0,10)
    }
    if(!sourceBuffer.updating) {
      sourceBuffer.appendBuffer(arrayBufferMeta?.buffer);
    }
      // if(data.type == 1) {
        // const ctx = canvasRef.current.getContext('2d');
        // const img = new Image();
        // img.onload = () => {
        //   ctx.drawImage(img, 0, 0, canvasRef.current.width, canvasRef.current.height);
        // };
        // img.src = `data:image/png;base64,${data.data}`;
      // }else {
        // arrayBufferMeta && audioContext.decodeAudioData(arrayBufferMeta.buffer, function(buffer) {
        //       const source = audioContext.createBufferSource();
        //       source.buffer = buffer;
        //       source.connect(audioContext.destination);
        //       source.start(0);
        //     });
        
        // if(!sourceBuffer.updating) {
          // sourceBuffer.appendBuffer(arrayBufferMeta?.buffer);
        // }
        // audioPlay.src = `data:audio/mpeg;base64,${data.data}`;
      // }
    // }
  };


  const stringToArrayBuffer = (string:string, type:string) => {
    // 无论怎样，要先把数据转成js里的ArrayBuffer
    if (type === 'base64') {
      const decodeStr = window.atob(string)
      const len = decodeStr.length;
      const bytes = new Int8Array(len);
    
      for (let i = 0; i < len; i++) {
        bytes[i] = decodeStr.charCodeAt(i);
      }
      return  new Uint8Array(bytes);
    }
    if (type === 'hex') {
        // remove the leading 0x
        const hexString = string.replace(/^0x/, '');

        // ensure even number of characters
        if (hexString.length % 2 != 0) {
            console.log('WARNING: expecting an even number of characters in the hexString');
        }

        // check for some non-hex characters
        const bad = hexString.match(/[G-Z\s]/i);
        if (bad) {
            console.log('WARNING: found non-hex characters', bad);
        }

        // split the string into pairs of octets
        const pairs = hexString.match(/[\dA-F]{2}/gi);

        // convert the octets to integers
        const integers = pairs.map(function (s) {
            return parseInt(s, 16);
        });

        const array = new Uint8Array(integers);
        // console.log(array);
        return array;
    }
    return null;
}

const [cropperPosition, setCropperPosition] = useState({ x: 0, y: 0});
const [rect, setRect] = useState({ w:0, h:0});
const [coverData, setCoverData] = useState('');
const [coverVisable, setCoverVisable] = useState(false);
const [isCoverSetting, setIsCoverSetting] = useState(false);
const [isPress, setIsPress] = useState(false)
const [resizeLeft, setResizeLeft] = useState(false)
const [resizeRight, setResizeRight] = useState(false)
const [moveRect, setMoveRect] = useState(false)
const [cursorStyle, setCursorStyle] = useState('auto')
const [pressStartPosition, setPressStartPosition] = useState({ x:0, y:0});

const onHandleSetCover = () => {
  setIsCoverSetting(true);
  document.execCommand('cut');
  const canvas = coverMaskCanvas.current;
  const context = canvas.getContext('2d');;
  const rectWidth = canvas.width -40;
  const rectHeight = rectWidth * 4/3;
  // 设置截图起始位置
  const x = 20;
  const y = (canvas.height - rectHeight)/2;
  setCropperPosition({ x: x/canvas.width, y: y/canvas.height});
  setRect({w:rectWidth/canvas.width, h:rectHeight/canvas.height})
  context.beginPath();
  context.clearRect(0,0, canvas.width, canvas.height);
  context.rect(x, y, rectWidth, rectHeight);
  context.strokeStyle= 'red';
  context.stroke();
}

const handleCancelCover = () => {
  const ctx = coverCanvas.current.getContext('2d');
  ctx.clearRect(0,0,coverCanvas.current.width,coverCanvas.current.height);
  setCoverVisable(false);
  setIsCoverSetting(false);
  setCoverData({data:'',width:0, height:0});
}

const handleConfirmCover = () => {
  setCoverApi({
    projectId: projectId,
    videoId: playVideoReq.videoId,
    timestamp: curTimestamp,
    startX:cropperPosition.x,
    startY:cropperPosition.y,
    width:rect.w,
    height:rect.h
  }).then((res:any) => {
    if(res.result) {
      message.success('设置成功');
      return res.result;
    }else{
      message.error('设置失败');
    }
  })
  setCoverVisable(false);
}

const handleMouseDown = (event) => {
  setPressStartPosition({x:event.clientX, y:event.clientY})
  if(isCoverSetting && (resizeLeft || resizeRight || moveRect)) {
    setIsPress(true); 
  }
};

useEffect(() => {
  if(resizeLeft || resizeRight) {
    setCursorStyle('nwse-resize');
  }
  else if(moveRect) {
    setCursorStyle('pointer')
  }else{
    setCursorStyle('auto')
  }
}, [resizeLeft, resizeRight, moveRect])

const handleMouseMove = (event) => {
  const canvas = coverMaskCanvas.current;
  if(canvas === null) {
    return;
  }
  const rectCanvas = canvas.getBoundingClientRect();
  if(isCoverSetting && isPress && (resizeLeft || resizeRight || moveRect)) {
    if(resizeLeft) {
      const endX = event.clientX > rectCanvas.right ? rectCanvas.right:event.clientX  - rectCanvas.left;
      const rectWidth = (cropperPosition.x  + rect.w) * rectCanvas.width  -endX;
      const rectHeight = rectWidth * 4/3;
      const startPX = endX/canvas.width; 
      const startPY = (rect.h * canvas.height - rectHeight +  cropperPosition.y *  canvas.height)/canvas.height;
      setRect({w:rectWidth/canvas.width, h:rectHeight/canvas.height})
      setCropperPosition({x:startPX, y:startPY});
    }else if(resizeRight) {
      const endX = event.clientX > rectCanvas.right ? rectCanvas.right:event.clientX;
      const rectWidth = endX - rectCanvas.left - cropperPosition.x * rectCanvas.width;
      const rectHeight = rectWidth * 4/3;
      setRect({w:rectWidth/canvas.width, h:rectHeight/canvas.height})
    }else if(moveRect) {
      let startX = cropperPosition.x;
      let startY = cropperPosition.y; 
      const gapX = event.clientX - pressStartPosition.x;
      const gapY = event.clientY - pressStartPosition.y;
      console.log("cropperPositionX" + cropperPosition.x);
      console.log("cropperPositionY" + cropperPosition.y);
      console.log("gapX" + gapX);
      console.log("gapY" + gapY);
      const movedStartX = cropperPosition.x * rectCanvas.width + gapX
      const movedEndX = cropperPosition.x * rectCanvas.width + rect.w * rectCanvas.width  + gapX
      const movedStartY = cropperPosition.y * rectCanvas.height + gapY;
      const movedEndY = cropperPosition.y * rectCanvas.height + rect.h * rectCanvas.height  + gapY
      console.log("movedStartX" + movedStartX);
      console.log("movedStartY" + movedStartY);
      if(movedStartX > 0  &&  movedEndX < rectCanvas.width) {
        startX = movedStartX/rectCanvas.width;
      }
      if(movedStartY > 0 && movedEndY < rectCanvas.height) {
        startY = movedStartY/rectCanvas.height;
      } 
      console.log("startX" + startX);
      console.log("startY" + startY);
      setPressStartPosition({x:event.clientX,y:event.clientY})
      setCropperPosition({x:startX, y:startY});
    }
  }
  else if(isCoverSetting &&  (
    event.clientX>=cropperPosition.x * rectCanvas.width + rectCanvas.left - 10  && event.clientX<=cropperPosition.x * rectCanvas.width + rectCanvas.left + 10
    ||
    (event.clientY>=cropperPosition.y * rectCanvas.height + rectCanvas.top - 10  && event.clientY<=cropperPosition.y * rectCanvas.height + rectCanvas.top + 10)
  )) {
    setMoveRect(false);
    setResizeLeft(true);
    setResizeRight(false);
  }
  else if(isCoverSetting &&  (
    (event.clientX>=cropperPosition.x * rectCanvas.width + rectCanvas.left + rect.w * rectCanvas.width - 10  && event.clientX<=cropperPosition.x * rectCanvas.width + rect.w * rectCanvas.width + rectCanvas.left + 10)
    ||
    (event.clientY>=cropperPosition.y * rectCanvas.height + rectCanvas.top + rect.h * rectCanvas.height - 10  && event.clientY<=cropperPosition.y * rectCanvas.height + rectCanvas.top + rect.h * rectCanvas.height + 10)
  )){
    setMoveRect(false);
    setResizeLeft(false);
    setResizeRight(true);
  }else if(isCoverSetting &&  (
    event.clientX>cropperPosition.x * rectCanvas.width + rectCanvas.left  + 10 && event.clientX<cropperPosition.x * rectCanvas.width + rect.w * rectCanvas.width + rectCanvas.left - 10
    &&
    event.clientY>cropperPosition.y * rectCanvas.height + rectCanvas.top + 10  && event.clientY<cropperPosition.y * rectCanvas.height + rectCanvas.top + rect.h * rectCanvas.height - 10
  )) {
    setMoveRect(true);
    setResizeLeft(false);
    setResizeRight(false);
  }else{
    setMoveRect(false);
    setResizeLeft(false);
    setResizeRight(false);
    setIsPress(false);
  }
  // if (isCoverSetting && coverMaskCanvas.current != null && isPress) {
  //   const canvas = coverMaskCanvas.current;
  //   const rectOfCanvas = canvas.getBoundingClientRect();
  //   const endX = event.clientX > rectOfCanvas.right ? rectOfCanvas.right:event.clientX  - rectOfCanvas.left;
  //   const rectWidth = endX - cropperPosition.x * canvas.width;
  //   const rectHeight = rectWidth * 4/3;
  //   setRect({w:rectWidth/canvas.width, h:rectHeight/canvas.height})
  // }
};

useEffect(() => {
  const canvas = coverMaskCanvas.current;
  const context = canvas.getContext('2d');
  context.beginPath();
  context.clearRect(0,0, canvas.width, canvas.height);
  const rectWidth = rect.w * canvas.width;
  const rectHeight = rectWidth * 4/3;
  context.rect(cropperPosition.x * canvas.width, cropperPosition.y * canvas.height, rectWidth, rectHeight);
  console.log(cropperPosition.x * canvas.width + ',' + cropperPosition.y * canvas.height + ',' +  rectWidth  + ',' +  rectHeight)
  context.strokeStyle= 'red';
  context.stroke();
},[cropperPosition, rect])

const handleSubmit = (event) => {
  if (isCoverSetting && coverMaskCanvas.current != null) {
    setIsPress(false);
    setIsCoverSetting(false);
    const canvas = coverMaskCanvas.current;
    const context = canvas.getContext('2d');
    context.clearRect(0,0, canvas.width, canvas.height);
    loadCoverData();
    setCoverVisable(true);
  }
};

const cancelSetCover = (event) => {
    setIsPress(false);
    setIsCoverSetting(false);
    const canvas = coverMaskCanvas.current;
    const context = canvas.getContext('2d');
    context.clearRect(0,0, canvas.width, canvas.height);
};

const handleMouseUp = (event) => {
    setIsPress(false);
};

const loadCoverData = () => {
  getCoverApi({
    projectId: projectId,
    videoId: playVideoReq.videoId,
    timestamp: curTimestamp,
    startX:cropperPosition.x,
    startY:cropperPosition.y,
    width:rect.w,
    height:rect.h
  }).then((res:any) => {
    if(res.result) {
      setCoverData("data:" + res.result);
    }else{
      message.error('获取封面失败');
    }
  })
}

useEffect(()=>{
  if(coverCanvas.current) {
    const ctx = coverCanvas.current.getContext('2d');
    const img = new Image();
    img.onload = () => {
      ctx.drawImage(img, 0, 0, 450, (coverData.height/coverData.width)*450);
    };
    img.src=`data:image/png;base64,${coverData.data}`;
  }
},
[coverData])
 
  return <>
    <div className='videoPlay'
        onMouseUp={handleMouseUp}
    >
      <div className='canvas' style={{cursor: cursorStyle}}>
        <canvas className='canvasContent' id='videoCanvas' width='450' height='760'></canvas>
        <canvas className='canvasMask' ref={coverMaskCanvas} width='450' height='760'
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
         ></canvas>
      </div>
      <div>videoTime:{timestampToTime(videoTime/1000)}</div>
      <div>audioTime:{timestampToTime(audioTime/1000)}</div>
    <audio id='audioPlay' autoPlay></audio>
    </div>
    <div className='controlls'>
      <div className='playButton'><button onClick={onHandlePlay}>播放</button></div>  
      <div className='stopButton'><button onClick={onHandleStop}>暂停</button></div>
      {!isCoverSetting?
      <div className='setCoverButton'><button onClick={onHandleSetCover}>封面设置</button></div>:<>
      <div className='setCoverButton'><button onClick={handleSubmit}>封面提交</button></div>
      <div className='setCoverButton'><button onClick={cancelSetCover}>取消设置</button></div></>
      }
    </div>
    <Modal className='coverModal' open={coverVisable} title="封面设置" onCancel={handleCancelCover} onOk={handleConfirmCover}>
      <canvas ref={coverCanvas} width='450' height='600'></canvas>
    </Modal>
  </>
};
 
export default VideoPlay;