.videoPlay {
    padding-top: 10px;
    text-align: center;
}

.canvas{
    text-align: center;
    background: #000000b7;
    position: relative;
    .canvasContent{
        position: absolute;
    }
    .canvasMask{
        position: relative;
    }
}

.controlls{
    display: flex;
    text-align: center;

    .playButton{
        text-align: right;
        flex:1;
        padding: 10px;
    }

    .stopButton{
        flex:1;
        text-align: left;
        padding: 10px;
    }

    .setCoverButton{
        flex:1;
        text-align: left;
        padding: 10px;
    }
}

.coverModal{
    flex:1;
    text-align: center;
    padding: 10px;
}
