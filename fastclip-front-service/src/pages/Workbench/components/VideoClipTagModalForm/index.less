.tagSelect {
    width        : 275px;
    height       : 2rem;
    border-color : transparent;
    border       : 0 !important;
    border-radius: 4px;
    color        : #FFF;

    .option-list {
      color: red;
      ;
    }

    .ant-select-selector {
      padding: 0 16px;
    }

    .ant-select-selection-item {
      padding-left: 4px;
    }

    .ant-select-arrow {
      width     : .75rem;
      border    : .375rem solid transparent;
      border-top: .375rem solid #fff;
      margin-top: -0.25rem;

      svg {
        display: none;
      }
    }
}

.Action{
  border: 1px solid rgba(15, 114, 239, 50%);
  cursor: pointer;
  font-size: 8px;
  color: #000;
  border-radius: 2px;
  width: 40px;
  margin-right: 10px;
  height: 15px;
  margin-top: 0;
  background: #e0dfdf;
}
