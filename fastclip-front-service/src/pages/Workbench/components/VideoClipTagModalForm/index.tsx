import services from '@/services/workbench'

import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Form, message, Result } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less'


const {getVideoClipTagList, tagVideoClip} = services.WorkbenchController;

const VideoClipTagModalForm = (props: any) => {
  const {onSubmitSuccess, videoClipId, tags} = props;
  const [form] = Form.useForm<{ name: string; company: string }>();
  const [tagValues, setTagValues] = useState([])

  useEffect(() => {
    if(tags != null) {
      let iTagValues = tags.map((tag:any) => {return {
        label:tag.name,
        value:tag.code
      }})
      setTagValues(iTagValues);
    }
  }, [])

  const handleSumbmit = (values:any) => {
    tagVideoClip({
      videoClipId: videoClipId,
      tags: tagValues
    }).then((res:any) => {
      if(res.result) {
        message.success('提交成功');
        onSubmitSuccess();
        return res.result;
      }else{
        message.success('提交失败');
      }
    })
  }

  const handleSelectTagChange = (tags: []) => {
    setTagValues(tags)
  }

  return (
    <ModalForm
      title={"视频片段打标(id:" + videoClipId + ")"}
      trigger={
        <Button className={styles.Action}>
          打标
        </Button>
      }
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true
      }}
      width='300px'
      submitTimeout={2000}
      onFinish={(values) => handleSumbmit(values)}
    >
      <ProForm.Group>
        <ProFormSelect 
          request={async () => getVideoClipTagList().then(
            (res: any) => 
              res.result.map(item => {return {value: item.code, label: item.name};}
            )
          )}
          fieldProps={{
            mode: 'multiple',
          }}
          width="lg"
          name="tags"
          label="标签"
          onChange={(value: []) => handleSelectTagChange(value)}
          initialValue ={tagValues}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default VideoClipTagModalForm;