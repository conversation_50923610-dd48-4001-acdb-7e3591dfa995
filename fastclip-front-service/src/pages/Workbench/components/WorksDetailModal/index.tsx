import services from '@/services/workbench'

import { Button, Form, Input, message, Modal, Result } from 'antd';
import { useContext, useEffect, useRef, useState } from 'react';

const waitTime = (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

const WorksDetailModal: React.FC = (props: any) => {
  const {worksId, modalVisable, onVisiableChange} = props;

  const [isModalVisable, setIsModalVisable] = useState(false)

  const {getWorks} = services.WorkbenchController;
  const [worksData, setWorksData] = useState(); 
  const worksNameRef = useRef(null);
  const [isCopied, setIsCopied] = useState(false);

  useEffect(() => {
    setIsModalVisable(modalVisable);
  }, [modalVisable])

  useEffect (() => {
    worksId && getWorks({worksId:worksId}).then(
      (res: any) =>  {
        setWorksData(res?.result.data[0]);
      }
    )
  }, [worksId])

  const handleCancel = () => {
    setIsModalVisable(false);
    onVisiableChange(false);
  }

  const copyText = (index:number) => {
    if(index == 1) {
      // navigator.clipboard.writeText(worksData?.worksName)
      worksNameRef.select()
    }else if(index == 2) {
      navigator.clipboard.writeText(worksData?.worksTag)
    }else if(index == 3) {
      navigator.clipboard.writeText(worksData?.worksDesc)
    }else if(index == 4) {
      navigator.clipboard.writeText(worksData?.item.shareUrl)
    }
    
  };

  return (
    <Modal
      open={isModalVisable}
      title="作品详情"
      width={860}
      onCancel={handleCancel}
    >作品名称： <a onClick={() => copyText(1)}>copy</a>
      <Input
        ref={worksNameRef}
        value={worksData?.worksName}
        disabled={true}
      />
     作品标签：<a onClick={() => copyText(2)}>copy</a>
      <Input
        // value={worksData?.worksTag}
        value={"****"}
        disabled={true}
      />
      作品描述：<a onClick={() => copyText(3)}>copy</a>
      <Input.TextArea maxLength={1000} rows={8} 
      value={worksData?.worksDesc} 
      disabled={true}/>
      商品链接：<a onClick={() => copyText(4)}>copy</a>
      <Input.TextArea maxLength={1000} rows={2} value={worksData?.item.shareUrl} disabled={true}/>
      
    </Modal>
  );
};

export default WorksDetailModal;