import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, message, Modal, Space, Tag } from 'antd';
import React, { useContext, useRef, useState } from 'react';
import services from '@/services/workbench';
import CreateProjectModalForm from '../components/CreateProjectModalForm';
import { SortOrder } from 'antd/es/table/interface';
import ProjectContext from '../Project/compoents/ProjectContext';

const ProjectList: React.FC = () => {

  const columns: ProColumns<API.ProjectVO>[] = [
    {
      title: '项目id',
      dataIndex: 'id',
      width: 60,
      render: (text, record) => (<a href={'./project?projectId='+ record.id}>{text}</a>),
      search: false
    },
    {
      title: '项目名称',
      width: 80,
      dataIndex: 'projectName',
      tooltip: '标题过长会自动收缩',
      render: (text, record) => (<a href={'./project?projectId=' + record.id}>{text}</a>),
    },
    {
      disable: true,
      title: '项目状态',
      dataIndex: 'status',
      filters: true,
      onFilter: true,
      ellipsis: true,
      width: 50,
      valueType: 'select',
      valueEnum: {
        1: {
          text: '已发布',
        },
        0: {
          text: '草稿',
        },
      },
      search: false
    },
    {
      disable: true,
      width: 300,
      title: '商品名称',
      dataIndex: 'itemName',
      search: true
    },
    {
      disable: true,
      width: 90,
      title: '作品数量',
      dataIndex: 'worksCount',
      search: true,
      sorter: true,
    },
    {
      disable: true,
      title: '达人名称',
      width: 100,
      dataIndex: 'sellerName',
      search: true
    },
    {
      disable: true,
      title: '最近素材',
      width: 100,
      dataIndex: 'maxMaterialDate',
      search: true
    },
    {
      disable: true,
      title: '作品数量',
      width: 50,
      dataIndex: 'worksCount',
      search: false
    },
    {
      title: '创建人',
      width: 70,
      dataIndex: 'userName',
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      key: 'showTime',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 100,
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 80,
      key: 'option',
      valueType: 'option',
      render: (text, record) => [
        <a key='delete' onClick={ () => {handelDeleteSubmit(record)}}>删除</a>,
      ],
    },
  ];

  const actionRef = useRef<ActionType>();
  const {getProjectList, justifyItemPromotion, deleteProject} = services.WorkbenchController
  const [openJustifyResult, setOpenJustifyResult] = useState(false);
  const [promotionResult, setPromotionResult] = useState('');
  
  const refreshTable = () => {
    actionRef.current?.reload();
  }

  const onSubmitCreateSuccess = (itemId:any) => {
    refreshTable();
    // onHandleJustifyPromotion(itemId);
  }

  const handelDeleteSubmit = (record) => {
    deleteProject({id: record.id}).then((res) => {
      if(res.result) {
        message.success("删除成功！");
        refreshTable();
      }else {
        message.error("删除失败!");
      }
    })
  }


  const onHandleJustifyPromotion = (id:any) => {
    justifyItemPromotion({itemId:id}).then((res) => {
      setOpenJustifyResult(true);
      if(res.result) {
        setPromotionResult("商品id("+id+")校验通过，该商品可以推广！");
      }else {
        setPromotionResult("商品id("+id+")校验失败，该商品不可以推广！");
      }
    })
  }

  return (
    <>
    <ProTable<API.ProjectVO>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort, filter) => {
        return (
          await 
          getProjectList({...params, pageNum: params.current}).then((res:any) => ({
            data: res.result.data.map((item:any)=>({...item, userName:item.creator.userName})),
            total: res.result.total,
            sucess: res.resultCode
          })));
      }}
      editable={{
        type: 'multiple',
      }}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        defaultValue: {
          option: { fixed: 'right', disable: true },
        },
        onChange(value) {
          console.log('value: ', value);
        },
      }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
        defaultCollapsed: false,
      }}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      form={{
        // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              created_at: [values.startTime, values.endTime],
            };
          }
          return values;
        },
      }}
      pagination={{
        pageSize: 15,
        onChange: (page) => console.log(page),
      }}
      dateFormatter="string"
      headerTitle="项目列表"
      toolBarRender={() => [
        // <Button
        //   key="button"
        //   icon={<PlusOutlined />}
        //   onClick={() => {
        //     actionRef.current?.reload();
        //   }}
        //   type="primary"
        // >
        //   新建
        // </Button>,
        <CreateProjectModalForm onSubmitSuccess= {(itemId:any) => onSubmitCreateSuccess(itemId)} />,
        <Dropdown
          key="menu"
          menu={{
            items: [
              {
                label: '1st item',
                key: '1',
              },
              {
                label: '2nd item',
                key: '2',
              },
              {
                label: '3rd item',
                key: '3',
              },
            ],
          }}
        >
          <Button>
            <EllipsisOutlined />
          </Button>
        </Dropdown>,
      ]}
    />
    <Modal open={openJustifyResult} onCancel={ () => setOpenJustifyResult(false)}>
        <div>
          {promotionResult}
        </div>
      </Modal>
    </>
  );
}
export default ProjectList;
