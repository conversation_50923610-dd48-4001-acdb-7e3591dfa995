.Subtiles {
  display: flex;

  .SubtilesProList {
    flex: 1;

    .ProListContent {
      /* 设置一个固定的高度 */
      height: 730px;
      overflow-y: scroll; /* 添加垂直滚动条 */
      font-size: 12px;
    }
  }
}



.ProListRow {
  border-radius: 10px;
  background: #f1f1f1; /* 选中之后的颜色 */
  border: 1px;
  margin-top: 2px;
}

.ProListAddedRow {
  border-radius: 4px;
  background: #e0dfdf; /* 选中之后的颜色 */
  border: 1px;
  margin-top: 2px;
  color: #9b3131;
}

.ProListPlayRow {
  border-radius: 4px;
  background: #ea7a7a; /* 选中之后的颜色 */
  border: 1px;
  margin-top: 2px;
}

/* 为了避免在Webkit内核的浏览器中出现滚动条的时候产生双滚动条，可以添加以下样式 */
.ProList::-webkit-scrollbar {
  width: 12px; /* 滚动条宽度 */
}

.ProList::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道颜色 */
}

.ProList::-webkit-scrollbar-thumb {
  background: #888; /* 滚动条手柄颜色 */
}

.ProList::-webkit-scrollbar-thumb:hover {
  background: #555; /* 滚动条手柄hover颜色 */
}

.Card{
  border: 1px;
}

.Title {
  font-size: 11px;
  width: 150px;
}

.Content {
  font-size: 12px;
  color: #000;
}

.Content {
  font-size: 12px;
  color: #000;
}

.Action{
  border: 1px solid rgba(15, 114, 239, 50%);
  padding: 1px;
  cursor: pointer;
  font-size: 8px;
  border-radius: 2px;
  color: #000;
  width: 40px;
}

.Controlls {
  display: flex;
}
.Controll {
  display: flex;
  flex: 1;
}

.SelectControlls {
  margin: 10px;
  color: #888;
  height: 40px;
  display: flex;
  background-color: #e0dfdf;
  border-radius: 10px;
}
.SelectControll {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}