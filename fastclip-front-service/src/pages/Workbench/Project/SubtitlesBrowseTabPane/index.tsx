import { Key, ProList } from '@ant-design/pro-components';
import { Button, Input, message, Modal, Space, Tag } from 'antd';
import { useEffect, useState, useContext, useRef } from 'react';
import SubtitlesLocationList from '../compoents/SubtitlesLocationList'
import styles from './index.less'
import { timestampToTime } from '@/utils/dateUtils';
import services from '@/services/workbench'
import { addSubtitlesToProject } from '@/services/workbench/WorkbenchController';
import globalState from '@/services/workbench copy';
import ProjectContext from '../compoents/ProjectContext';
import ScrollTo from 'react-scroll-to';


const SubtitlesBrowseTabPane = (props: any) => {

  const {projectId, itemId, videoMaterial, itemType} =  props;

  const {subtitlesLoadFlag, setSubtitlesCutLoadFlag, setPlayVideoReq, playVideoReq, setIsPlayVideo, curTimestamp, setCurSubtitlesId} = useContext(ProjectContext);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [startTs, setStartTs] = useState<number>(0)
  const [endTs, setEndTs] = useState<number>(0)
  const [items, setItems] = useState([])
  const [itemStyles, setItemStyles] = useState([])
  const [preScrollToIndex, setPreScrollToIndex] = useState(0);

  const [subtitlesData, setSubtitlesData] =  useState<Array<API.SubtitlesRecord>>([]);
  const {searchSubtitles, addSubtitlesToProject, removeSubtitlesCut, editSubtitles} = services.WorkbenchController;
  const subtitlesList = useRef()

  const[isModalVisable, setIsModalVisable] = useState(false);
  const[editRecord, setEditRecord] = useState();
  const[editContent, setEditContent] = useState();
  const[isSelectControllVisible, setIsSelectControllVisible] = useState(false)
  
  useEffect(() => {
    if(subtitlesData.length>0 && playVideoReq?.playType==3) {
      let curTime =  parseInt(curTimestamp/1000);
      let scrollToIndex = getSubtitlesNumber(curTime, 0 ,subtitlesData.length - 1); 

      let videoStartTime = 0;
      if(videoMaterial.videoType == 2 && videoMaterial.status==2) {
        videoStartTime = videoMaterial.startTime;
      }
      if(subtitlesList.current) {
        if(items.length<= scrollToIndex - 5 || curTime >= (endTs + videoStartTime) || curTime<= (startTs + videoStartTime)) {
            setStartTs(curTime - videoStartTime);
            setEndTs(curTime+600000 - videoStartTime);
        }
        else if (items.length > scrollToIndex && scrollToIndex >= 0 && scrollToIndex !== preScrollToIndex) {
          if(scrollToIndex >= 4)  {
            items[scrollToIndex-4].scrollIntoView();
          }
          let preIndex = preScrollToIndex;
          itemStyles[scrollToIndex] = styles.ProListPlayRow;
          itemStyles[preIndex] = subtitlesData[preScrollToIndex].isAdded ? styles.ProListAddedRow:styles.ProListRow;
          // setItemStyles(itemStyles);
          setCurSubtitlesId(subtitlesData[scrollToIndex].id);
          setPreScrollToIndex(scrollToIndex);
        }
      }
    }
  },[curTimestamp])

  const getSubtitlesNumber = (timestamp:number, startIndex:number, endIndex:number) => {
    if(timestamp < subtitlesData[startIndex].startTs || timestamp > subtitlesData[endIndex].endTs || startIndex > endIndex) {
      return -1
    }
    
    if(timestamp >= subtitlesData[startIndex].startTs && timestamp <= subtitlesData[startIndex].endTs) {
      return startIndex;
    }
    if(timestamp >= subtitlesData[endIndex].startTs && timestamp <= subtitlesData[endIndex].endTs) {
      return endIndex;
    }
    let middlleIndex = parseInt((startIndex + endIndex)/2);
    if(timestamp > subtitlesData[startIndex].endTs && timestamp <= subtitlesData[middlleIndex].endTs) {
      return getSubtitlesNumber(timestamp, startIndex + 1, middlleIndex)
    }else {
      return getSubtitlesNumber(timestamp, middlleIndex +1, endIndex - 1);
    }
  }

  const reloadData = () => {
    if(startTs!=null && endTs!=null) searchSubtitles({projectId: projectId, videoId: videoMaterial.id,
       startTs: startTs, endTs: endTs}).then((res:any) => {
      setSubtitlesData(res.result);
      let itemStyles = res.result?.map((data:any)=>{
        return data.isAdded ? styles.ProListAddedRow:styles.ProListRow;
      });
      setItemStyles(itemStyles);
      setPreScrollToIndex(0);
    })
  }

  useEffect(() => {
    reloadData();
    let tmpItems = subtitlesList.current.getElementsByClassName("ant-list-item");
    setItems(tmpItems);
  }, [startTs, endTs, subtitlesLoadFlag]);

  const onLocationChangeHandel = (startTs: any) => {
    setStartTs(startTs);
    setEndTs(parseInt(startTs) + 600000);
  }

  const onAddSubtitlesCutHandel = (id: any) => {
    addSubtitlesToProject({projectId: projectId, subtitlesId: id}).then((res: any) => {
      if(res.result) {
        reloadData();
        setSubtitlesCutLoadFlag(new Date());
      }
    })
  }

  const onAddAndMerge = () => {
    addSubtitlesToProject({projectId: projectId, subtitlesIds: selectedRowKeys}).then((res: any) => {
      if(res.result) {
        reloadData();
        setSelectedRowKeys([]);
        setSubtitlesCutLoadFlag(new Date());
      }
    })
  }

  const onRemoveSubtitlesCutHandle = (subId: any) => {
    removeSubtitlesCut({projectId: projectId, subtitlesId: subId}).then((res:any) => {
      if(res.result) {
        reloadData();
        setSubtitlesCutLoadFlag(new Date());
      }
    });
  }

  const handleGetSubtitlesInFront = () => {
    setStartTs(startTs - 60000);
    setEndTs(endTs - 60000);
  }

  const handleGetSubtitlesAfter = () => {
    setStartTs(startTs + 60000);
    setEndTs(endTs + 60000);
  }

  const changeEditRecordContent = (value) => {
    if(value.target.value != null) {
      let tmpEditRecord = editRecord;
      tmpEditRecord.content = value.target.value;
      setEditContent(value.target.value);
      setEditRecord(tmpEditRecord);
    }
  }

  const onHandleItemSelect = (record: any, index: number) => {
    setIsPlayVideo(true);
    setPlayVideoReq({projectId:projectId, subtitlesId: record.id, playType: 3, videoId: videoMaterial.id})
    itemStyles[index] = styles.ProListPlayRow;
    itemStyles[preScrollToIndex] = subtitlesData[preScrollToIndex].isAdded ? styles.ProListAddedRow:styles.ProListRow;
  }

  const onHandleControll = (isPlay:Boolean) => {
    setIsPlayVideo(isPlay);
  }

  const onHandleEditSubtitles = (record, index) => {
    setIsPlayVideo(false);
    setEditRecord(record);
    setEditContent(record.content);
    setIsModalVisable(true);
  }

  const handleCancelChangeSubtitles = () => {
    setEditRecord(null);
    setIsModalVisable(false);
  }

  const handleSubmitChangeSubtitles = () => {
    if(editRecord != null) {
      editSubtitles(editRecord).then((res) => {
        if(res.result) {
          message.success("更新成功！");
          // onLocationChangeHandel(editRecord.startTs);
          reloadData();
          setIsModalVisable(false);
        }else {
          message.error("更新失败!");
        }
      })
    }
  }


  const datasource = subtitlesData?.map((data, index) => ({...data, num: index,title:timestampToTime(data.startTs)+ '-' + timestampToTime(data.endTs)}))

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: Key[]) => setSelectedRowKeys(keys),
    getCheckboxProps: record => ({disabled: record.isAdded}),
  };

  return (
    <div>
      字幕：
      <SubtitlesLocationList videoId={videoMaterial.id} itemId={itemId} itemType={itemType} onLocationChange={onLocationChangeHandel}/>
     
      <div className={styles.Controlls}>
        <div className={styles.Controll}>
        <a onClick={handleGetSubtitlesInFront}>往前取字幕</a>
        </div><div className={styles.Controll}>
        <a onClick={handleGetSubtitlesAfter}>往后取字幕</a>
        </div><div className={styles.Controll}>
        <button onClick={() =>onHandleControll(true)}>播放</button>
        </div><div className={styles.Controll}>
        <button onClick={() =>onHandleControll(false)}>暂停</button>
      </div>
      </div>
      <div className={styles.SelectControlls} style={{display:selectedRowKeys.length>0?'flex':'none'}}>
        <div className={styles.SelectControll}>
        当前已选:({selectedRowKeys?selectedRowKeys.length:0})个
        </div><div className={styles.SelectControll}>
        <Button onClick={() => {setSelectedRowKeys([])}}>取消选择</Button>
        <Button onClick={onAddAndMerge}>添加并合并</Button>
        </div>
      </div>
      <div className= {styles.Subtiles} >
        <div ref={subtitlesList} className= {styles.SubtilesProList}>
          <ProList
            id='proListContent'
            className= {styles.ProListContent}
            rowKey={'id'}
            showActions="hover"
            rowSelection={rowSelection}
            dataSource={datasource}
            tableAlertRender={false}
            rowClassName= {(row:any) => (
              itemStyles[row.num]
            )}
            
            metas={{
              title: {
                dataIndex: 'title',
                render: (dom, text) => (
                  <div className={styles.Title}>{dom}</div>
                )
              },
              description: {
                dataIndex: 'content',
                render: (text) => (<span className={styles.Content}>{text}</span>)
              },
              actions: {
                cardActionProps:'extra',
                render: (text, row, index, action) => (
                  <div>
                  {!row.isAdded? <div className={styles.Action}
                      onClick={() => {
                        onAddSubtitlesCutHandel(row.id)
                      }}
                      >
                      添加
                      </div>
                      :
                      <div className={styles.Action}
                      onClick={() => {
                        onRemoveSubtitlesCutHandle(row.id)
                      }}
                      >
                      还原
                      </div>
                  }
                  <div className={styles.Action}
                    onClick={() => {
                      onHandleItemSelect(row, index)
                    }}
                    >
                    播放
                  </div>
                  <div className={styles.Action}
                    onClick={() => {
                      onHandleEditSubtitles(row, index)
                    }}
                    >
                    修改
                  </div></div>
                )
              },
            }}
          />  
        </div>
      </div>
      <Modal
      open={isModalVisable}
      title="字幕修改"
      width={860}
      onCancel={handleCancelChangeSubtitles}
      onOk={handleSubmitChangeSubtitles}
      >
      时间：
      <Input
        value={editRecord?.title}
        disabled={true}
      />
      字幕：<p style={{ color: 'red' ,whiteSpace: 'nowrap'}}>（请谨慎修改）</p>
      <Input
        type='text'
        onChange={changeEditRecordContent}
        value={editContent}
        disabled={false}
      />
    </Modal>
    </div>
  );
};

export default SubtitlesBrowseTabPane;