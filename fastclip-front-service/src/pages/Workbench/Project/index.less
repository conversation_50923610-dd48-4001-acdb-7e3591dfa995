.content {
  // overflow: hidden;
  .content_top{
    position: relative;
    background: rgb(240, 242, 240);
    margin-bottom: 10px;
    width: 100%;
    height: 60px;
    color: #252B3A;
    z-index: 999;
  }

  .content_main{
    position: relative;
    color: #252B3A;
    display: flex;
    width: 100%;
    height: 700px;
    z-index: 999;

    .content_left {
      position: relative;
      background: rgb(240, 242, 240);
      width: 30%;
      margin-right: 10px;
      color: #252B3A;
      flex: 1;
      z-index: 999;
    }

    .content_middle {
      background: rgb(240, 242, 240);
      width: 30%;
      margin-right: 10px;
      color: #252B3A;
      flex: 1;
    }
  
  
    .content_right {
      background: rgb(240, 242, 240);
      padding-bottom: 10px;
      width: 40%;
      flex: 2;
    }
  }
  
}
