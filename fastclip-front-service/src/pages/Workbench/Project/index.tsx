import { useEffect, useState} from 'react';
import SubtitlesBrowse from './SubtitlesBrowse';
import styles from './index.less';
import ProjectTop from './ProjectTop'
import { useLocation } from '@umijs/max';
import serivces from '@/services/workbench'
import VideoClipsCut from './VideoClipsCut';
import VideoPlay from '../components/VideoPlay';
import fs from 'fs';
import VideoClips from './VideoClips';

const Project: React.FC = (props: any) => {

  const [projectData, setProjectData] = useState({})
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const projectId = searchParams.get('projectId');

  const {getProjectList} = serivces.WorkbenchController

  useEffect(() => {
    getProjectList({id: parseInt(projectId, 0)}).then((res:any) => {
      setProjectData(res.result.data[0])
    })
  }, [projectId]);



  return (
      <div className={styles.content}>
        <div className={styles.content_top}>
          <ProjectTop {...projectData}></ProjectTop>
        </div>
        <div className={styles.content_main}>
          <div className={styles.content_left}>
            <SubtitlesBrowse projectId={projectId}></SubtitlesBrowse>
          </div>
          <div className={styles.content_middle}>
            <VideoClips projectId={projectId}></VideoClips>
          </div>
          <div className={styles.content_right}>
            <VideoPlay projectId={projectId}></VideoPlay>
          </div>
        </div>
      </div>
  );
};

export default Project;