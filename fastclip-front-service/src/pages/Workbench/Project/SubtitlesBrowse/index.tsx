import { ProList } from '@ant-design/pro-components';
import { Space, Tabs, TabsProps, Tag } from 'antd';
import { useEffect, useState} from 'react';
import styles from './index.less';
import ProjectTop from './ProjectTop'
import { useLocation } from '@umijs/max';
import { getProjectList } from '@/services/workbench/WorkbenchController';
import serivces from '@/services/workbench'
import SubtitlesBrowseTabPane from '../SubtitlesBrowseTabPane';


const SubtitlesBrowse: React.FC = (props: any) => {

  const {onTabChange} = props;

  const [projectData, setProjectData] = useState<API.ProjectVO>()
  const [videoMaterials, setVideoMaterials] = useState<API.VideoMaterialRecord[]>([])


  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const projectId = searchParams.get('projectId');

  const {getProjectList, getVideoMaterials} = serivces.WorkbenchController

  useEffect(() => {
    getProjectList({id: parseInt(projectId, 0)}).then((res:any) => {
      let p = (res.result.data[0]);
      setProjectData(p);
      getVideoMaterials({sellerId: p?.sellerId, itemId:p?.itemId, itemType:p?.itemType}).then((res:any) => {
        setVideoMaterials(res.result.data);
      })
    });
    
  }, [projectId]);

  const tabItems: TabsProps['items']  = [];
  if(videoMaterials != null && videoMaterials.length > 0) {
    videoMaterials.forEach((videoMaterial: API.VideoMaterialRecord): void => {
      videoMaterial.id && tabItems.push(
        {
          key: videoMaterial.id.toString(),
          label: "素材(" + videoMaterial.videoName + ")",
          children:<SubtitlesBrowseTabPane projectId={projectId} videoMaterial={videoMaterial} itemType={projectData?.itemType}
          itemId={projectData?.itemId}></SubtitlesBrowseTabPane>
        }
      );
    });
  }
  return (
      <Tabs className='SubtitlesBrowse' onChange={onTabChange} items={tabItems}>
      </Tabs>
  );
};

export default SubtitlesBrowse;