.ProListHeader {
  min-height: 45px; /* 设置一个固定的高度 */
  font-size: 12px;
  background: #e0dfdf;
  display: flex;
  flex-wrap: wrap;
}

.HeaderTop {
  min-height: 50px;
  font-size: 12px;
  background: #e0dfdf;
  margin-bottom: 2px;
  display: flex;
  background-color: #fed44a;
  flex-wrap: wrap;
}

.HeaderBottom {
  height: 30px; /* 设置一个固定的高度 */
  font-size: 12px;
  background: #e0dfdf;
  margin-bottom: 3px;
  display: flex;
  flex-wrap: wrap;
  justify-content: right; /* 水平居中 */
  align-items: right; /* 垂直居中 */
}

.ProList {
  height: 1000px; /* 设置一个固定的高度 */
  overflow-y: scroll; /* 添加垂直滚动条 */
  font-size: 12px;
  background: #e0dfdf;
}

.ProListRow {
  border-radius: 6px;
  background: #e0dfdf;
  border: 1px;
  margin-top: 2px;
}

.RowRight {
  text-align: center;
  justify-content: center;
  flex-wrap: wrap;
  background: #e0dfdf;
}

/* 为了避免在Webkit内核的浏览器中出现滚动条的时候产生双滚动条，可以添加以下样式 */
.ProList::-webkit-scrollbar {
  width: 12px; /* 滚动条宽度 */
}
 
.ProList::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道颜色 */
}
 
.ProList::-webkit-scrollbar-thumb {
  background: #888; /* 滚动条手柄颜色 */
}
 
.ProList::-webkit-scrollbar-thumb:hover {
  background: #555; /* 滚动条手柄hover颜色 */
}

.Title {
  font-size: 11px;
  width: 150px;
}

.Content {
  font-size: 12px;
  color: #000;
}

.Action{
  border: 1px solid rgba(15, 114, 239, 50%);
  padding: 1px;
  cursor: pointer;
  font-size: 8px;
  color: #000;
  border-radius: 2px;
  width: 40px;
  margin-right: 10px;
}

.Action:hover {
  background-color: #FFF; /* 鼠标悬停时的背景颜色 */
}

.CreateWorkAction{
  border: 1px solid rgba(15, 114, 239, 50%);
  padding: 1px;
  cursor: pointer;
  font-size: 10px;
  color: #000;
  border-radius: 4px;
  height: 20px;
  width: 65px;
  margin-right: 10px;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  align-items: center;
  background-color: #9cc0e1; /* 鼠标悬停时的背景颜色 */
}

.CreateWorkAction:hover {
  background-color: #FFF; /* 鼠标悬停时的背景颜色 */
}

.SelectAction:hover {
  background-color: #FFF; /* 鼠标悬停时的背景颜色 */
}

.Tag{
  margin-top: -5px;
}

.Mark{
  position: fixed;
  background: #000005;
}

.Label{
  background: #90b3eb;
  color: white;
}

.checkbox_container {
  /* 设置组件宽度，根据需要调整 */
  flex: 0 0 105px;
  font-size: 12px;
  height:20px;
} 
.TotalTime{
  margin-right: 20px;
}

.BatchCreateLine{
  display: flex;
}

.BatchCreateLineCell{
  display: flex;
  flex: 1;
}