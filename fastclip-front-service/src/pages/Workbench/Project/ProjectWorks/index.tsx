import { Key, DragSortTable, ProColumns } from '@ant-design/pro-components';
import { Button, Input, message, Modal, Space, Tag } from 'antd';
import { useEffect, useState, useContext, useRef} from 'react';
import styles from './index.less'
import { timestampToTime } from '@/utils/dateUtils';
import services from '@/services/workbench'
import ProjectContext from '../compoents/ProjectContext';
import { MenuOutlined, PlusOutlined } from '@ant-design/icons';
import VideoClipTagModalForm from '../../components/VideoClipTagModalForm';


const ProjectWorks = (props: any) => {

  const {projectId} =  props;
  const {setPlayVideoReq, setIsPlayVideo} = useContext(ProjectContext);
  const [cutData, setCutData] = useState<API.CutDataRecord>([]);
  const [worksData, setWorksData] = useState<Array<API.WorksRecordVO>>();
  const [videoClipsData, setVideoClipsData] =  useState<Array<API.VideoClipRecord>>([]);
  const [selectedWorksData, setSelectedWorksData] =  useState();
  const {createWorks, deleteVideoClip, getWorks} = services.WorkbenchController;
  const [videoClipTagVisable, setVideoClipTagVisable] = useState(false)
  const [selectedTagValue, setSelectedTagValue] = useState();
  const [datasource, setDatasource] = useState()
  const [isModalVisable, setIsModalVisable] = useState(false);
  const [worksNum, setWorksNum] = useState(2);
  const [speedUp, setSpeedUp] = useState(0);

  const onDeleteVideoClipHandle = (videoClipId: number) => {
    deleteVideoClip({projectId: projectId, videoClipId: videoClipId}).then((res: any) => {
    })
  }

  const labels = (tags:[]) => {
    if(tags!=null) {
      return tags.map((tag) => {
        return <><span className={styles.Label}>{tag.name}</span><span> </span></>
      })
    }
  }

  useEffect(() => {
   loaddata();
  }, [])

  const loaddata = ()=> {
    getWorks({projectIds: [projectId], pageNum: 1, pageSize: 100}).then((res:any) => 
      {
        if(res.result.data ) {
          setWorksData(res.result.data);
          if(res.result.data.length > 0) {
            setSelectedTagValue(res.result.data[0].id);
            setSelectedWorksData(res.result.data[0]);
            setVideoClipsData(res.result.data[0].details.map((detail:any)=> detail.videoClipDTO));
          }else{
            setSelectedTagValue(null);
            setSelectedWorksData(null);
            setVideoClipsData([])
          }
        }
      })
  }


  useEffect(() => {
    worksData?.forEach((data:any) => {
      if(data.id === selectedTagValue) {
        setSelectedWorksData(data);
        setVideoClipsData(data.details.map((detail:any)=> detail.videoClipDTO));
      }
    })
  }, [selectedTagValue])


  useEffect(() => {

    let datas = videoClipsData?.map((data) =>  {
        let title =  "时长:" + timestampToTime(data?.duration);
        let content = data?.subtitles
        return {...data, content: content, title: title}
      });
    setDatasource(datas);
  }, [videoClipsData])

  const columns: ProColumns[] = [
    {
      dataIndex: 'sort',
      className: 'drag-visible',
      colSize:1
    },
    {
      title: '',
      render: (dom, item, index) => {
        return (
          <div >
              <div className={styles.Title}>{item.title} {labels(item.tags)}</div>
              <div className={styles.Content} onMouseUp={(event) => handleSelect(event, item, index)}>{item.content}</div>
          </div>
        );
      },
      colSize:1000
    }, {
      title: '',
      render: (dom, item, index) => {
        return (
          <div className ={styles.RowRight}>
            <div>
              <div className={styles.Action}
                onClick={() => {
                }}
                >
                删除
              </div>
            </div>
          </div>

        );
      },
      colSize:8
    }
  ];

  const handleDragSortEnd = (
    beforeIndex: number,
    afterIndex: number,
    newDataSource: any,
  ) => {
    let tmp = newDataSource[beforeIndex].sort;
    newDataSource.map((data: any, index: number) => {
      if(afterIndex > beforeIndex) {
        index == afterIndex ? data.sort = tmp : null;
        index >= beforeIndex && index < afterIndex ? data.sort = data.sort - 1 : null;
      }else {
        index == afterIndex ? data.sort = tmp : null;
        index > afterIndex && index <= beforeIndex ? data.sort = data.sort + 1 : null;
      }
    })
    setVideoClipsData(newDataSource);
    // updateVideoClips({projectId: projectId, videoClipDTOs:newDataSource});
  };

  const handleCheckboxChange = (event:any) => {
    setSelectedTagValue(parseInt(event.target.id));
  };

  const handleCreateWorks = () => {
    createWorks({projectId: projectId, worksNum:1, speedUp: 0}).then((res:any) => 
      {
        if(res.result) {
          message.info("创建成功！")
          loaddata();
        }else{
          message.error("创建失败！" + res.resultDesc)
        }
      })
  }

  const handleBatchCreateWorks = () => {
    createWorks({projectId: projectId, worksNum:worksNum, speedUp: speedUp}).then((res:any) => 
      {
        if(res.result) {
          message.info("创建成功！")
          setIsModalVisable(false)
          loaddata();
        }else{
          message.error("创建失败！" + res.resultDesc)
        }
      })
  }

  const handleCancel = () => {
    setIsModalVisable(false);
  }

  return (
    <>
    <div className= {styles.ProListHeader}>
      <div className= {styles.HeaderTop} >
          {(worksData && worksData.length > 0)?worksData.map((option:any) => (
            <div key={option.tagCode} className={styles.checkbox_container}>
            <input
              type="checkbox"
              id={option.id}
              value={option.id}
              checked={selectedTagValue === option.id}
              onChange={handleCheckboxChange}
            />
            <label className="label">
              {'作品' + '(id:' + option.id + ')'}
            </label>
          </div>
          )):null}
      </div>
    </div>
    <div className= {styles.HeaderBottom}>
      {/* <div className={styles.CreateWorkAction}
          onClick={() => {
            setIsPlayVideo(true);
            setPlayVideoReq({projectId:projectId, worksId: selectedTagValue, playType: 2})
          }}
          >
            播放作品
        </div> */}
        {(worksData && worksData.length > 0)? <div className={styles.TotalTime}>
          作品时长：{selectedWorksData?.duration/1000}s
        </div>:null}
        <div className={styles.CreateWorkAction}
          onClick={() => {
            handleCreateWorks()
          }}
          >
            一键生成作品
        </div>  
        <div className={styles.CreateWorkAction}
          onClick={() => {
            setIsModalVisable(true)
          }}
          >
            批量生成作品
        </div> 
        <a onClick={loaddata}>刷新</a>
    </div>

      <DragSortTable
        headerTitle=""
        className= {styles.ProList}
        rowKey="id"
        dataSource={datasource}
        // rowSelection={rowSelection}
        rowClassName={styles.ProListRow}
        dragSortKey="sort"
        columns={columns}
        onDragSortEnd={handleDragSortEnd}
        search={false}
        showHeader={false}
        pagination={false}
        loading={false}
        options={{
          setting: false, // 隐藏设置按钮
          density: false, // 如果不需要密度选择，也可以一并关闭
          fullScreen: false, // 如果不需要全屏功能，同样可以关闭
          reload: false,
        }}
      />
      <Modal
      open={isModalVisable}
      title="批量生成作品"
      width={260}
      onOk={(record) => handleBatchCreateWorks()}
      onCancel={handleCancel}
    >
    <div className={styles.BatchCreateLine}>
      <div className={styles.BatchCreateLineCell}>
      作品数量： 
      </div>
      <div className={styles.BatchCreateLineCell}>
      <Input
        style={{width:"60px"}}
        type="number"
        step="1"
        max="20"
        min="1"
        defaultValue="2"
        onChange={(e:any) => setWorksNum(e.target.value)}
      />
      </div>
    </div>
    <div className={styles.BatchCreateLine}>
      <div className={styles.BatchCreateLineCell}>
      加速： 
      </div>
      <div className={styles.BatchCreateLineCell}>
      <Input
        style={{width:"60px"}}
        type="number"
        step="1"
        max="20"
        min="0"
        defaultValue="0"
        onChange={(e:any) => setSpeedUp(e.target.value)}
      /> %
      </div>
      </div>
    </Modal>
    </>
  );
};

export default ProjectWorks;