import services from '@/services/workbench';

import React, { useEffect, useState } from 'react';
import { useLocation } from '@umijs/max';
import styles from './index.less'
import { timestampToTime } from '@/utils/dateUtils';
import { Button, Input, Modal, TimePicker } from 'antd';
import moment from 'moment';

const SubtitlesLocationList = (props:any) => {

  const {videoId, itemId, onLocationChange, itemType} = props;

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const projectId = searchParams.get('projectId');
  const [videoMaterialClips, setVideoMaterialClips] = useState<Array<API.VideoMaterialClipRecord>>([])
  const [selectedValue, setSelectedValue] = useState('');
  const [searchTime, setSearchTime] = useState('');

  const {getVideoMaterialClips} = services.WorkbenchController

  useEffect(() => {
    getVideoMaterialClips({videoId: videoId, itemId: itemId, itemType: itemType}).then((res:any) => {
      if(res.result != null) {
        setVideoMaterialClips(res.result);
        setSelectedValue(res.result[0].id.toString());
        onLocationChange(res.result[0].startTs);
      }
    })
  }, [videoId]);

  const handleCheckboxChange = (event:any) => {
    setSelectedValue(event.target.id);
    onLocationChange(event.target.value)
  };

  const onHandleSearchButton = () => {
    onLocationChange(searchTime);
  }


  const onDateTimePickerChange =(value) => {
    if(value != null) {
      setSearchTime(value.$H*3600000 + value.$m*60000 + value.$s*1000);
    }
  }

  return (
    <div className={styles.checkbox_list}>
      {videoMaterialClips?.slice(0,15).map((option:API.VideoMaterialClipRecord) => (
        <div key={option.id} className={styles.checkbox_container}>
          <input
            type="checkbox"
            id={option.id?.toString()}
            value={option.startTs}
            checked={selectedValue === option.id?.toString()}
            onChange={handleCheckboxChange}
          />
          <label>
            {timestampToTime(option.startTs || 0)}
          </label>
        </div>
      ))}
      <div className={styles.search_container}>  
        <TimePicker className={styles.time_picker} onChange={onDateTimePickerChange} showNow={false}/>
        <Button className={styles.search_button} onClick={onHandleSearchButton}>搜索字幕</Button></div>
    </div>
  );
};
 
export default SubtitlesLocationList;