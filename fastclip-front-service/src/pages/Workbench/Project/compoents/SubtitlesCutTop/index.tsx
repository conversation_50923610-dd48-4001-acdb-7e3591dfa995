import services from '@/services/workbench';

import React, { useEffect, useState } from 'react';
import { useLocation } from '@umijs/max';
import styles from './index.less'
import { timestampToTime } from '@/utils/dateUtils';

const SubtitlesCutTop = (props:any) => {

  const {videoId, itemId, onLocationChange} = props;

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const projectId = searchParams.get('projectId');
  const [videoMaterialClips, setVideoMaterialClips] = useState<Array<API.VideoMaterialClipRecord>>([])
  const [selectedValue, setSelectedValue] = useState('');

  const {getVideoMaterialClips} = services.WorkbenchController

  useEffect(() => {
    getVideoMaterialClips({videoId: videoId, itemId: itemId}).then((res:any) => {
      setVideoMaterialClips(res.result);
      setSelectedValue(res.result[0].id.toString());
      onLocationChange(res.result[0].startTs);
    })
  }, [videoId]);

  const handleCheckboxChange = (event:any) => {
    setSelectedValue(event.target.id);
    onLocationChange(event.target.value)
  };

  return (
    <div className={styles.checkbox_list}>
      
    </div>
  );
};
 
export default SubtitlesCutTop;