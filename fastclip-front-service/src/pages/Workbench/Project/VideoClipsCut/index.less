.ProListHeader {
  height: 65px; /* 设置一个固定的高度 */
  font-size: 12px;
  background: #e0dfdf;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.HeaderTop {
  height: 45px; /* 设置一个固定的高度 */
  font-size: 12px;
  background: #e0dfdf;
  margin-bottom: 2px;
  display: flex;
  flex-wrap: wrap;
}

.HeaderBottom {
  height: 30px; /* 设置一个固定的高度 */
  font-size: 12px;
  background: #e0dfdf;
  margin-bottom: 3px;
  display: flex;
  flex-wrap: wrap;
  justify-content: right; 
  width: 100%;
  margin-bottom: 2px;
}

.ProList {
  height: 700px; /* 设置一个固定的高度 */
  overflow-y: scroll; /* 添加垂直滚动条 */
  font-size: 12px;
  background: #e0dfdf;
}

.ProListRow {
  border-radius: 6px;
  background: #e0dfdf;
  border: 1px;
  margin-top: 2px;
}

.RowMiddle {
}

.RowRight {
  text-align: center;
  justify-content: center;
  flex-wrap: wrap;
  background: #e0dfdf;
}

/* 为了避免在Webkit内核的浏览器中出现滚动条的时候产生双滚动条，可以添加以下样式 */
.ProList::-webkit-scrollbar {
  width: 12px; /* 滚动条宽度 */
}
 
.ProList::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道颜色 */
}
 
.ProList::-webkit-scrollbar-thumb {
  background: #888; /* 滚动条手柄颜色 */
}
 
.ProList::-webkit-scrollbar-thumb:hover {
  background: #555; /* 滚动条手柄hover颜色 */
}

.Title {
  font-size: 11px;
}

.Content {
  font-size: 12px;
  color: #000;
  width: 80%;
}

.Action{
  border: 1px solid rgba(15, 114, 239, 50%);
  padding: 1px;
  cursor: pointer;
  font-size: 8px;
  color: #000;
  border-radius: 2px;
  width: 40px;
  margin-right: 10px;
}

.Action:hover {
  background-color: #FFF; /* 鼠标悬停时的背景颜色 */
}

.TotalTime{
  margin-right: 20px;
}


.SelectAction{
  border: 1px solid rgba(15, 114, 239, 50%);
  padding: 1px;
  cursor: pointer;
  font-size: 10px;
  color: #000;
  border-radius: 4px;
  height: 20px;
  width: 35px;
  text-align: center;
  margin-right: 10px;
  background-color: #bdbbbb; /* 鼠标悬停时的背景颜色 */
}

.SelectAction:hover {
  background-color: #FFF; /* 鼠标悬停时的背景颜色 */
}

.Tag{
  margin-top: -5px;
}

.Mark{
  position: fixed;
  background: #000005;
}

.Label{
  background: #90b3eb;
  color: white;
}

.checkbox_container {
  /* 设置组件宽度，根据需要调整 */
  flex: 0 0 80px;
  font-size: 12px;
  height:20px;
} 