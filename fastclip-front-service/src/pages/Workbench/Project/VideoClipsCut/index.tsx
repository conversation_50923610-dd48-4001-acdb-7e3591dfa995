import { Key, DragSortTable, ProColumns } from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tag } from 'antd';
import { useEffect, useState, useContext, useRef} from 'react';
import styles from './index.less'
import { timestampToTime } from '@/utils/dateUtils';
import services from '@/services/workbench'
import ProjectContext from '../compoents/ProjectContext';
import { MenuOutlined, PlusOutlined } from '@ant-design/icons';
import VideoClipTagModalForm from '../../components/VideoClipTagModalForm';


const VideoClipsCut = (props: any) => {

  const {projectId} =  props;
  
  const {subtitlesCutLoadFlag, setSubtitlesLoadFlag, isHandleCutVideoClip, setIsHandleCutVideoClip, divStyle, setDivStyle} = useContext(ProjectContext);
  const [cutData, setCutData] = useState<API.CutDataRecord>();
  const [videoClipsData, setVideoClipsData] =  useState<Array<API.VideoClipRecord>>([]);
  const [tagCounts, setTagCounts] =  useState([]);
  const {getVideoClips, removeSubtitlesCut, mergeVideoClips, splitVideoClip, updateVideoClips, getWorksIdsOfSubtitlesClip} = services.WorkbenchController;
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [totalTime, setTotalTime] = useState(0)
  const [videoClipTagVisable, setVideoClipTagVisable] = useState(false)
  const [selectedTagValues, setSelectedTagValues] = useState([]);
  const [datasource, setDatasource] = useState([])
  const [worksIds, setWorksIds] = useState([])
  const [videoClipToDelete, setVideoClipToDelete] = useState(-1)
  const [videoClipsToDelete, setVideoClipsToDelete] = useState([])
  //删除或解除合并，1：删除，2：解除合并
  const [deleteOrSplit, setDeleteOrderSplit] = useState(0)
  const [isDeleteConfirmModalVisable, setIsDeleteConfirmModalVisable] = useState(false)

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: Key[]) => setSelectedRowKeys(keys),
  };

  const loadData = () => {
    getVideoClips({projectId: projectId}).then((res:any) => {
      if(res.result.videoClipList != null) {
        setVideoClipsData(res.result.videoClipList);
      }else{
        setVideoClipsData([]);
      }
      setTagCounts(res.result.tagCounts);
      setTotalTime(res.result.totalTime);
      let selectedTags = res.result.tagCounts?.map((tagCount:any) =>tagCount.tagCode);
      setSelectedTagValues(selectedTags);
    });
  }

  useEffect(() => {
    loadData();
  }, [subtitlesCutLoadFlag]);

  const confirmRemoveSubtitlesCut = (videoClipId: any) => {
    removeSubtitlesCut({projectId: projectId, videoClipId: videoClipId}).then((res:any) => {
      loadData();
      setSubtitlesLoadFlag(new Date());
    });
  }

  const confirmSplitVideoClip = (videoClipId: any) => {
    splitVideoClip({projectId: projectId, videoClipId: videoClipId}).then((res: any) => {
      loadData();
    })
  }

  const handleDeleteSubtitlesClip = (videoClipId:number) => {
    setVideoClipToDelete(videoClipId);
    setDeleteOrderSplit(1);
    getWorksIdsOfSubtitlesClip({videoClipId: videoClipId}).then((res) => {
      if(res.result && res.result.length > 0) {
        setWorksIds(res.result);
        setIsDeleteConfirmModalVisable(true);
      }else{
        confirmRemoveSubtitlesCut(videoClipId);
      }
    })
  }

  const onSplitVideoClipHandle = (videoClipId: number) => {
    setDeleteOrderSplit(2);
    setVideoClipToDelete(videoClipId);
    getWorksIdsOfSubtitlesClip({videoClipId: videoClipId}).then((res) => {
      if(res.result && res.result.length > 0) {
        setWorksIds(res.result);
        setIsDeleteConfirmModalVisable(true);
      }else{
        confirmSplitVideoClip(videoClipId);
      }
    })
  }

  const onConfirmClip = () => {
    if(deleteOrSplit == 1) {
      confirmRemoveSubtitlesCut(videoClipToDelete);
    }else if(deleteOrSplit == 2){
      confirmSplitVideoClip(videoClipToDelete);
    }else if(deleteOrSplit == 3) {
      confirmMerageVideoClips(videoClipsToDelete);
    }
    setIsDeleteConfirmModalVisable(false);
  }

  const confirmMerageVideoClips = (indexes: number[]) => {
    mergeVideoClips({projectId: projectId, videoClipIds: indexes}).then((res) =>{
      res?loadData():null;
    });
  }

  const onMerageVideoClipsHandel = (indexes: number[]) => {
    setDeleteOrderSplit(3);
    setVideoClipsToDelete(indexes);
    getWorksIdsOfSubtitlesClip({videoClipIds: indexes}).then((res) => {
      if(res.result && res.result.length > 0) {
        setWorksIds(res.result);
        setIsDeleteConfirmModalVisable(true);
      }else{
        confirmMerageVideoClips(indexes);
      }
    })
  }

  const handleSelect = (event:any, item:API.VideoClipRecord, index: number) => {
    const selection = window.getSelection() || document.getSelection();
    const selectedText = selection?.toString();
    // selectedText && setCutData({projectId: item.projectId, videoClipId:item.id, subtitlesCutId: item.startSubtitlesId, text: selectedText, startTs:0, endTs:0, duration:0});
    if(selectedText ) {
      if(cutData) {
        videoClipsData[cutData.index].subtitles = cutData.preContent;
      }
      const range = selection.getRangeAt(0);
      let startOffset = range.startOffset
      let endOffset = range.endOffset;
      const rect = range.getBoundingClientRect(); 
      setDivStyle({top:rect.top-15, left: rect.left, zIndex: 1000});
      setIsHandleCutVideoClip(true);
      let subtitles = videoClipsData[index].subtitles;
      videoClipsData[index].subtitles = <div style={{userSelect:'none'}}> {subtitles.slice(0, startOffset)}
        <span style={{ backgroundColor: 'gray', color: 'white' }}> 
        {subtitles.slice(startOffset, endOffset)}
        </span>
        {subtitles.slice(endOffset, subtitles.length)}
        </div>;
      setCutData({
        videoClipId: item.id,
        projectId: item.projectId,
        index: index,
        startOffset: startOffset,
        endOffset: endOffset,
        preContent: subtitles
      });
    }else {
      if(cutData) {
        videoClipsData[cutData.index].subtitles = cutData.preContent;
        setIsHandleCutVideoClip(false);
      }
    }
  };

  const labels = (tags:[]) => {
    if(tags!=null) {
      return tags.map((tag) => {
        return <><span className={styles.Label}>{tag.name}</span><span> </span></>
      })
    }
  }


  useEffect(() => {
    let filterData = videoClipsData?.filter((data) => {
      let tags = data.tags;
      let filterFlag = false;
      if(tags){
        tags.forEach((tag) => {
        if(selectedTagValues.includes(tag.code)) {
          filterFlag = true;
        }})
      }else{
        filterFlag = selectedTagValues.includes("emptyTag")
      }
      return filterFlag;
    })
    let datas = filterData?.map((data) =>  {
        let title =  "时长:" + timestampToTime(data.duration);
        let content = data.subtitles
        return {...data, content: content, title: title}
      });
    setDatasource(datas);
  }, [videoClipsData, selectedTagValues])


  const columns: ProColumns[] = [
    {
      dataIndex: 'sort',
      className: 'drag-visible',
      colSize:1
    },
    {
      title: '',
      render: (dom, item, index) => {
        return (
          <div className={styles.RowMiddle}>
              <div className={styles.Title}>{item.title} {labels(item.tags)}</div>
              <div className={styles.Content} onMouseUp={(event) => handleSelect(event, item, index)}>{item.content}</div>
          </div>
        );
      },
      colSize:10
    }, {
      title: '',
      render: (dom, item, index) => {
        return (
          <div className ={styles.RowRight}>
            <div>
              {index!=0 && item.videoId===videoClipsData[index-1].videoId?<div className={styles.Action}
                  onClick={() => {
                    onMerageVideoClipsHandel([datasource[index-1].id, datasource[index].id])
                  }}
                  >
                    向上合并
                </div>:null
                }
                {item.subtitlesCutCount>1?<div className={styles.Action}
                onClick={() => {
                  onSplitVideoClipHandle(item.id)
                }}
                >
                解除合并
                </div>:<div className={styles.Action}
                onClick={() => {
                  handleDeleteSubtitlesClip(item.id)
                }}
                >
                删除
                </div>}
                <div className={styles.Tag}>
                <VideoClipTagModalForm  videoClipId={item.id}  visible={videoClipTagVisable} onSubmitSuccess={loadData} tags={item.tags}></VideoClipTagModalForm>
                </div>
                {index!=(videoClipsData.length -1 )?<div className={styles.Action}
                onClick={() => {
                  onMerageVideoClipsHandel([datasource[index].id, datasource[index+1].id])
                }}
                >
                向下合并
                </div>: null
                }
              </div>
          </div>

        );
      },
      colSize:8
    }
  ];

  const handleDragSortEnd = (
    beforeIndex: number,
    afterIndex: number,
    newDataSource: any,
  ) => {
    let tmp = newDataSource[beforeIndex].sort;
    newDataSource.map((data: any, index: number) => {
      if(afterIndex > beforeIndex) {
        index == afterIndex ? data.sort = tmp : null;
        index >= beforeIndex && index < afterIndex ? data.sort = data.sort - 1 : null;
      }else {
        index == afterIndex ? data.sort = tmp : null;
        index > afterIndex && index <= beforeIndex ? data.sort = data.sort + 1 : null;
      }
    })
    setVideoClipsData(newDataSource);
    updateVideoClips({projectId: projectId, videoClipDTOs:newDataSource});
  };

  const dragHandleRender = (rowData: any, idx: any) => (
    <>
      <MenuOutlined style={{ cursor: 'grab', color: 'gold' }} />
      &nbsp;{idx + 1} - {rowData.name}
    </>
  ); 

  const handleCheckboxChange = (event:any) => {
    if(event.target.checked) {
      setSelectedTagValues([...selectedTagValues, event.target.id]);
    }else{
      setSelectedTagValues(selectedTagValues.filter((item) => item !== event.target.id));
    }
  };



  return (
    <>
      <div className= {styles.ProListHeader} >
        <div className= {styles.HeaderTop} >
          {tagCounts && tagCounts.map((option:any) => (
            <div key={option.tagCode} className={styles.checkbox_container}>
            <input
              type="checkbox"
              id={option.tagCode?.toString()}
              value={option.code}
              checked={selectedTagValues.includes(option.tagCode?.toString())}
              onChange={handleCheckboxChange}
            />
            <label className="label">
              {option.tagName+'(' + option.count + ')'}
            </label>
          </div>
          ))}
        </div>
        <div className={styles.HeaderBottom}>
          <div className={styles.TotalTime}>
                已选总时长：{totalTime/1000}s
            </div>
          <div className={styles.SelectAction}
              onClick={() => {
                setSelectedTagValues(tagCounts.map((tagCount) => tagCount.tagCode));
              }}
              >
                全选
            </div>
            <div className={styles.SelectAction}
              onClick={() => {
                setSelectedTagValues([])
              }}
              >
                全不选
            </div>
            <div className={styles.SelectAction}
              onClick={() => {
                loadData();
              }}
              >
                刷新
            </div>
          </div>
      </div>
      <DragSortTable
        headerTitle=""
        className= {styles.ProList}
        rowKey="id"
        dataSource={datasource}
        // rowSelection={rowSelection}
        rowClassName={styles.ProListRow}
        dragSortKey="sort"
        columns={columns}
        onDragSortEnd={handleDragSortEnd}
        search={false}
        showHeader={false}
        pagination={false}
        loading={false}
        options={{
          setting: false, // 隐藏设置按钮
          density: false, // 如果不需要密度选择，也可以一并关闭
          fullScreen: false, // 如果不需要全屏功能，同样可以关闭
          reload: false,
        }}
      />
      <Modal
        open={isDeleteConfirmModalVisable}
        title="关联作品"
        width={460}
        onOk={() => onConfirmClip()}
        onCancel={() => setIsDeleteConfirmModalVisable(false)}
        >
        <div>该片段已被如下作品使用，需和作品一起删除：  </div>
        <div>{worksIds?worksIds.map((item) => {return item + " "}):null}</div>
    </Modal>
    </>
  );
};

export default VideoClipsCut;