import { Space, Tabs, TabsProps, Tag } from 'antd';
import { useEffect, useState} from 'react';
import styles from './index.less';
import VideoClipsCut from '../VideoClipsCut';
import ProjectWorks from '../ProjectWorks';


const VideoClips: React.FC = (props: any) => {

  const {projectId} = props;



  const tabItems: TabsProps['items']  = [
    {
    key: "videoClip",
    label: "视频片段",
    children:<VideoClipsCut projectId={projectId}></VideoClipsCut>
    },
    {
      key: "worksList",
      label: "作品列表",
      children:<ProjectWorks projectId={projectId}></ProjectWorks>
    }
  ];
  
  return (
      <Tabs className={styles.tabs} items={tabItems}>
      </Tabs>
  );
};

export default VideoClips;