import { Input, Button, Modal } from 'antd';
import styles from './index.less';
import { useState } from 'react';
import services from '@/services/workbench'

const Project: React.FC = (props: any) => {

  const {sellerName, itemName, projectName, shareUrl} = props;
  const [openItemDetail, setOpenItemDetail] =  useState(false);
  const [openJustifyResult, setOpenJustifyResult] = useState(false);
  const [promotionResult, setPromotionResult] = useState('');
  const {justifyItemPromotion} = services.WorkbenchController;

  const onHandleJustifyPromotion = () => {
    justifyItemPromotion({url:shareUrl}).then((res) => {
      setOpenJustifyResult(true);
      if(res.result) {
        setPromotionResult("校验通过，该商品可以推广！");
      }else {
        setPromotionResult("校验失败，该商品不可以推广！");
      }
    })
  }

  return (
    <div className={styles.content}>
      <div className={styles.content_projectName}>
        项目名称：
        <input value={projectName} disabled={true} onMouseOver={(e) => (e.target.title = e.target.value)}></input>
      </div>
      <div className={styles.content_sellerName}>
        达人：
        <input value={sellerName} disabled={true} onMouseOver={(e) => (e.target.title = e.target.value)}></input>
      </div>
      <div className={styles.content_itemName}>
        商品：
        <input className={styles.input} value={itemName} disabled={true} onMouseOver={(e) => (e.target.title = e.target.value)}></input>
        <a onClick={ () => setOpenItemDetail(true)}>查看</a>
        <Button onClick={ () => onHandleJustifyPromotion()}>校验商品是否可以推广</Button>
      </div>
      {/* <div className={styles.content_button}>
        <Button className={styles.button}>发布</Button>
      </div> */}
      <Modal open={openItemDetail} onCancel={ () => setOpenItemDetail(false)}>
        <div>
          <iframe
            title="Embedded React App"
            src= {shareUrl}
            width="100%"
            height="500px"
            frameBorder="0"
            scrolling="no"
          ></iframe>
        </div>
      </Modal>
      <Modal open={openJustifyResult} onCancel={ () => setOpenJustifyResult(false)}>
        <div>
          {promotionResult}
        </div>
      </Modal>
    </div>
  );
};

export default Project;