import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, ProForm, ProFormDatePicker, ProFormSelect, ProFormText, ProFormUploadButton, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, message, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import services from '@/services/workbench';
import WorksDetailModal from '../components/WorksDetailModal';
import ProjectContext from '../Project/compoents/ProjectContext';

const WorksList: React.FC = () => {

  const columns: ProColumns<API.WorksRecordVO>[] = [
    {
      title: '作品id',
      dataIndex: 'id',
      width: 50,
      search: false
    },
    {
      title: '项目Id',
      dataIndex: 'projectId',
      width: 50,
      filters: true,
      onFilter: true,
      valueType: 'select'
    },
    {
      title: '达人名称',
      dataIndex: 'sellerName',
      width: 100    
    },
    {
      disable: true,
      title: '账号',
      dataIndex: 'phone',
      search: false,
      width: 100  
    },
    // {
    //   disable: true,
    //   title: '是否已合成视频',
    //   dataIndex: 'isComposed',
    //   filters: true,
    //   onFilter: true,
    //   ellipsis: true,
    //   width: 50,
    //   valueType: 'select',
    //   valueEnum: {
    //     true: {
    //       text: '已合成',
    //     },
    //     false: {
    //       text: '未合成',
    //     },
    //   },
    // },
    {
      disable: true,
      title: '是否已发布',
      dataIndex: 'isPublished',
      filters: true,
      onFilter: true,
      ellipsis: true,
      width: 50,
      valueType: 'select',
      valueEnum: {
        true: {
          text: '已发布',
        },
        false: {
          text: '未发布',
        },
      },
    },
    {
      disable: true,
      title: '合成状态',
      dataIndex: 'composeStatus',
      filters: true,
      onFilter: true,
      ellipsis: true,
      width: 50,
      valueType: 'select',
      valueEnum: {
        0: {
          text: '待合成',
        },
        1: {
          text: '合成中',
        },
        2: {
          text: '已完成',
        },
      },
    },
    {
      title: '创建人',
      key: 'creator',
      dataIndex: 'userName',
      sorter: true,
      width: 70,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      key: 'showTime',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      sorter: true,
      width: 130,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      key: 'showTime',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      sorter: true,
      width: 130,
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 250,
      key: 'option',
      valueType: 'option',
      render: (text, record) => [
        record.composeStatus==2 ? <a key='combine' onClick={ () => {handelCombineSubmit(record)}}>重新合成</a>:
        null,
        <a key='desc' onClick={ () => {handelWorksDescSubmit(record)}}>生成文案</a>,
        !record.isPublished ? <a key='publish' onClick={ () => {handelPublishSubmit(record)}}>发布</a>:
        <a key='publish' onClick={ () => {handelPublishSubmit(record)}}>重新发布</a>,
        // <a key='play' onClick={ () => {handelPlaySubmit(record)}}>播放</a>,
        <a key='delete' onClick={ () => {handelDeleteSubmit(record)}}>删除</a>,
        <a key='detail' onClick={ () => {handelWorksDetail(record)}}>查看</a>,   
        record.composeStatus==2 ? <TableDropdown
        key="actionGroup"
        onSelect={(key) => handleMore(key, record)}
        menus={record.worksCover == '' ? [
          { key: 'handelDownloadSubmit', name: '下载视频' },
          { key: 'handelDownloadWithOutAssSubmit', name: '下载无字幕视频' }]:
          [
            { key: 'handelDownloadSubmit', name: '下载视频' },
            { key: 'handelDownloadWithOutAssSubmit', name: '下载无字幕视频' },
            { key: 'handelDownloadCoverSubmit', name: '下载封面' },
          ]}
        >下载</TableDropdown>:null, 
      ],
    },
  ];

  const actionRef = useRef<ActionType>();
  const currentUrl = window.location.href;

  const {getWorks, createWorks, deleteWorks, combineWorks, publishWork, createWorksDesc, downloadWorks, downloadWorksWithOutAss, downloadWorksCover} = services.WorkbenchController
  const [worksDetailVisable, setWorksDetailVisable] = useState(false)
  const [worksDetailId, setWorksDetailId] = useState()

  const refreshTable = () => {
    actionRef.current?.reload();
  }

  const handelDeleteSubmit = (record) => {
    deleteWorks({worksIds: [record.id]}).then((res) => {
      if(res.result) {
        message.success("删除成功！");
        refreshTable();
      }else {
        message.error("删除失败!");
      }
    })
  }

  const handleMore = (key, record) => {
    if(key === 'handelDownloadSubmit') {
      handelDownloadSubmit(record);
    }else if(key === 'handelDownloadWithOutAssSubmit') {
      handelDownloadWithOutAssSubmit(record);
    }else if(key === 'handelDownloadCoverSubmit') {
      handelDownloadCoverSubmit(record);
    }
  }

  const handelCombineSubmit = (record) => {
    combineWorks({worksId: record.id}).then((res) => {
      if(res.result) {
        message.success("合成成功！");
        refreshTable();
      }else {
        message.error("合成失败!");
      }
    })
  }

  const handelWorksDescSubmit = (record) => {
    createWorksDesc({worksId: record.id}).then((res) => {
      if(res.result) {
        message.success("生成文案成功！");
        refreshTable();
      }else {
        message.error("生成文案失败!");
      }
    })
  }

  const handelDownloadWithOutAssSubmit = async (record) => {
        let url = record.downloadWorksWithoutAssUrl;
        if(currentUrl.startsWith("http://192.168.3")) {
          url = record.lcoalDownloadWorksWithoutAssUrl;
        }
        const res = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // 如果后端使用了 cookie 认证
        });
        if(res.ok) {
          const blob = await res.blob();

          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          // const url = window.URL.createObjectURL(new Blob([res],{type: 'application/octet-stream'}));
          const link = document.createElement('a');


          link.href = url;
          // 从header中获取服务端命名的文件名
          const headers = res.headers;
          const fileName = record.id + "_" + record.projectId + "_withnoass.mp4";
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }else{
          message.error("下载视频失败，请重新合成视频！");
        }
  }

  const handelDownloadSubmit = async (record) => {
      let url = record.downloadWorksUrl;
      if(currentUrl.startsWith("http://192.168.3")) {
        url = record.localDownloadWorksUrl;
      }
      const res = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // 如果后端使用了 cookie 认证
      });
      if(res.ok) {
        const blob = await res.blob();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        // const url = window.URL.createObjectURL(new Blob([res],{type: 'application/octet-stream'}));
        const link = document.createElement('a');


        link.href = url;
        // 从header中获取服务端命名的文件名
        const headers = res.headers;
        const fileName = record.id + "_" + record.projectId + ".mp4";
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }else{
        message.error("下载视频失败，请重新合成视频！");
      }
    
  }



  const handelDownloadCoverSubmit = async (record) => {
      let url = record.downloadCoverUrl;
      if(currentUrl.startsWith("http://192.168.3")) {
        url = record.localDownloadCoverUrl;
      }
      const res = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // 如果后端使用了 cookie 认证
      });
      if(res.ok) {
        const blob = await res.blob();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        // const url = window.URL.createObjectURL(new Blob([res],{type: 'application/octet-stream'}));
        const link = document.createElement('a');


        link.href = url;
        // 从header中获取服务端命名的文件名
        const headers = res.headers;
        const fileName = record.id + "_" + record.projectId + "_cover.mp4";
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }else{
        message.error("下载封面失败，请重新合成视频！");
      }
  }

  const handelPublishSubmit = (record) => {
    publishWork({id: record.id}).then((res) => {
      if(res.result) {
        message.success("发布成功！");
        refreshTable();
      }else {
        message.error("发布失败!");
      }
    })
  }

  const handelWorksDetail = (record) => {
    setWorksDetailId(record.id);
    setWorksDetailVisable(true);
  }

  const context = { 
    worksDetailVisable, setWorksDetailVisable
  }

  return (
    <>
    <ProTable<API.ProjectVO>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort, filter) => {
        return (await 
          getWorks({...params, pageNum: params.current}).then((res:any) => (
            {
            data: res.result.data.map((item) => ({...item, sellerName:item.seller?item.seller.sellerName:"无", userName:item.creator.userName})),
            total: res.result.total,
            sucess: res.resultCode
          }
        )));
      }}
      editable={{
        type: 'multiple',
      }}
      // columnsState={{
      //   persistenceKey: 'pro-table-singe-demos',
      //   persistenceType: 'localStorage',
      //   defaultValue: {
      //     option: { fixed: 'right', disable: true },
      //   },
      //   onChange(value) {
      //     console.log('value: ', value);
      //   },
      // }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      form={{
        // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              created_at: [values.startTime, values.endTime],
            };
          }
          return values;
        },
      }}
      pagination={{
        pageSize: 15,
        onChange: (page) => console.log(page),
      }}
      dateFormatter="string"
      headerTitle="作品列表"
    />
    <WorksDetailModal modalVisable={worksDetailVisable} onVisiableChange={setWorksDetailVisable} worksId={worksDetailId}/>
    </>
    
  );
}
export default WorksList;
