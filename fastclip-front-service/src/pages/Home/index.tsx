import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-components';
import { EditableProTable, ModalForm, ProForm, ProFormSelect, ProFormText, ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Form, Input, message, Modal, Space, Tag } from 'antd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import styles from './index.less'
import services from  '../../services/workbench';
import ProjectContext from '../Workbench/Project/compoents/ProjectContext';

const DouyinAccountList: React.FC = () => {

  const editableFormRef = useRef<EditableFormInstance>();
  const columns: ProColumns<API.SellerVO>[] = [
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 180,
      search: false
    },
    {
      title: '抖音名称',
      dataIndex: 'douyinName',
      width: 300,
      search: false
    },
    {
      title: '达人名称',
      dataIndex: 'sellerId',
      search: false,
      render: (_, record) => (
        <div>{record.sellerName}</div>
      ),
      width: 300
    },
    {
      title: '今日完成作品',
      width: 80,
      key: 'option',
      valueType: 'option',
      render: (text, record, _,action) => [
        <a
          onClick={() => {
          }}
        >
          {record.worksCountToday}
        </a>,
      ],
    },
    {
      title: '今日发布作品',
      width: 80,
      key: 'option',
      valueType: 'option',
      render: (text, record, _,action) => [
        <a
          onClick={() => {
          }}
        >
          {record.publishedWorksCountToday}
        </a>,
      ],
    },
    {
      title: '待发布作品',
      width: 80,
      key: 'option',
      valueType: 'option',
      render: (text, record, _,action) => [
        <a
          onClick={() => {
          }}
        >
          {record.unPublishedWorks}
        </a>,
      ],
    },
  ];

  const actionRef = useRef<ActionType>();

  const {getHomeData} = services.WorkbenchController
  const [homeData, setHomeData] = useState(null);
  const [accountData, setAccountData] = useState(null);

  const {user} = useContext(ProjectContext);


  useEffect(() => {
    getHomeData().then((res:any) => {
      let accountData = res.result.douyinAccountDTOS?.map((item:any) => ({...item, sellerName:item.sellerDTO?item.sellerDTO.sellerName:"无"}))
      setAccountData(accountData)
      setHomeData(res.result)
    })
  },[])

  return (
      <>
      {user?.isAdmin?<div className={styles.HomeHeaderOrders}>
        <div className={styles.Top}>今年订单数量：{homeData?.ordersCountThisYear}</div>
        <div className={styles.Top}>今年订单金额：¥{homeData?.totalPayAmountThisYear/100}</div>
      </div>:null
      } 
      {user?.isAdmin?<div className={styles.HomeHeaderOrders}>
        <div className={styles.Top}>本月订单数量：{homeData?.ordersCountThisMonth}</div>
        <div className={styles.Top}>本月订单金额：¥{homeData?.totalPayAmountThisMonth/100}</div>
      </div>:null
      } 
      {user?.isAdmin?<div className={styles.HomeHeaderOrders}>
        <div className={styles.Top}>今天订单数量：{homeData?.ordersCountToday}</div>
        <div className={styles.Top}>今天订单金额：¥{homeData?.totalPayAmountToday/100}</div>
      </div>:null
      } 
      <div className={styles.HomeHeader}>
        <div className={styles.Top}>今日完成作品数量：{homeData?.worksCountToday}</div>
        <div className={styles.Top}>今日发布作品数量：{homeData?.publishedWorksCountToday}</div>
        <div className={styles.Top}>待发布作品数量：{homeData?.unPublishedWorks}</div>
      </div>
      <div className={styles.HomeMiddle}>
        <div className={styles.Top}>本月发布作品数量：{homeData?.publishedWorksCountThisMonth}</div>
        <div className={styles.Top}>上月发布作品数量：{homeData?.publishedWorksCountLastMonth}</div>
        <div className={styles.Top}>所有发布作品数量：{homeData?.publishedWorksCountTotal}</div>
      </div>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        dataSource={accountData}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
          onChange(value) {
            console.log('value: ', value);
          },
        }}
        rowKey="id"
        search={false}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        form={{
          // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
          onChange: (page) => console.log(page),
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Dropdown
            key="menu"
            menu={{
              items: [
                {
                  label: '1st item',
                  key: '1',
                },
                {
                  label: '2nd item',
                  key: '2',
                },
                {
                  label: '3rd item',
                  key: '3',
                },
              ],
            }}
          >
            <Button>
              <EllipsisOutlined />
            </Button>
          </Dropdown>,
        ]}
      />
    </>
  );
}
export default DouyinAccountList;
