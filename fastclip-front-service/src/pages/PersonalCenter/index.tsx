import React from 'react';

interface MyComponentProps<T> {
  data: T;
}

const MyComponent = <T,>(props: MyComponentProps<T>) => {
    return (
      <div>
        {JSON.stringify(props.data)}
      </div>
    );
  };

  const PersonalCenter = () => {
    const myData = {name: '<PERSON>',age: 30};
    return (
      <div>
        sdfsdfdssdfsdf
        <MyComponent data={myData} />
      </div>
    );
  };

  export default PersonalCenter;