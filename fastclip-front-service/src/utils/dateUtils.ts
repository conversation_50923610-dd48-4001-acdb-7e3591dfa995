export function timestampToTime (timestamp: number) {
    let hours = Math.floor(timestamp/3600000);
    let minutes = Math.floor((timestamp - hours * 3600000)/60000);
    let seconds = Math.floor((timestamp - hours * 3600000 - 60000*minutes)/1000);
    let hoursStr = (hours < 10 )? '0' + hours: hours;
    let minutesStr = (minutes < 10 )? '0' + minutes: minutes;
    let secondsStr = (seconds < 10 )? '0' + seconds: seconds;

    return hoursStr + ":" + minutesStr + ":" + secondsStr;
  }