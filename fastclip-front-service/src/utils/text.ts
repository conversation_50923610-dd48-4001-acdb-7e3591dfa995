/**
 * 文本处理工具类
 */

// 将文字复制到剪贴板
export function copyToClipboard(text: string) {
    return new Promise(async (resolve, reject) => {
        if (navigator.clipboard) {
            try {
                await navigator.clipboard.writeText(text)
                resolve(text)
            } catch (err) {
                // 无操作
            }
        }

        if (typeof document.execCommand === 'function') {
            try {
                const input = document.createElement('textarea')
                input.setAttribute('readonly', 'readonly')
                input.value = text
                document.body.appendChild(input)
                input.select()
                if (document.execCommand('copy')) {
                    document.execCommand('copy')
                }
                document.body.removeChild(input)
                resolve(text)
            } catch (error) {
                reject(error)
            }
        } else {
            reject(new Error(undefined))
        }
    })
}

export function joinTrim(arr: (string | number | undefined)[]): string {
    return arr.filter(x => x !== 'undefined').join(' ').trim();
}

// 拼接url的query参数
export function urlApendParams(url: string, params?: object) {
    if (!url.startsWith('http')) url = location.origin + url;
    const u = new URL(url);
    if (params) {
        const p = new URLSearchParams();
        if (params) {
            for (const [key, value] of Object.entries(params)) {
                p.append(key, value);
            }
        }
        u.search = p.toString();
    }
    return u.href
}

