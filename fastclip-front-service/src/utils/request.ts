/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { extend } from 'umi-request';
import { notification, message } from 'antd';
import { urlApendParams } from './text';

const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/**
 * 异常处理程序
 */
const errorHandler = (error: { response: Response }): Response => {
  const { response, request } = error;
  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  } else if (!response) {
    if (request) {
      const { url } = request;
      // 目录树查询接口取消接口查页面不展示报错信息
      if (url.indexOf('listAssert/v2') !== -1) {
        request['code'] = 'cancelRequest';
        return request
      }
    }
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
  }
  return response;
};

/**
 * 配置request请求时的默认参数
 */
const request = extend({
  errorHandler, // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
});

// request拦截器,改变url或options.
request.interceptors.request.use((url: any, options: any) => {
  const token = localStorage.getItem('token');
  const { headers } = options;
  if (token) {
    headers.token = token;
  }
  return {
    url,
    options: { ...options, headers },
  };
});

// request.interceptors.response.use((response) => {
//   const authorization = response.headers.get('authorization');
//   if (authorization) {
//     sessionStorage.authorization = authorization;
//   }
//   return response;
// });

request.interceptors.response.use( (response: any) => {

  const data = response.clone();
  const { url } = response;
  if (data && !data.status) {
    return response
  }
  if (data && data.status && window.location.href.indexOf('isUpgrading') === -1) {

    //   history.push({
    //     pathname: '/homePage',
    //     query: {
    //        isUpgrading:true
    //     },
    // });
  }
  if (data && data?.status === 200) {
    if (window.location.href.indexOf('isGrading=true') !== -1) {
      window.location.href = '/home'
    }
  }
  if (data && data.status !== 200) {
    if (data?.code === 666) {
      if (window.location.href.indexOf('isGrading=true') === -1) {
        window.location.href = '/home'
      }
    }
    else if (data.status === 401) {
      sessionStorage.clear();
      window.location.href = `${window.location.origin}/login`;
    } else {
      // 设置不弹出报错信息的接口
      if (url.indexOf('predict') === -1 && url.indexOf('exportPredictExcels') === -1 &&
        url.indexOf('queryTableField') === -1 && url.indexOf('getLabelValue') === -1 &&
        url.indexOf('queryTextStandard') === -1 && url.indexOf('queryTextLabel') === -1 &&
        url.indexOf('intelligence') === -1 && url.indexOf('queryTableDetail') === -1 &&
        url.indexOf('/auth/accCookie') === -1) {
        message.error(data.msg);
      }
    }
    return response;
  }
  return response;
});

interface xmlHttpRequestType {
  method: 'GET' | 'POST',
  params?: Record<string, string>, // url中的query参数，{key1: value1, key2: value2......}
  data?: Record<string, any>, // 请求报文，{key1: value1, key2: value2......}
  headers?: Record<string, string>, // 请求头，{key1: value1, key2: value2......}
  async?: boolean // 是否异步请求，暂仅支持false
}

// 原生XMLHttpRequest请求，目前用于一些需使用同步请求的场景，暂未对异步请求做处理
const xmlHttpRequest = (url: string, config: xmlHttpRequestType) => {
  const { method, params, data, headers, async = false } = config
  const xhr = new XMLHttpRequest();
  // 拼接query参数到url后
  const spliceUrl = urlApendParams(url, params)
  xhr.open(method, spliceUrl, async)
  // 请求头添加认证token
  const token = localStorage.getItem('token')
  xhr.setRequestHeader('token', `${token}`)
  // 添加自定义请求头
  if (headers) {
    for (const [key, value] of Object.entries(headers)) {
      xhr.setRequestHeader(key, value)
    }
  }
  xhr.setRequestHeader('content-Type', 'application/json;charset=UTF-8')
  xhr.send(JSON.stringify(data));
  const responseHandle = () => {
    if (xhr.status === 200) {
      const responseJson = JSON.parse(xhr.responseText || '{}')
      // 登录失效跳转统一登陆
      if (responseJson.code === 401) {
        sessionStorage.clear();
        window.location.href = `${window.location.origin}/login`;
      }
      return responseJson;
    } else {
      notification.error({
        message: `请求错误 ${xhr.status}: ${spliceUrl}`,
        description: '网络异常',
      });
      return '{}';
    }
  }
  // if (async) {
  //   xhr.onload = () => {
  //     return responseHandle()
  //   }
  // } else {
  return responseHandle()
  // }
}

export default request;

export {
  request,
  xmlHttpRequest
}
