/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable array-callback-return */
/**
 * Ant Design Pro v4 use `@ant-design/pro-layout` to handle Layout.
 * You can view component api by:
 * https://github.com/ant-design/ant-design-pro-layout
 */


import ProLayout from '@ant-design/pro-layout';
import { Outlet } from 'umi';
import React, { useCallback, useEffect, useState } from 'react';
import { history } from '@umijs/max';
import { useLocation } from 'react-router-dom';
import DeleteIcon from '@/assets/cut.jpg';
import ManIcon from '@/assets/man.png';
import ProjectContext from '@/pages/Workbench/Project/compoents/ProjectContext';
import Styles from './BasicLayout.less'
import { Dropdown, message } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import services from  '../services/workbench';

const BasicLayout: React.FC = (props: any) => {

  const location = useLocation();
  const pathname = location.pathname;

  const [subtitlesLoadFlag, setSubtitlesLoadFlag] = useState(null)
  const [subtitlesCutLoadFlag, setSubtitlesCutLoadFlag] = useState(null)
  const [isHandleCutVideoClip, setIsHandleCutVideoClip] = useState(false);
  const [divStyle, setDivStyle] = useState<API.divPosition>({top:0,left:0});
  const [playVideoReq, setPlayVideoReq] = useState<API.playVideoReq>()
  const [isPlayVideo, setIsPlayVideo] = useState(false)
  const [curTimestamp, setCurTimestamp] = useState(0)
  const [curSubtitlesId, setCurSubtitlesId] = useState()
  const [user, setUser] = useState();
  const {currentUser, logout} = services.WorkbenchController

  const context = { 
    subtitlesLoadFlag, 
    setSubtitlesLoadFlag, 
    subtitlesCutLoadFlag, 
    setSubtitlesCutLoadFlag,
    isHandleCutVideoClip,
    setIsHandleCutVideoClip,
    divStyle,
    setDivStyle,
    playVideoReq,
    setPlayVideoReq,
    isPlayVideo,
    setIsPlayVideo,
    curTimestamp,
    setCurTimestamp,
    curSubtitlesId,
    setCurSubtitlesId,
    user,
    setUser
  }
  
  const routes = {
      path: '/',
      routes: [
        {
          path: '/home',
          name: '首页',
        },
        {
          path: '/workbench',
          name: '工作台',
          routes: [
            {
              path: '/workbench/projectList',
              name: '项目列表',
            },
            {
              path: '/workbench/project',
              name: '项目详情',
              hideInMenu: true,
            },
            {
              path: '/workbench/worksList',
              name: '作品管理',
            }
          ]
        },
        {
          path: '/personalCenter',
          name: '个人中心',
        },
        user?.isAdmin?{
          path: '/management',
          name: '后台管理中心',
          routes: [
            {
              path: '/management/videoMaterialList',
              name: '素材管理',
            },{
              path: '/management/sellerList',
              name: '达人管理',
            },{
              path: '/management/itemList',
              name: '商品管理',
            },
            {
              path: '/management/douyinAccountList',
              name: '抖音账号管理',
            },
            {
              path: '/management/orderList',
              name: '订单管理',
            },
            {
              path: '/management/teamList',
              name: '团队管理',
            }
          ]
        }:{}
      ],
  }

  useEffect(() => {
    currentUser().then((res:any) => {
      setUser(res.result)
    })
  },[])

  const handleLogout = () => {
    logout().then((res:any) => {
      message.info("退出登录成功");
      setTimeout(() => {
        window.location.href = '/login'; // 替换为你想要跳转的URL
      }, 1000);
    } )
  }

  return (
    <ProjectContext.Provider value={context}>      
      {isHandleCutVideoClip && <div style={{position: 'absolute', top:divStyle.top, left: divStyle.left, zIndex: 1000}}>
        <img src={DeleteIcon} width={12} height={14} />
      </div>}
      <ProLayout
          title = {'万相智能切片'}
          route={routes}
          location={{
              pathname
            }}
          className= {Styles.ProLayout}
          onMenuHeaderClick={() => {history.push('/home');}}
          menuItemRender={(item, dom) => (
              <a
                onClick={() => {
                  history.push(item.path);
                }}
              >
                {dom}
              </a>
            )}
            avatarProps={{
              src: ManIcon,
              size: 'small',
              title: user?.userName,
              render: (props, dom) => {
                return (
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'logout',
                          icon: <LogoutOutlined />,
                          label: '退出登录',
                          onClick: () => handleLogout()
                        },
                      ],
                    }}
                  >
                    {dom}
                  </Dropdown>
                );
              },
            }}
          breadcrumbRender={(routers = []) => [
            {
              path: '/',
              title: '首页',
            },
            ...routers,
          ]}
      >
  
          <Outlet />
      </ProLayout>
    </ProjectContext.Provider>
  );
}
export default BasicLayout;
 