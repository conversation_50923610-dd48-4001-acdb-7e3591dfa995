import type { FC } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import { Input } from 'antd';
import headerLogo from '@/assets/home/<USER>'
import serachImge from '@/assets/home/<USER>'
import userLogo from '@/assets/home/<USER>'
import { history } from 'umi';
type Header = {
    dispatch: any;
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
const SubtitlesList: FC<Header> = (props) => {
    const { dispatch } = props;
    const [subtitlesData, setSubtitlesData] = useState([]);

    useEffect(() => {
        
    }, [dispatch]);

    return (
        <div className={styles.SubtitlesList}>
            <img className="logo" src={headerLogo} alt="" />
        </div>
    );
};

export default SubtitlesList
