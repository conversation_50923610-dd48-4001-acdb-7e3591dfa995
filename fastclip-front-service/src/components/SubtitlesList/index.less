.SubtitlesList {
    position: fixed;
    height: 100rem;
    top: 0;
    left: 0;
    background: transparent;
    width: 100%;
    display: flex;
    z-index: 1;
    line-height: 3rem;
    align-items: center;
    padding-left: 2.6rem;

    :global {
        .logo {
            width: 10.5rem;
            height: 2.5rem;
        }

        .nav-list {
            flex:1;
            display: flex;
            margin-left: 3rem;

            .nav-item {
                margin-right: 3rem;
                font-size: 1rem;
                font-family: "PingFang SC";
                font-weight: 400;
                color: #FFF;
                margin-top: 10px;
                cursor: pointer;
            }

            .nav-item:nth-child(1) {
                border-bottom: 3px solid #FFF;
            }
        }

        input {
            border-color: transparent;
            border: 0 !important;
            background: #3265B7;
            color: #FFF;
            padding-right: 50px;
        }

        input:focus {
            outline: none;
        }

        .nav-right {
            position: absolute;
            right: 0;

            .serach {
                position: relative;
                right: 1.4rem;
                top: -2px;
                cursor: pointer;
            }

            span {
                font-size: 14px;
                font-family: "PingFang SC";
                font-weight: 400;
                color: #FFF;
                margin-left: 2rem;
            }

            .user-info {
                display: inline-block;
                margin-left: 3rem;

                span {
                    margin-left: 5px;
                    margin-right: 4.3rem;
                }
            }
        }
    }
}